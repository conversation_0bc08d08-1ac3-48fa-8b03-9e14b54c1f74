import React, { Component } from 'react';
import { <PERSON>rror<PERSON><PERSON><PERSON> } from '@sworksio/dashboard-core';

export class ErrorBoundary extends Component {
  constructor(props) {
    super(props);

    this.state = {
      hasError: false,
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, info) {
    console.log(error);
    console.log(info);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div>
          <ErrorHandler
            errorCode={`500`}
            errorMsg={`Something went wrong! Please try after sometime!`}
            showTryAgainButton={true}
            occupyFullScreen={true}
          />
        </div>
      );
    }
    return this.props.children;
  }
}

export default ErrorBoundary;
