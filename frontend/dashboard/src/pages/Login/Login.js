// NPM modules
import React, { useEffect, useState } from 'react';
import { Col, Image, Navbar, Row } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import axios from 'axios';
// Constants file
import Constants from '../../shared/constants/AppConstants';
import PathConstants from '../../shared/constants/PathConstants';
import UrlConstants from '../../shared/constants/UrlConstants';
// Components file
import EmailLogin from '../../components/EmailLogin/EmailLogin';
import Loader from '../../components/Loader/Loader';
// Support modules
import packageJson from '../../../package.json';
import { themeStyling } from '../../shared/providers/ThemeStyler';
import styles from './Login.module.scss';

const Login = (props) => {
  const [configuration, setConfiguration] = useState(null);
  const [isConfigurationFetched, setIsConfigurationFetched] = useState(false);
  const [isDashboardEnabled, setIsDashboardEnabled] = useState(false);
  const [theme, setTheme] = useState(Constants.SYSTEM.DEFAULT_THEME);
  const [showLoader, shouldShowLoader] = useState(false);

  const history = useHistory();

  useEffect(() => {
    const isAuthenticated = localStorage.getItem(Constants.FIELD.IS_AUTHENTICATED);
    if (isAuthenticated) {
      history.push(PathConstants.HOME);
    } else {
      const API_HOST_URL = process.env.REACT_APP_API_HOST_URL;
      axios
        .get(`${API_HOST_URL}${UrlConstants.GET_CONFIG}`, {
          params: { dashboardConfig: true },
        })
        .then((res) => {
          processConfigurationsData(res);
        })
        .catch((err) => {});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function processConfigurationsData(res) {
    if (res.data?.result && res.data?.result?.dashboardId) {
      setConfiguration(res.data?.result);
      setIsDashboardEnabled(true);
      themeStyling(res.data?.result?.theme || Constants.SYSTEM.DEFAULT_THEME);
      setTheme(res.data?.result?.theme);
      localStorage.setItem(Constants.FIELD.CONFIGURATIONS, JSON.stringify(res.data.result));
    }
    setIsConfigurationFetched(true);
  }
  const SideMenuImage = () => {
    const name = configuration?.name || Constants.SYSTEM.DEFAULT_TITLE;
    const landingPageImageUrl = configuration?.dashboard?.landingPage?.imgUrl || Constants.SYSTEM.DEFAULT_BANNER;
    return <Image src={landingPageImageUrl} alt={name} fluid="md" className={`${styles.landingPageImage} w-100`} />;
  };

  const LogoIcon = () => {
    const name = configuration?.name || Constants.SYSTEM.DEFAULT_TITLE;
    const logo = configuration?.logo || Constants.SYSTEM.DEFAULT_ICON;
    return (
      <Image
        src={logo}
        className={isDashboardEnabled ? `${styles.logoWithLogin}` : `${styles.logoWithoutLogin}`}
        alt={name}
        rounded
      />
    );
  };

  const Footer = () => {
    document.title = configuration?.name || Constants.SYSTEM.DEFAULT_TITLE;
    const favicon = document.getElementById('favicon');
    favicon.href = configuration?.logo || Constants.SYSTEM.DEFAULT_ICON;
    return (
      <div className={styles.footerDiv}>
        <Navbar.Brand
          className={styles.centerFooter}
          href={Constants.SYSTEM.WEBSITE_LINK}
          target="_blank"
          rel="noreferrer"
          id="copyright_link"
        >
          {Constants.SYSTEM.COPYRIGHT_FOOTER} ({packageJson.version})
        </Navbar.Brand>
      </div>
    );
  };

  return theme ? (
    <div className={styles.fullScreen}>
      {showLoader ? <Loader /> : ''}
      <Row noGutters={true}>
        <Col lg={6}>{isConfigurationFetched ? <SideMenuImage /> : ''}</Col>
        <Col lg={6} className={styles.rightContainerView}>
          <div className={styles.rightContainerCenter}>
            <div>{isConfigurationFetched ? <LogoIcon /> : ''}</div>
            {isDashboardEnabled ? (
              <>
                <EmailLogin
                  isLoginPage={true}
                  setConfiguration={setConfiguration}
                  configuration={configuration}
                  shouldShowLoader={shouldShowLoader}
                />
                <Footer />
              </>
            ) : (
              ''
            )}
          </div>
        </Col>
      </Row>
    </div>
  ) : (
    ''
  );
};

Login.propTypes = {};

Login.defaultProps = {};

export default Login;
