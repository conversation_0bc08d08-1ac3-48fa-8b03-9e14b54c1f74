@import '../../styles/themes.module.scss';

// .versionFooter {
//   position: fixed;
//   right: 0;
//   bottom: 0rem;
//   width: 100%;
//   color: var(--text-secondary) !important;
//   text-align: right;
//   font-size: 0.5rem;
// }

.centerFooter {
  // position: fixed;
  right: 0;
  bottom: 0.75rem;
  width: 100%;
  color: var(--text-secondary) !important;
  // text-align: right;
  font-size: 0.5rem;
}

.fullScreen {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  overflow: hidden;
  background: var(--primary);
}

.rightContainerView {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  position: relative;
}
.footerDiv{
  // position: absolute;
  bottom: 0px;
  right: 0px;
  left: 0px;
  width: 100%;
}

.rightContainerCenter {
  display: inline-block;
}

.logoWithLogin {
  width: 10rem;
  // height: 10rem;
  // margin-top: 12rem;
  margin-bottom: 2rem;
}

.logoWithoutLogin {
  width: 10rem;
  // height: 10rem;
  // margin-top: 50%;
}

.landingPageImage {
  max-width: 100%;
  height: 100vh;
}

@media only screen and (max-width: 992px) {
  .landingPageImage {
    display: none;
  }
}
