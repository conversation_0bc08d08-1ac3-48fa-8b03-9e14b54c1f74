import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { Catalogues } from '@sworksio/dashboard-catalogue';
import '../../../node_modules/@sworksio/dashboard-catalogue/lib/index.css';
import { getThemeConfigurations, getModuleConfigurations, getApiConfigurations } from '../../shared/providers/Utils';
// Constants file
import Constants from '../../shared/constants/AppConstants';
import PathConstants from '../../shared/constants/PathConstants';
// Support modules
import styles from './CatalogueHome.module.scss';

const CatalogueHome = (props) => {
  const [themeColors, setThemeColors] = useState({});
  const [moduleConfigurations, setModuleConfigurations] = useState({});
  const [apiConfigurations, setApiConfigurations] = useState({});
  const [loadData, setLoadData] = useState(false);
  const history = useHistory();

  const configurations = JSON.parse(localStorage.getItem(Constants.FIELD.DASHBOARD_CONFIGURATIONS));
  const loaderConfiguration = configurations?.loader;
  const type = loaderConfiguration?.type || Constants.SYSTEM.DEFAULT_LOADER.TYPE;

  useEffect(() => {
    const isAuthenticated = localStorage.getItem(Constants.FIELD.IS_AUTHENTICATED);
    if (!isAuthenticated) {
      localStorage.clear();
      history.push(`${PathConstants.LOGIN}`);
    } else {
      const themeColors = getThemeConfigurations();
      setThemeColors(themeColors);
      const moduleConfigurations = getModuleConfigurations(props);
      setModuleConfigurations(moduleConfigurations);
      const apiConfigurations = getApiConfigurations();
      setApiConfigurations(apiConfigurations);
      setLoadData(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.CatalogueHome} data-testid="CatalogueHome">
      {loadData && moduleConfigurations?.title ? (
        <Catalogues
          title={moduleConfigurations.title}
          helpText={moduleConfigurations.helpText}
          themeColors={themeColors}
          moduleConfigurations={moduleConfigurations.configurations}
          apiConfigurations={apiConfigurations}
          loaderType={type}
        />
      ) : (
        ''
      )}
    </div>
  );
};

CatalogueHome.propTypes = {};

CatalogueHome.defaultProps = {};

export default CatalogueHome;
