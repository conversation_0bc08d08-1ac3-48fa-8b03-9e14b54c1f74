// NPM modules
import { useState, useEffect } from 'react';
import { Container, Col, Row } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
// Components file
// import SideMenuBar from '../../components/SideMenuBar/SideMenuBar';
import Loader from '../../components/Loader/Loader';
// Constants file
import Constants from '../../shared/constants/AppConstants';
import PathConstants from '../../shared/constants/PathConstants';
// Support modules
import { themeStyling } from '../../shared/providers/ThemeStyler';
import styles from './DashboardHome.module.scss';

import { useGAPageViewTracker } from '@sworksio/dashboard-core';
import SideMenuBar from '../../components/SideMenuBar/SideMenuBar';

const DashboardHome = (props) => {
  const [isConfigFetched, setIsConfigFetched] = useState(false);
  const [configurationDetails, setConfigurations] = useState({});
  const [dashboardConfigurations, setDashboardConfigurations] = useState({});
  const [activeMenu, setActiveMenu] = useState(false);

  const history = useHistory();
  const pageView = useGAPageViewTracker();

  const configurations = JSON.parse(localStorage.getItem(Constants.FIELD.CONFIGURATIONS));
  useEffect(() => {
    history.listen((location) => {
      document.title = configurations.name;
      let titleElement = document.querySelector('title');
      if (titleElement) titleElement.innerHTML = configurations.name || Constants.SYSTEM.DEFAULT_TITLE;
      pageView(location.pathname + location.search);
    });
    const isAuthenticated = localStorage.getItem(Constants.FIELD.IS_AUTHENTICATED);
    const refetchDate = localStorage.getItem(Constants.FIELD.REFETCH_ON);
    const refetchPassed = new Date(refetchDate) === 'Invalid Date' || new Date(refetchDate) < new Date();
    if (!isAuthenticated || !configurations || refetchPassed) {
      localStorage.clear();
      history.push(`${PathConstants.LOGIN}`);
    } else {
      setClientConfigurations();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function setClientConfigurations() {
    const configurations = JSON.parse(localStorage.getItem(Constants.FIELD.CONFIGURATIONS));
    const dashboardConfigurations = JSON.parse(localStorage.getItem(Constants.FIELD.DASHBOARD_CONFIGURATIONS));
    setConfigurations(configurations);
    setDashboardConfigurations(dashboardConfigurations);
    themeStyling(configurations?.theme || Constants.SYSTEM.DEFAULT_THEME);
    setDataOnLoad(dashboardConfigurations);
    const favicon = document.getElementById('favicon');
    favicon.href = configurations?.logo || Constants.SYSTEM.DEFAULT_ICON;
    setIsConfigFetched(true);
  }

  function setDataOnClick(moduleConfigurations) {
    setActiveMenu(moduleConfigurations);
  }

  function setDataOnLoad(configurations) {
    const pathName = window.location.pathname;
    let mappingConfiguration = configurations?.menu.find(
      (item) => item.eventKey === pathName || `${item.eventKey}/` === pathName,
    );
    if (mappingConfiguration === '{}' || mappingConfiguration === undefined) {
      mappingConfiguration = configurations?.menu[0];
    }
    history.push({
      pathname: mappingConfiguration?.eventKey,
      state: {
        eventKey: mappingConfiguration?.eventKey,
        subEventKey: mappingConfiguration?.subMenus ? mappingConfiguration.subMenus[0].eventKey : null,
      },
    });
    setActiveMenu(mappingConfiguration);
  }

  return (
    <div className={styles.fullScreen}>
      {!isConfigFetched ? (
        <Loader />
      ) : (
        <>
          <Container fluid={true} className="ps-2 pe-xl-3">
            <Row>
              <Col className={`${styles.sideBarWidthMaxWidth} px-0`}>
                <SideMenuBar
                  activeMenu={activeMenu}
                  config={configurationDetails}
                  dashboardConfig={dashboardConfigurations}
                  isConfigFetched={isConfigFetched}
                  setDataOnClick={setDataOnClick}
                  key={`side-menu`}
                  showExpanded={false}
                  history={props.history}
                />
              </Col>
              <Col lg={11} xl={11} className="ps-sm-3  ps-md-0  pe-1">
                {activeMenu ? props.children : ''}
              </Col>
            </Row>
          </Container>
        </>
      )}
    </div>
  );
};

DashboardHome.propTypes = {};

DashboardHome.defaultProps = {};

export default DashboardHome;
