import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import NotificationHome from './NotificationHome';

describe('<NotificationHome />', () => {
  test('it should mount', () => {
    render(<NotificationHome />);

    const notificationHome = screen.getByTestId('NotificationHome');

    expect(notificationHome).toBeInTheDocument();
  });
});
