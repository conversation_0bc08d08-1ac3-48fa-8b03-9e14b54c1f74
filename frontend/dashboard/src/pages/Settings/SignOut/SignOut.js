// NPM modules
import { useHistory } from 'react-router-dom';
// Constants file
import PathConstants from '../../../shared/constants/PathConstants';
// Support modules
// import styles from './SignOut.module.scss';

const SignOut = () => {
  const history = useHistory();

  const SignOutButton = () => {
    const userData = JSON.parse(localStorage.getItem('user'));
    localStorage.clear();
    history.push(`${PathConstants.LOGIN}?userType=${userData.type}`);
  };

  return (
    <div>
      <SignOutButton />
    </div>
  );
};

SignOut.propTypes = {};

SignOut.defaultProps = {};

export default SignOut;
