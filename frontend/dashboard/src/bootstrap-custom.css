.modal-dialog-scrollable 
    .modal-content {
     max-height: unset !important;
     overflow: unset !important;
   }
 

   


.form-control-lg {
    min-height: calc(1.5em + 1rem + 2px);
    padding: 0.5rem 1rem !important;
    font-size: 1.25rem !important;
    border-radius: 0.5rem !important;
}
.btn{
  padding: .5rem !important;
}
.pagination{
  padding-top: 1rem;
}

.btn-primary  {
  text-transform: uppercase;
  background-color: var(--bg-primary, #3b8396) !important;
  color: var(--text-primary, #ffffff);
  border-color: var(--bg-secondary, #2a5e6c) !important;
}
small, .small{
  font-size: small !important;
  opacity: 0.8;
}
.bs-popover-bottom>.popover-arrow::before {
  border: 0;
}
.btn-outline-primary {
  text-transform: uppercase;
  /* padding: 0.5rem; */
  /* width: 100%; */
  color: var(--bg-primary, #ffffff) !important;
  border-color: var(--bg-primary, #2a5e6c) !important;
}
.btn-outline-danger{

  text-transform: uppercase;
}
.btn-primary:hover,.btn-outline-primary:hover {
  background-color: var(--bg-primary, #2a5e6c) !important;
  color: var(--text-primary, #ffffff) !important;
  border-color: var(--bg-primary, #3b8396) !important;
}
:root {
  --font-size: 0.875rem;
}
#create_trip .form-check-input:checked {
    background-color: #000000;
    border-color: #000000;
}
#create_trip .form-control {
  font-size: var(--font-size);
}
#edit_trip .form-control {
  font-size: var(--font-size);
}
#create_trip .form-select {
  font-size: var(--font-size);
}
#edit_trip .form-select {
  font-size: var(--font-size);
}
#create_trip .form-check-label{
  font-size: var(--font-size);
}

#edit_trip .form-check-input:checked {
  background-color: #000000;
  border-color: #000000;
}
#react-select-2-input{
  font-size: var(--font-size) !important;
}
.search-ride .form-select {
  font-size: var(--font-size);
}

.search-ride .form-control {
  font-size: var(--font-size);
}

.react-datepicker__aria-live {
  display: none !important;
}

#TripDispatcher .pagination{
  margin-bottom: 0px;
}
.swal2-top-start{
margin-left: 6rem
}

  

/* .gm-style-iw.gm-style-iw-c{
box-shadow: unset !important;
background-color: transparent !important;
} */
/* 
.no-gutters {
    flex-wrap: unset !important;} */