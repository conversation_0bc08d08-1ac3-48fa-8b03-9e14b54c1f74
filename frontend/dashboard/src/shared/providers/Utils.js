// Constants file
import Constants from '../constants/AppConstants';

export const getApiConfigurations = () => {
  const user = JSON.parse(localStorage.getItem(Constants.FIELD.USER));
  const configurations = {
    baseUrl: process.env.REACT_APP_API_HOST_URL,
    userId: user._id,
    token: localStorage.getItem(Constants.FIELD.AUTH_TOKEN),
  };
  return configurations;
};

export const getThemeConfigurations = () => {
  const configurations = JSON.parse(localStorage.getItem(Constants.FIELD.CONFIGURATIONS));
  const theme = configurations?.theme || Constants.SYSTEM.DEFAULT_THEME;
  const themeConfigurations = {
    textAlternate: theme['--text-alternate'],
    textSecondary: theme['--text-secondary'],
    textPrimary: theme['--text-primary'],
    backgroundSecondary: theme['--bg-secondary'],
    backgroundPrimary: theme['--bg-primary'],
    primary: theme['--primary'],
  };
  return themeConfigurations;
};

export const getModuleConfigurations = (props) => {
  const configurations = JSON.parse(localStorage.getItem(Constants.FIELD.DASHBOARD_CONFIGURATIONS));
  if (!configurations) {
    localStorage.clear();
  }
  const mappingConfiguration = configurations?.menu.find((item) => item.eventKey === props.location.state.eventKey);
  if (mappingConfiguration?.subMenus) {
    const subMappingConfigurations = mappingConfiguration.subMenus.find(
      (item) => item.eventKey === props.location.state.subEventKey,
    );
    return subMappingConfigurations;
  }
  return mappingConfiguration;
};

export const getTabViewConfigurations = (props) => {
  const configurations = JSON.parse(localStorage.getItem(Constants.FIELD.DASHBOARD_CONFIGURATIONS));
  if (!configurations) {
    localStorage.clear();
  }
  const mappingConfiguration = configurations?.menu.find((item) => item.eventKey === props.location.state.eventKey);
  if (mappingConfiguration?.tabViewConfigurations) {
    const tabViewConfigurations = mappingConfiguration?.tabViewConfigurations;
    return tabViewConfigurations;
  }
  return {};
};

export const eventBus = {
  on(event, callback) {
    document.addEventListener(event, (e) => callback(e.detail));
  },
  dispatch(event, data) {
    document.dispatchEvent(new CustomEvent(event, { detail: data }));
  },
  remove(event, callback) {
    document.removeEventListener(event, callback);
  },
};

export default eventBus;
