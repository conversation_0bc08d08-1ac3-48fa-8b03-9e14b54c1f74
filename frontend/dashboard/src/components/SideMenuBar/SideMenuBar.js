// NPM modules
import { useState } from 'react';
import { Accordion, Container, Image, Navbar, NavItem } from 'react-bootstrap';
import { Icon } from '@sworksio/dashboard-core';
import { disableBodyScroll, enableBodyScroll } from 'body-scroll-lock';
import { size } from 'lodash';
import { Link } from 'react-router-dom';
// Support modules
import Loader from '../Loader/Loader.lazy';
import styles from './SideMenuBar.module.scss';
const SideMenuBar = (props) => {
  const config = props.config;
  const { name, logo } = config;
  const dashboard = props.dashboardConfig;
  const [expanded, setExpanded] = useState(false);

  function navClick(selectedMenu) {
    const body = document.getElementsByTagName('body');
    props.setDataOnClick(selectedMenu);
    setExpanded(false);
    enableBodyScroll(body);
  }

  function toggle() {
    var body = document.getElementsByTagName('body');
    setExpanded(expanded ? false : 'expanded');
    expanded === false ? disableBodyScroll(body) : enableBodyScroll(body);
  }

  return (
    <Navbar id="sideMenuBar" key="sideMenuBar" className={styles.sidebar} expanded={expanded} expand="lg">
      <Container fluid>
        <Image className={styles.menuLogo} src={logo} alt={config?.name} fluid="md" />
        <span className={styles.menuTitle}>{name}</span>
        <Navbar.Toggle
          aria-controls="responsive-navbar-nav"
          className={styles.hamburgerMenu}
          onClick={() => toggle()}
        />
      </Container>
      <Navbar.Collapse id="responsive-navbar-nav" key="responsive-navbar-nav">
        <Accordion className={props.showExpanded ? '' : styles.layoutHeight}>
          {!props.isConfigFetched ? (
            <Loader />
          ) : (
            <div className={styles.layoutHeight1}>
              {dashboard && size(dashboard.menu) > 0 ? (
                <>
                  {dashboard.menu.map((item, outerIndex) =>
                    item.isHidden ? (
                      ''
                    ) : (
                      <Link
                        to={{ pathname: item.path ? item.eventKey : '', state: { eventKey: item.eventKey } }}
                        key={`link-${outerIndex}`}
                        className={styles.anchor}
                      >
                        <NavItem key={item.title}>
                          <Accordion.Toggle
                            as={NavItem}
                            onClick={() => (item && size(item.subMenus) === 0 ? navClick(item) : null)}
                            key={`accordion-toggle-${outerIndex}`}
                            id={`${item.module}-at-${outerIndex}`}
                            eventKey={item.eventKey}
                            className={`${styles.linkSubMenu} ${
                              props.history?.location?.state?.eventKey === item.eventKey && size(item.subMenus) === 0
                                ? `${styles.subMenuActive}`
                                : ``
                            }`}
                          >
                            {props.showExpanded ? (
                              <span className={styles.menuIcon} key={`menu-icon-${outerIndex}`}>
                                <Icon iconName={item.icon} label={item.title} />
                              </span>
                            ) : (
                              <div style={{ width: '100%' }}>
                                <div className={styles.menuitems}>
                                  <div style={{ marginLeft: '1rem' }}>
                                    <Icon iconName={item.icon} />
                                  </div>

                                  <span className={styles.menuItemTitle}>{item.title}</span>
                                </div>
                              </div>
                            )}
                          </Accordion.Toggle>
                          {item.subMenus && size(item.subMenus) > 0 ? (
                            <>
                              <Accordion.Collapse
                                className={`${styles.collapsedMenu}`}
                                eventKey={item.eventKey}
                                id={`${item.module}-ac-${outerIndex}`}
                                key={`accordion-collapse-${outerIndex}`}
                              >
                                <Navbar className={`flex-column ${styles.menuDisplay}`} key={`nav-bar-${outerIndex}`}>
                                  {item.subMenus.map((subMenu, innerIndex) => (
                                    <>
                                      <Link
                                        key={`link-${outerIndex}`}
                                        to={{
                                          pathname: subMenu.eventKey || '',
                                          state: { subEventKey: subMenu.eventKey, eventKey: item.eventKey },
                                        }}
                                      >
                                        <NavItem
                                          key={subMenu.title}
                                          id={`${item.module}-ni-${outerIndex}`}
                                          onClick={() => navClick(subMenu)}
                                          className={`${styles.linkSubMenu} ${
                                            props.history?.location?.state?.subEventKey === subMenu.eventKey
                                              ? `${styles.subMenuActive}`
                                              : ``
                                          }`}
                                        >
                                          {props.showExpanded === false ? (
                                            <div className={styles.menuitems}>
                                              <div style={{ marginLeft: '1rem' }}>
                                                {subMenu.icon ? (
                                                  <Icon iconName={subMenu.icon} />
                                                ) : subMenu.eventKey === '/settings/signout' ? (
                                                  <Icon iconName={'FaPowerOff'} />
                                                ) : (
                                                  <Icon iconName={'FaCar'} />
                                                )}
                                              </div>

                                              <span className={styles.menuItemTitle}>{subMenu.title}</span>
                                            </div>
                                          ) : (
                                            subMenu.title
                                          )}
                                        </NavItem>
                                      </Link>
                                    </>
                                  ))}
                                </Navbar>
                              </Accordion.Collapse>
                            </>
                          ) : (
                            ''
                          )}
                        </NavItem>
                      </Link>
                    ),
                  )}
                </>
              ) : (
                ''
              )}
            </div>
          )}
        </Accordion>
      </Navbar.Collapse>
    </Navbar>
  );
};

SideMenuBar.propTypes = {};

SideMenuBar.defaultProps = {};

export default SideMenuBar;
