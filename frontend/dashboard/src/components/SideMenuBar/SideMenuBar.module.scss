@import '../../styles/themes.module.scss';

a:hover {
  text-decoration: none !important;
}

.sidebar {
  top: 0;
  bottom: 0;
  height: 100%;
  z-index: 100;
  /* Behind the navbar */
  padding: 0 0 0;
  display: block !important;
  box-shadow: inset -1px 0 0 transparent;
  background-color: var(--bg-primary);
}

.hamburgerMenu {
  background-color: var(--bg-secondary);
  margin: 1rem;
}

.menuIcon {
  margin-right: 0rem;
  margin-left: 0rem;
}

.menuItemTitle{
  font-size: 12px;
  text-align: center;
  min-width: 5rem;
}

.menuLogo {
  // width: 3rem;
  height: 3rem;
  // border-radius: 20%;
  max-width: 4rem;
  margin: 1rem;
  display: block;
  object-fit: contain;
}

.menuTitle {
  // color: var(--text-primary) !important;
  // font-size: 1rem;
  // margin: auto;
  display: none;
}

.layoutHeight {
  height: 80vh;
  text-decoration: none !important;
  position: relative;
}

.layoutHeight1 {
  height: 100vh;
  text-decoration: none !important;
  overflow: hidden;
  position: absolute;

  &:hover {
    overflow: auto;
    padding-bottom: 12vh;
  }

  ::-webkit-scrollbar {
    width: 5px !important;
    position: absolute;
    display: none !important;

  }
}

.menuitems {
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media only screen and (max-width: 992px) {
  .layoutHeight1 {
    height: 100vh;
    text-decoration: none !important;
    position: absolute !important;
    overflow: auto !important;
    width: 100%;
    padding-bottom: 16vh;
  }

  .menuitems {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 15px;

    span {
      font-size: 15px !important;
    }

    .menuItemTitle{
      text-align: left;
    }
  }
}

.collapsedMenu {
  // margin-left: 4rem;
  background-color: #95959582;
}

.menuDisplay {
  display: contents;
}

.linkSubMenu {
  color: var(--text-primary) !important;
  font-size: 1rem;
  display: flex;
  padding: 0.5rem 0.5rem;
}

.linkSubMenu:hover {
  opacity: 0.6;
  text-decoration: none;
}

.subMenuActive {
  border-left: 4px solid var(--primary) !important;
}

@media only screen and (min-width: 992px) {
  .menuLogo {
    margin-left: auto;
    margin-right: auto;
    object-fit: contain;
  }

  .menuTitle {
    display: none;
  }

  .sidebar {
    position: fixed;
    min-width: 6rem;
    margin-left: 4px;
  }

  .sidebarCompact {
    position: fixed;
    min-width: 5.2rem;
  }
}

@media only screen and (max-width: 992px) {
  .layoutHeight {
    height: 90vh;
  }
}