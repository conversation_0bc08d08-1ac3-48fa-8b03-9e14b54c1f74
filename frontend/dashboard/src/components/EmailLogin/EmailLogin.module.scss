@import '../../styles/themes.module.scss';

.headerLabel {
  color: var(--text-alternate);
  font-size: 1rem;
  text-align: left;
  display: block;
  opacity: 0.4;
}

.signInButton {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--bg-secondary);
  width: 100%;
}

.signInButton:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--bg-primary);
}

.sideMargin {
  margin-left: 1rem;
  margin-right: 1rem;
  flex-shrink: unset !important;
}

.loginError {
  color: red;
  text-align: left;
  font-size: 0.75rem;
  padding-bottom: 0.3rem;
}
