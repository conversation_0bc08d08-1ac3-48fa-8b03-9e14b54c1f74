// Scripts for firebase and firebase messaging
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

const firebaseConfig = {
  "apiKey" : 'AIzaSyA2Ob5jpJOQNWl5xxvTeiLJ5CrhHhuCN7c',
	"projectId" : 'scab-f0c5f',
	"messagingSenderId" : '836094961463',
	"appId" : '1:836094961463:web:5922d07195eeb65b59dcb2',
}

firebase.initializeApp(firebaseConfig);

// Retrieve firebase messaging
const messaging = firebase.messaging();

// Handle incoming messages while the app is not in focus (i.e in the background, hidden behind other tabs, or completely closed).
messaging.onBackgroundMessage(function(payload) {
  const notificationTitle = payload.data?.title;
  const notificationOptions = {
    body: payload.data?.body,
    icon: payload.data?.imageUrl
  };

  self.registration.showNotification(notificationTitle,
    notificationOptions);
});
