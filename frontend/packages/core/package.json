{"name": "@frontend/core", "version": "1.19.24", "description": "This is dashboard core library project", "main": "lib/index.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "scripts": {"analyze": "source-map-explorer 'lib/*'", "bootstrap": "yarn install", "clean": "<PERSON><PERSON><PERSON> dist", "start": "start-storybook -p 6006", "build": "rollup -c", "format": "prettier --write \"src/**/*\"", "test": "yarn test", "verify": "npm-run-all clean bootstrap build", "preversion": "yarn verify", "build-storybook": "build-storybook"}, "dependencies": {"@googlemaps/js-api-loader": "^1.15.1", "@types/google.maps": "^3.50.5", "axios": "^1.1.3", "dompurify": "^3.2.3", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "formik": "^2.2.6", "get-user-locale": "^2.0.0", "html-to-draftjs": "^1.5.0", "moment": "^2.29.4", "rc-time-picker": "^3.7.3", "react-bootstrap": "^1.5.2", "react-datepicker": "^4.8.0", "react-draft-wysiwyg": "^1.15.0", "react-ga": "^3.3.1", "react-icons": "^4.6.0", "react-image-gallery": "^1.2.11", "react-loader-spinner": "^4.0.0", "react-loading-skeleton": "^3.1.0", "react-phone-input-2": "^2.15.1", "react-share": "^4.4.1", "react-switch": "^7.0.0", "rollup": "^2.79.1", "styled-components": "^5.3.6", "use-latest-version": "^1.2.4", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.20.2", "@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-node-resolve": "^15.0.1", "@storybook/addon-actions": "^6.5.13", "@storybook/addon-essentials": "^6.5.13", "@storybook/addon-links": "^6.5.13", "@storybook/addon-postcss": "^3.0.0-alpha.1", "@storybook/react": "^6.5.13", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/draftjs-to-html": "^0.8.1", "@types/html-to-draftjs": "^1.4.0", "@types/react": "^17.0.2", "@types/react-datepicker": "^4.8.0", "@types/react-draft-wysiwyg": "^1.13.3", "@types/react-image-gallery": "^1.2.0", "@types/styled-components": "^5.1.26", "@types/testing-library__jest-dom": "^5.14.5", "babel-loader": "^9.1.0", "bootstrap": "^5.2.3", "generate-react-cli": "^8.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.19", "postcss-cli": "^10.0.0", "postcss-import": "^15.0.0", "postcss-nested": "^6.0.0", "postcss-preset-env": "^7.8.3", "prettier": "^2.8.0", "react": "^17.0.2", "rimraf": "^3.0.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.34.1", "source-map-explorer": "^2.5.3", "storybook-css-modules-preset": "^1.0.8", "typescript": "^4.8.4"}, "keywords": ["dashboard", "core", "react"], "author": "", "license": "ISC", "files": ["/lib"], "browser": {"fs": false, "path": false, "os": false}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}