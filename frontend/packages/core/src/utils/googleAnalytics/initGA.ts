import ReactGA from 'react-ga';

export interface GaOptions {
  name?: string;
  clientId?: string;
  sampleRate?: number;
  siteSpeedSampleRate?: number;
  alwaysSendReferrer?: boolean;
  allowAnchor?: boolean;
  cookieName?: string;
  cookieDomain?: string;
  cookieExpires?: number;
  cookieUpdate?: boolean;
  legacyCookieDomain?: string;
  legacyHistoryImport?: boolean;
  allowLinker?: boolean;
  userId?: string;
  storage?: string;
  storeGac?: boolean;
  cookieFlags?: string;
}

export interface InitializeOptions {
  debug?: boolean;
  gaAddress?: string;
  testMode?: boolean;
  titleCase?: boolean;
  gaOptions?: GaOptions;
  alwaysSendToDefaultTracker?: boolean;
  standardImplementation?: boolean;
  /** Optional. Defaults to `true`. Enables redacting a email as the string that in "Event Category" and "Event Action". */
  redactEmail?: boolean;
  useExistingGa?: boolean;
}
export function initGA(gaId: string, options?: InitializeOptions) {
  ReactGA.initialize(gaId, options);
}
