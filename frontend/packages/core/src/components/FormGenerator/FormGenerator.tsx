import React from 'react';
import TextField from './components/TextField/TextField.lazy';
import TextAreaField from './components/TextAreaField/TextAreaField.lazy';
import SelectField from './components/SelectField/SelectField.lazy';
import CheckboxField from './components/CheckboxField/CheckboxField.lazy';
import DateField from './components/DateField/DateField.lazy';
import TimeField from './components/TimeField/TimeField.lazy';
import HtmlField from './components/HtmlField/HtmlField.lazy';
import SwitchField from './components/SwitchField/SwitchField.lazy';
import LocationSelector from './components/LocationSelector/LocationSelector.lazy';
import LocationSelectorV2 from './components/LocationSelectorV2/LocationSelectorV2.lazy';
import AsyncSelect from './components/AsyncSelect/AsyncSelect.lazy';
import InfoBoxField from './components/InfoBoxField/InfoBoxField.lazy';
import RadioField from './components/RadioField/RadioField.lazy';
import PhoneField from './components/PhoneField/PhoneField.lazy';
import HtmlContentDisplay from './components/HtmlContentDisplay/HtmlContentDisplay.lazy';

export interface FormGeneratorProps {
  /**
   * The form generator fields
   */
  fields: Record<string, any>;
  /**
   * The label css
   */
  labelColor: Record<string, string>;
  /**
   * The background css
   */
  backgroundColor: Record<string, string>;
  /**
   * The button css
   */
  buttonColor: Record<string, string>;
  /**
   * The formik props generator fields
   */
  formikProps: Record<string, any>;
  /**
   * The api configurations
   */
  apiConfigurations?: Record<string, string>;

  onHandleChange?: (e: any) => void;
}

const fieldMap: any = {
  checkbox: CheckboxField,
  date: DateField,
  select: SelectField,
  radio: RadioField,
  text: TextField,
  textArea: TextAreaField,
  time: TimeField,
  html: HtmlField,
  switch: SwitchField,
  location: LocationSelector,
  locationV2: LocationSelectorV2,
  asyncSelect: AsyncSelect,
  infoBox: InfoBoxField,
  phone: PhoneField,
  htmlContent: HtmlContentDisplay,
};

export const FormGenerator: React.FC<FormGeneratorProps> = ({
  fields,
  labelColor,
  backgroundColor,
  buttonColor,
  formikProps,
  apiConfigurations,
  onHandleChange = (e) => {},
  ...props
}) => {
  const { errors, touched, values, handleBlur, handleChange, setFieldValue } = formikProps;
  const onChangeHandler = (e: any) => {
    handleChange(e);
    onHandleChange(e);
  };
  return fields.map((item: any, index: number) => {
    const Component = fieldMap[item.type];
    const error = errors && errors.hasOwnProperty(item.id) && errors[item.id];
    if (!item.type || !Component) {
      return null;
    }
    return (
      <Component
        key={index}
        label={item.label}
        name={item.id}
        type={item.type}
        subType={item.subType}
        placeholder={item.placeholder}
        value={values ? values[item.id] : ''}
        options={item.options}
        touched={touched}
        error={error}
        readOnly={item.readOnly || false}
        override={item.override || false}
        labelCss={labelColor}
        backgroundCss={backgroundColor}
        buttonCss={buttonColor}
        maxAllowedDays={item.maxAllowedDays}
        minAllowedDays={item.minAllowedDays}
        showTimeSelect={item.showTimeSelect}
        showMonthSelect={item.showMonthSelect}
        showYearSelect={item.showYearSelect}
        use12Hours={item.use12Hours}
        displayFormat={item.displayFormat}
        handleBlur={handleBlur}
        handleChange={(e: any) => onChangeHandler(e)}
        setFieldValue={setFieldValue}
        apiConfigurations={apiConfigurations || {}}
        apiKey={item.apiKey}
        searchable={item.searchable}
        timeIntervals={item.timeIntervals}
        content={item.content}
        configurations={item.configurations}
      />
    );
  });
};
