/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { FormGenerator, FormGeneratorProps } from './FormGenerator';

const data = [
  {
    id: 'name',
    label: 'Full name',
    placeholder: 'Enter Full Name',
    type: 'text',
    validationType: 'string',
    value: '',
    validations: [
      {
        type: 'required',
        params: ['Full Name is required'],
      },
      {
        type: 'min',
        params: [5, 'Full Name cannot be less than 5 characters'],
      },
      {
        type: 'max',
        params: [10, 'Full Name cannot be more than 10 characters'],
      },
    ],
  },
  {
    id: 'phone',
    label: 'Phone number',
    placeholder: 'Enter Phone Number',
    type: 'phone',
    validationType: 'string',
    value: '',
    configurations: {
      allowCountries: ['in', 'ru', 'us'],
    },
    validations: [
      {
        type: 'required',
        params: ['Full Name is required'],
      },
      {
        type: 'min',
        params: [5, 'Full Name cannot be less than 5 characters'],
      },
      {
        type: 'max',
        params: [10, 'Full Name cannot be more than 10 characters'],
      },
    ],
  },
];

export default {
  title: 'Components/FormGenerator',
  component: FormGenerator,
} as Meta;

const Template: Story<FormGeneratorProps> = (args) => <FormGenerator {...args} />;

export const GenerateForm = Template.bind({});
GenerateForm.args = {
  fields: data,
  formikProps: {},
  apiConfigurations: {
    baseUrl: 'http://localhost:3100',
    userId: '607e6bd098b6013bdf13d854',
    token: '',
  },
};
