/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { SwitchField, SwitchFieldProps } from './SwitchField';

export default {
  title: 'Components/FormGenerator/SwitchField',
  component: SwitchField,
} as Meta;

const Template: Story<SwitchFieldProps> = (args) => <SwitchField {...args} />;

export const SelectForm = Template.bind({});
SelectForm.args = {
  name: 'name',
  label: 'Full name',
  options: [
    { id: 1, name: 'K' },
    { id: 2, name: 'A' },
  ],
};
