import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { SwitchField } from './SwitchField';

const data = {
  name: 'name',
  label: 'Full name',
  value: true,
  options: [
    { id: 1, name: 'K' },
    { id: 2, name: 'A' },
  ],
  labelCss: {},
  handleBlur: function () {},
  handleChange: function () {},
  setFieldValue: function () {},
};

describe('<SwitchField />', () => {
  test('it should mount', () => {
    render(<SwitchField {...data} />);

    const selectField = screen.getByTestId('SwitchField');

    expect(selectField).toBeInTheDocument();
  });
});
