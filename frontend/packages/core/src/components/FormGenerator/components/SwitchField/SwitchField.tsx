import React from 'react';
import styles from './SwitchField.module.css';
import Switch from 'react-switch';

export interface SwitchFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The selectable options
   */
  options: any[];
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The value object
   */
  value?: boolean;
  /**
   * The place holder of the field
   */
  placeholder?: string;
  /**
   * The field is readonly
   */
  readOnly?: boolean;
  /**
   * The error handling object
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange: React.ChangeEventHandler<HTMLSelectElement>;
  /**
   * The function to be called on change
   */
  handleBlur: React.FocusEventHandler<HTMLSelectElement>;
  /**
   * The function to be called set value
   */
  setFieldValue: Function;
}

export const SwitchField: React.FC<SwitchFieldProps> = ({
  label,
  name,
  value,
  placeholder,
  labelCss,
  readOnly,
  options,
  error,
  touched,
  handleChange,
  handleBlur,
  setFieldValue,
  ...props
}) => {
  readOnly = readOnly || false;
  const [checked, setChecked] = React.useState(value || false);
  const handleOnChange = (value: any) => {
    setChecked(value);
    setFieldValue(name, value);
  };

  return (
    <div className={'form-group my-2'}>
      {label ? (
        <label htmlFor={name} style={labelCss} className={`${styles.widthFull} mb-1 mt-2`}>
          {label}
          {/* @ts-ignore - Temporary fix for React types conflict between react-switch and @testing-library/react */}
          <Switch onChange={handleOnChange} checked={checked} className={styles.reactSwitch} disabled={readOnly} />
        </label>
      ) : (
        ''
      )}
      {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
    </div>
  );
};
