import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { InfoBoxField } from './InfoBoxField';

const data = {
  name: 'name',
  value: 'Enter Full Name',
};

describe('<InfoBoxField />', () => {
  test('it should mount', () => {
    render(<InfoBoxField {...data} />);

    const infoBoxField = screen.getByTestId('InfoBoxField');

    expect(infoBoxField).toBeInTheDocument();
  });
});
