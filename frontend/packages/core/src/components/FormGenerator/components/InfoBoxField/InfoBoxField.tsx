import React from 'react';
import InfoBox from '../../../InfoBox/InfoBox.lazy';
import styles from './InfoBoxField.module.css';

export interface InfoBoxFieldProps {
  /**
   * The name field
   */
  name: string;
  /**
   * The label css
   */
  labelCss?: Record<string, string>;
  /**
   * The background css
   */
  backgroundCss?: Record<string, string>;
  /**
   * The button css
   */
  buttonCss?: Record<string, string>;
  /**
   * The value object
   */
  value: string;
  /**
   * The configurations object
   */
  configurations?: Record<string, any>;
}

export const InfoBoxField: React.FC<InfoBoxFieldProps> = ({
  name,
  labelCss,
  backgroundCss,
  buttonCss,
  value,
  configurations,
  ...props
}) => {
  value = value || '';

  return (
    <div className={`${styles.infoContainer}`}>
      <InfoBox
        text={value}
        noGutter={true}
        iconName={configurations?.iconName || 'FaLightbulb'}
        background={configurations?.background || '#D0E5FC'}
        opacity={configurations?.opacity || '0F'}
        iconColor={configurations?.iconColor || '#D0E5FC'}
        fontColor={configurations?.fontColor || '#D0E5FC'}
      />
    </div>
  );
};
