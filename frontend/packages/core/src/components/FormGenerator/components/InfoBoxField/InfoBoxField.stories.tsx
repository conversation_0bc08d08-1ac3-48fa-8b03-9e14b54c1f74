/* eslint-disable */
import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import { InfoBoxField, InfoBoxFieldProps } from './InfoBoxField';

export default {
  title: 'Components/FormGenerator/InfoBoxField',
  component: InfoBoxField,
} as Meta;

const Template: Story<InfoBoxFieldProps> = (args) => <InfoBoxField {...args} />;

export const InfoBoxForm = Template.bind({});
InfoBoxForm.args = {
  name: 'name',
  value: 'Enter Full Name',
};
