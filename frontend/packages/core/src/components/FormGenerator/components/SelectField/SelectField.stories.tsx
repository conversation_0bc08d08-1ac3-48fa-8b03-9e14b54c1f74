/* eslint-disable */
import React from 'react';
import { <PERSON><PERSON>, Story } from '@storybook/react';
import { SelectField, SelectFieldProps } from './SelectField';

export default {
  title: 'Components/FormGenerator/SelectField',
  component: SelectField,
} as Meta;

const Template: Story<SelectFieldProps> = (args) => <SelectField {...args} />;

export const SelectForm = Template.bind({});
SelectForm.args = {
  name: 'name',
  label: 'Full name',
  options: [
    { id: 1, name: 'K' },
    { id: 2, name: 'A' },
  ],
};
