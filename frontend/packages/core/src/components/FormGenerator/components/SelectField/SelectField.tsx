import React from 'react';
import styles from './SelectField.module.css';
export interface SelectFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The selectable options
   */
  options: any[];
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The key of the object to render
   */
  key?: string;
  /**
   * The value object
   */
  value?: string | number | readonly string[];
  /**
   * The place holder of the field
   */
  placeholder?: string;
  /**
   * The field is readonly
   */
  readOnly?: boolean;
  /**
   * The error handling object
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange: React.ChangeEventHandler<HTMLSelectElement>;
  /**
   * The function to be called on change
   */
  handleBlur: React.FocusEventHandler<HTMLSelectElement>;
}

export const SelectField: React.FC<SelectFieldProps> = ({
  label,
  name,
  value,
  key,
  placeholder,
  labelCss,
  readOnly,
  options,
  error,
  touched,
  handleChange,
  handleBlur,
  ...props
}) => {
  placeholder = placeholder || `Please Select`;
  readOnly = readOnly || false;
  const valueKey: string = key || 'name';

  return (
    <div className={'form-group my-2'}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ''
      )}
      <select
        id={name}
        className={`form-control form-select form-control-lg ${styles.selectIcon}`}
        name={name}
        defaultValue={value || 'none'}
        onBlur={handleBlur}
        onChange={handleChange}
      >
        <option value="none" disabled hidden>
          {placeholder}
        </option>
        {options.map((opt, index) => {
          return (
            <option key={index} value={opt?.id}>
              {opt[valueKey]}
            </option>
          );
        })}
      </select>
      {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
    </div>
  );
};
