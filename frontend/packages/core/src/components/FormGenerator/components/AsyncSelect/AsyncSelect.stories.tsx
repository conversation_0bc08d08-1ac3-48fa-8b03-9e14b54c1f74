/* eslint-disable */
import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import { AsyncSelect, AsyncSelectProps } from './AsyncSelect';

export default {
  title: 'Components/FormGenerator/AsyncSelect',
  component: AsyncSelect,
} as Meta;

const Template: Story<AsyncSelectProps> = (args) => <AsyncSelect {...args} />;

export const AsyncSelectForm = Template.bind({});
AsyncSelectForm.args = {
  name: 'name',
  label: 'Full name',
  options: [
    { id: 1, name: 'KOAAA' },
    { id: 2, name: 'ANDRA<PERSON>' },
  ],
  searchable: true,
};
