.errorText {
  color: red;
  font-size: 12px;
  padding-top: 0.3rem;
}

.overlay {
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  position: fixed;
}

.selectBox {
  cursor: pointer;
  user-select: none;
  color: #495057;
}

.container {
  z-index: 5;
  margin-top: 0.25rem;
  cursor: pointer;
  display: block;
  width: 100%;
  max-height: 15rem;
  overflow: auto;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-shadow: 0px 0px 10px -2px rgba(0, 0, 0, 0.75);
}

.option {
  width: 100%;
  padding: 0.375rem 0.75rem;
}

.option:hover {
  background-color: var(--bg-secondary, #f2f2f2);
  color: var(--text-primary, #000);
}

.addOptionLabel {
  padding: 0.375rem 0.75rem;
  color: var(--text-secondary, #000);
  font-size: 1rem;
}

.addOptionLabel::before {
  content: '+';
  padding-right: 0.25rem;
}
