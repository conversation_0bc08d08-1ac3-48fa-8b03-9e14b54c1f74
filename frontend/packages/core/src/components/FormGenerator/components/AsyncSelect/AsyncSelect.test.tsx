import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { AsyncSelect } from './AsyncSelect';

const data = {
  name: 'name',
  label: 'Full name',
  value: {},
  options: [
    { id: 1, name: 'K' },
    { id: 2, name: 'A' },
  ],
  labelCss: {},
  handleBlur: function () {},
  handleChange: function () {},
};

describe('<AsyncSelect />', () => {
  test('it should mount', () => {
    render(<AsyncSelect {...data} />);

    const asyncSelect = screen.getByTestId('AsyncSelect');

    expect(asyncSelect).toBeInTheDocument();
  });
});
