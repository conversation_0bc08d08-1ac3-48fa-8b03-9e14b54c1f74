import React, { useState, useRef } from 'react';
import styles from './AsyncSelect.module.css';
export interface AsyncSelectProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The selectable options
   */
  options: any[];
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The key of the object to render
   */
  labelKey?: string;
  /**
   * The value object
   */
  value?: Record<string, any>;
  /**
   * The place holder of the field
   */
  placeholder?: string;
  /**
   * The field is readonly
   */
  readOnly?: boolean;
  /**
   * The options are loading
   */
  loading?: boolean;
  /**
   * is filled searchable
   */
  searchable?: boolean;
  /**
   * The error handling object
   */
  error?: Record<string, any>;
  /**
   * message shown if search item not found
   */
  notFoundError?: string;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * this is going to use the clear input field on select
   */
  isInputClearOnSelect?: boolean;
  /**
   * The function to be called on change
   */
  handleChange?: React.ChangeEventHandler;
  /**
   * the function to be called on search input change
   */
  onInputChange?: React.ChangeEventHandler;
  /**
   * The function to be called on change
   */
  handleBlur?: React.FocusEventHandler;
  /**
   * the function to be called on clicking new option
   */
  newOptionHandler?: React.ChangeEventHandler<any>;
  /**
   * new option button label
   */
  newOptionLabel?: string;
  /**
   * The function to be called set value
   */
  setFieldValue?: Function;
}

export const AsyncSelect: React.FC<AsyncSelectProps> = ({
  label,
  name,
  labelKey,
  placeholder,
  labelCss,
  readOnly,
  options,
  error,
  value,
  notFoundError,
  loading,
  touched,
  handleChange,
  onInputChange,
  newOptionLabel,
  newOptionHandler,
  setFieldValue,
  searchable,
  isInputClearOnSelect,
  ...props
}) => {
  placeholder = placeholder || `Please Select`;
  readOnly = readOnly || false;
  const valueKey: string = labelKey || 'name';
  isInputClearOnSelect = isInputClearOnSelect || false;
  const [searchedText, setSearchedText] = useState<string>(value ? value[valueKey] : '');
  const [showDropdown, setShowDropdown] = useState(false);

  const onSearchedHandle = (event: any) => {
    const { value } = event.target;
    if (value.length > 0) {
      setShowDropdown(true);
    } else {
      setShowDropdown(false);
    }
    onInputChange && onInputChange(value);
    setSearchedText(value);
  };

  const onChangeHandler = (option: any) => {
    setShowDropdown(false);
    isInputClearOnSelect ? setSearchedText('') : setSearchedText(option[valueKey]);
    handleChange && handleChange(option);
  };

  const onFocusHandler = () => {
    if ((searchable && searchedText.length > 0) || options.length > 0) {
      setShowDropdown(true);
    }
  };

  return (
    <>
      <div className={showDropdown ? styles.overlay : ''} onClick={() => setShowDropdown(false)}></div>
      <div className={'form-group my-2'}>
        {label ? (
          <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
            {label}
          </label>
        ) : (
          ''
        )}
        <div className={`position-relative`}>
          {searchable ? (
            <input
              className={'form-control form-control-lg'}
              type="text"
              name={name}
              onFocus={onFocusHandler}
              placeholder={placeholder}
              value={searchedText}
              readOnly={readOnly}
              onChange={onSearchedHandle}
              autoComplete="off"
              onClick={(e) => e.stopPropagation()}
              onBlur={props.handleBlur}
            />
          ) : (
            <div className={`form-control form-control-lg ${styles.selectBox}`} onClick={() => setShowDropdown(true)}>
              {searchedText || placeholder}
            </div>
          )}

          <div className={`position-absolute w-100  ${!showDropdown && 'd-none'}`} style={{ zIndex: 5 }}>
            {showDropdown ? (
              <div className={styles.container}>
                {loading && <div className={styles.option}>Loading...</div>}
                {!loading && options.length
                  ? options.map((option, index) => (
                      <div
                        className={styles.option}
                        title={option[valueKey]}
                        key={index}
                        onClick={() => onChangeHandler(option)}
                      >
                        {option[valueKey]}
                      </div>
                    ))
                  : !loading && <div className={styles.option}>{notFoundError || 'No Items'}</div>}

                {newOptionHandler && (
                  <div className={styles.addOptionLabel} onClick={newOptionHandler}>
                    {newOptionLabel || 'Add new'}
                  </div>
                )}
              </div>
            ) : (
              ''
            )}
          </div>
        </div>

        {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
      </div>
    </>
  );
};
