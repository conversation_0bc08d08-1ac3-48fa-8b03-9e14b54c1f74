/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { LocationSelector, LocationSelectorProps } from './LocationSelector';

export default {
  title: 'Components/FormGenerator/LocationSelector',
  component: LocationSelector,
} as Meta;

const Template: Story<LocationSelectorProps> = (args) => <LocationSelector {...args} />;

export const LocationSelectorForm = Template.bind({});
LocationSelectorForm.args = {
  name: 'your-location',
  label: 'Your location',
  placeholder: 'Enter your location',
  apiKey: '',
  configurations: { autoCompletionRequest: { location: { lng: -85.48142, lat: 32.60677 }, radius: 16900 } },
  onChange: (value) => console.log(value),
  // configurations: { componentRestrictions: { country: ['us'] } },
};
