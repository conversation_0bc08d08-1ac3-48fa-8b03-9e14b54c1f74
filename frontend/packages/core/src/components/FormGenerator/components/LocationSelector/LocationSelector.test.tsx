import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { LocationSelector } from './LocationSelector';

const data = {
  name: 'name',
  label: 'Full name',
  placeholder: 'Enter Full Name',
  subType: 'text',
  value: '',
  labelCss: {},
  handleBlur: function () {},
  handleChange: function () {},
  apiKey: '',
  debouceTime: 0,
  configurations: {},
  onChange: function () {},
  setFieldValue: function () {},
};

describe('<LocationSelector />', () => {
  test('it should mount', () => {
    render(<LocationSelector {...data} />);

    const locationSelector = screen.getByTestId('LocationSelector');

    expect(locationSelector).toBeInTheDocument();
  });
});
