import React, { useState, useEffect, useImperativeHandle } from 'react';
import { AsyncSelect } from '../AsyncSelect';
import { Loader } from '@googlemaps/js-api-loader';
import { debounce } from 'lodash';
export type GooglePlacesAutocompleteHandle = {
  getSessionToken: () => google.maps.places.AutocompleteSessionToken | undefined;
  refreshSessionToken: () => void;
};

export interface LatLng {
  lat: number;
  lng: number;
}

export interface AutocompletionRequest {
  bounds?: [LatLng, LatLng];
  componentRestrictions?: { country: string | string[] };
  location?: LatLng;
  offset?: number;
  radius?: number;
  types?: string[];
}

export interface option {
  label: string;
  value: any;
}

export interface LocationSelectorProps {
  /**
   * Google API key
   */
  apiKey: string;
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * ref for the component
   */
  ref?: React.Ref<GooglePlacesAutocompleteHandle>;
  /**
   * The place holder of the field
   */
  placeholder: string;
  /**
   * The place holder of the field
   */
  debouceTime: number;
  /**
   * The configurations for location selector component
   */
  configurations: Record<string, any>;
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The field is readonly
   */
  readOnly?: boolean;
  /**
   * The error handling object
   */

  abbreviation?: boolean;
  /**
   * The is for state field
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange: React.ChangeEventHandler<HTMLInputElement>;
  /**
   * The function to be called on change
   */
  onChange: React.ChangeEventHandler<any>;
  /**
   * The function to be called on blur
   */
  handleBlur: React.FocusEventHandler<HTMLInputElement>;
  /**
   * The function to be called set value
   */
  setFieldValue: Function;
}

export const LocationSelector: React.FC<LocationSelectorProps> = ({
  name,
  apiKey,
  configurations,
  setFieldValue,
  onChange,
  ref,
  debouceTime,
  abbreviation,
  ...props
}) => {
  const apiOptions = {};
  let autoCompletionRequest = {
    componentRestrictions: { country: ['us', 'in'] },
    // offset: 3,
  } as AutocompletionRequest;
  if (configurations && configurations.autoCompletionRequest) {
    autoCompletionRequest = { ...autoCompletionRequest, ...configurations.autoCompletionRequest };
  }
  const withSessionToken = false;
  const [placesService, setPlacesService] = useState<google.maps.places.AutocompleteService | undefined>(undefined);
  const [sessionToken, setSessionToken] = useState<google.maps.places.AutocompleteSessionToken | undefined>(undefined);
  const [options, setOptions] = useState<option[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchResults = debounce((value: string, cb: (options: option[]) => void): void => {
    if (!placesService) return cb([]);
    setLoading(true);
    const autoCompletionReq: AutocompletionRequest = { ...autoCompletionRequest };

    placesService.getPlacePredictions(
      requestBuilder(autoCompletionReq, value, withSessionToken && sessionToken),
      (suggestions) => {
        cb((suggestions || []).map((suggestion) => ({ label: suggestion.description, value: suggestion })));
        setLoading(false);
      },
    );
  }, debouceTime || 500);

  const handleOnSelect = ({ value }: any) => {
    if (!value) return;
    const { description } = value;
    geocodeByAddress(description).then((results: google.maps.GeocoderResult[]) => {
      if (results.length === 0) return;
      const parsedLocation = parseResponse(results[0], abbreviation!);
      setFieldValue(name, parsedLocation);
      onChange && onChange(parseResponse(parsedLocation, abbreviation!));
    });
  };

  useEffect(() => {
    const init = async () => {
      try {
        if (!window.google || !window.google.maps || !window.google.maps.places) {
          await new Loader({ apiKey, ...{ libraries: ['places'], ...apiOptions } }).load();
        }
        initializeService();
      } catch (error) {
        throw error;
      }
    };
    if (apiKey) init();
    else initializeService();
  }, []);

  const initializeService = () => {
    if (!window?.google?.maps?.places)
      throw new Error('[react-google-places-autocomplete]: Google maps places script not loaded / Invalid API key');

    setPlacesService(new window.google.maps.places.AutocompleteService());
    setSessionToken(new google.maps.places.AutocompleteSessionToken());

    const geocoder = new window.google.maps.Geocoder();
  };

  useImperativeHandle(
    ref,
    () => ({
      getSessionToken: () => {
        return sessionToken;
      },
      refreshSessionToken: () => {
        setSessionToken(new google.maps.places.AutocompleteSessionToken());
      },
    }),
    [sessionToken],
  );

  return (
    <AsyncSelect
      name={name}
      {...props}
      options={options}
      labelKey="label"
      handleChange={handleOnSelect}
      onInputChange={(searchedInput: any): void => {
        fetchResults(searchedInput, (data: option[]) => setOptions(data));
      }}
      searchable={true}
      loading={loading}
    />
  );
};

const requestBuilder = (
  autoCompletionRequest: AutocompletionRequest,
  input: string,
  sessionToken?: google.maps.places.AutocompleteSessionToken,
): google.maps.places.AutocompletionRequest => {
  const { bounds, location, radius, ...rest } = autoCompletionRequest;

  const res: google.maps.places.AutocompletionRequest = {
    input,
    ...rest,
  };

  if (sessionToken) {
    res.sessionToken = sessionToken;
  }

  if (bounds) {
    res.bounds = new google.maps.LatLngBounds(...bounds);
  }

  if (location) {
    res.location = new google.maps.LatLng(location);
  }

  if (radius) {
    res.radius = radius;
  }

  return res;
};

const geocodeByAddress = (address: string): Promise<google.maps.GeocoderResult[]> => {
  const geocoder = new window.google.maps.Geocoder();
  const { OK } = window.google.maps.GeocoderStatus;

  return new Promise((resolve, reject) => {
    geocoder.geocode({ address }, (results: any, status: google.maps.GeocoderStatus) => {
      if (status !== OK) {
        return reject(status);
      }
      return resolve(results);
    });
  });
};

const parseResponse = (geocoderResult: google.maps.GeocoderResult, abbreviation: boolean): any => {
  const mapping: Record<string, string> = {
    locality: 'city',
    country: 'country',
    administrative_area_level_1: 'state',
    postal_code: 'pin',
  };
  const { geometry, formatted_address, address_components } = geocoderResult;
  const { location } = geometry;
  const { lat, lng } = location;
  let valueObject: Record<string, string> = {};

  address_components.forEach((component) => {
    const type = mapping[component.types[0]];
    if (type) {
      valueObject[type] =
        (type === 'state' && abbreviation) || abbreviation === undefined ? component.short_name : component.long_name;
    }
  });
  return { ...valueObject, latitude: lat(), longitude: lng(), addressLine1: formatted_address };
};
