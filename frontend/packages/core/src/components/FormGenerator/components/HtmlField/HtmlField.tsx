import React, { useEffect, useState } from 'react';
import { Editor } from 'react-draft-wysiwyg';
import { EditorState, convertToRaw, ContentState } from 'draft-js';
import draftToHtml from 'draftjs-to-html';
import htmlToDraft from 'html-to-draftjs';
import './react-draft-wysiwyg.css';
import styles from './HtmlField.module.css';
import _ from 'lodash';

const toolbarConfigurations = {
  options: [
    'inline',
    'blockType',
    'fontSize',
    'fontFamily',
    'list',
    'colorPicker',
    'emoji',
    'remove',
    'textAlign',
    'history',
  ],
  inline: {
    inDropdown: false,
  },
  blockType: {
    inDropdown: true,
  },
  fontSize: {},
  fontFamily: {
    options: ['Arial', 'Georgia', 'Impact', 'Poppins', 'SF Pro', 'Tahoma', 'Times New Roman', 'Verdana'],
    className: undefined,
    component: undefined,
    dropdownClassName: undefined,
  },
  list: {
    inDropdown: false,
  },
  textAlign: {
    inDropdown: false,
  },
  colorPicker: {},
  emoji: {},
  remove: {},
  history: {
    inDropdown: false,
  },
};

export interface HtmlFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The function to be called set value
   */
  setFieldValue: Function;
  /**
   * The value object
   */
  value?: string;
  /**
   * The placeholder image url
   */
  placeholder?: string;
  /**
   * The htmled field can be edited or not
   */
  readOnly?: boolean;
  /**
   * The error handling object
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange?: React.ChangeEventHandler<HTMLInputElement>;
}

export function HtmlField({
  name,
  label,
  readOnly,
  value,
  labelCss,
  placeholder,
  error,
  touched,
  setFieldValue,
  handleChange,
}: HtmlFieldProps) {
  const [editorState, setEditorState] = useState<any>();
  readOnly = readOnly || false;
  placeholder = placeholder || 'Enter content here';

  useEffect(() => {
    const contentBlock = _.size(value) > 0 && typeof value === 'string' ? htmlToDraft(value) : null;
    if (contentBlock) {
      const contentState = ContentState.createFromBlockArray(contentBlock.contentBlocks);
      const editorState = EditorState.createWithContent(contentState);
      setEditorState(editorState);
    }
  }, []);

  function onEditorStateChange(editorState: any) {
    setEditorState(editorState);
    const htmlString = draftToHtml(convertToRaw(editorState.getCurrentContent()));
    setFieldValue(name, htmlString);
  }

  return (
    <div className={`form-group my-2`}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ''
      )}
      {/* @ts-ignore - Temporary fix for React types conflict between react-draft-wysiwyg and @testing-library/react */}
      <Editor
        editorState={editorState}
        onEditorStateChange={onEditorStateChange}
        placeholder={placeholder}
        readOnly={readOnly}
        spellCheck={true}
        wrapperClassName={styles.wrapper}
        toolbar={toolbarConfigurations}
      />
      {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
    </div>
  );
}
export default HtmlField;
