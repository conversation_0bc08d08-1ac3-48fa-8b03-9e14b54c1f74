import '@testing-library/jest-dom/extend-expect';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { HtmlField } from './HtmlField';

const data = {
  name: 'html',
  label: 'HTML',
  labelCss: {},
  setFieldValue: function () {},
};

describe('<HtmlField />', () => {
  test('it should mount', () => {
    render(<HtmlField {...data} />);

    const htmlField = screen.getByTestId('HtmlField');

    expect(htmlField).toBeInTheDocument();
  });
});
