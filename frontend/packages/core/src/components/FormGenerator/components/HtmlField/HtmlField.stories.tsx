/* eslint-disable */
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import React from 'react';
import { HtmlField, HtmlFieldProps } from './HtmlField';

export default {
  title: 'Components/FormGenerator/HtmlField',
  component: HtmlField,
} as Meta;

const Template: Story<HtmlFieldProps> = (args) => <HtmlField {...args} />;

export const HtmlForm = Template.bind({});
HtmlForm.args = {
  name: 'html',
  label: 'HTML',
  labelCss: {},
};
