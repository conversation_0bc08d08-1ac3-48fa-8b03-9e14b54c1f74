import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Loader } from '@googlemaps/js-api-loader';
export type GooglePlacesAutocompleteHandleV2 = {
  getSessionToken: () => google.maps.places.AutocompleteSessionToken | undefined;
  refreshSessionToken: () => void;
};
import { TextField } from '../TextField';
export interface LatLngV2 {
  lat: number;
  lng: number;
}

export interface AutocompletionRequestV2 {
  bounds?: google.maps.LatLngBounds;
  componentRestrictions?: { country: string | string[] };
  location?: LatLngV2;
  offset?: number;
  radius?: number;
  types?: string[];
  strictbounds?: boolean;
}

export interface optionV2 {
  label: string;
  value: any;
}

export interface LocationSelectorPropsV2 {
  /**
   * Google API key
   */
  apiKey: string;
  /**
   * The label for the field
   */
  value?: any;

  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * ref for the component
   */
  ref?: React.Ref<GooglePlacesAutocompleteHandleV2>;
  /**
   * The place holder of the field
   */
  placeholder: string;
  /**
   * The place holder of the field
   */
  debouceTime?: number;
  /**
   * The configurations for location selector component
   */
  configurations: Record<string, any>;
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The field is readonly
   */
  readOnly?: boolean;
  /**
   * The error handling object
   */

  abbreviation?: boolean;
  /**
   * The is for state field
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange?: React.ChangeEventHandler<HTMLInputElement>;
  /**
   * The function to be called on change
   */
  onChange: React.ChangeEventHandler<any>;
  /**
   * The function to be called on blur
   */
  handleBlur: React.FocusEventHandler<HTMLInputElement>;
  /**
   * The function to be called set value
   */
  setFieldValue?: Function;
}

export const LocationSelectorV2: React.FC<LocationSelectorPropsV2> = ({
  name,
  value,
  apiKey,
  configurations,
  onChange,
  handleChange,
  setFieldValue,
  ref,
  debouceTime,
  abbreviation,
  ...props
}) => {
  const apiOptions = {};
  let autocompletionRequest = {
    componentRestrictions: { country: configurations?.componentRestrictions?.country || [] },
  } as AutocompletionRequestV2;
  if (configurations && configurations.autocompletionRequest) {
    autocompletionRequest = { ...autocompletionRequest, ...configurations.autoCompletionRequest };
  }
  const [sessionToken, setSessionToken] = useState<google.maps.places.AutocompleteSessionToken | undefined>(undefined);
  const [inputValue, setInputValue] = useState<string>(value ? value.addressLine1 : '');
  useEffect(() => {
    const input = document.getElementsByName(name)[0] as HTMLInputElement;
    // if (input && value) {
    //   input.value = value;
    // }
    const init = async () => {
      try {
        await new Loader({ apiKey, ...{ libraries: ['places'], ...apiOptions } })
          .load()
          .then((google) => {
            const geolocation = {
              lat: configurations?.bounds?.lat,
              lng: configurations?.bounds?.lng,
            };
            const circle = new google.maps.Circle({ center: geolocation, radius: configurations?.radius });
            const autoComplete = new google.maps.places.Autocomplete(input, {
              bounds: circle?.getBounds()! || undefined,
              strictBounds: true,
              componentRestrictions: autocompletionRequest.componentRestrictions,
            });
            autoComplete.addListener('place_changed', () => {
              let place = autoComplete.getPlace();
              onChange && onChange(parseResponse(place, abbreviation!));
              setFieldValue && setFieldValue(name, parseResponse(place, abbreviation!));
              setInputValue(parseResponse(place, abbreviation!).addressLine1);
              // value = parseResponse(place, abbreviation!).addressLine1;
            });
            const styleElem = document.head.appendChild(document.createElement('style'));
            styleElem.innerHTML = '.pac-container {z-index: 10000;}';
          })
          .catch((e) => {});
        initializeService();
      } catch (error) {
        throw error;
      }
    };
    if (apiKey) init();
    else initializeService();
  }, []);

  const initializeService = () => {
    if (!window?.google?.maps?.places)
      throw new Error('[react-google-places-autocomplete]: Google maps places script not loaded / Invalid API key');
    setSessionToken(new google.maps.places.AutocompleteSessionToken());
  };

  useImperativeHandle(
    ref,
    () => ({
      getSessionToken: () => {
        return sessionToken;
      },
      refreshSessionToken: () => {
        setSessionToken(new google.maps.places.AutocompleteSessionToken());
      },
    }),
    [sessionToken],
  );
  const onChangeHandler = (e: any) => {
    // console.log(e);
    setInputValue(e.target.value);
    // handleChange && handleChange(e);
  };
  const styleElem = document.head.appendChild(document.createElement('style'));
  const styleElem1 = document.head.appendChild(document.createElement('style'));
  styleElem.innerHTML = '.pac-logo:after {display: none;}';
  styleElem1.innerHTML = '.pac-item {padding: 5px;cursor: pointer;}';
  return <TextField value={inputValue} name={name} {...props} handleChange={onChangeHandler} />;
};

const parseResponse = (geocoderResult: google.maps.places.PlaceResult, abbreviation: boolean): any => {
  const mapping: Record<string, string> = {
    locality: 'city',
    country: 'country',
    administrative_area_level_1: 'state',
    postal_code: 'pin',
  };
  const { geometry, formatted_address, address_components } = geocoderResult;
  let valueObject: Record<string, string> = {};
  address_components &&
    address_components?.forEach((component) => {
      const type = mapping[component.types[0]];
      if (type) {
        valueObject[type] =
          (type === 'state' && abbreviation) || abbreviation === undefined ? component.short_name : component.long_name;
      }
    });
  return {
    ...valueObject,
    latitude: geometry?.location?.lat(),
    longitude: geometry?.location?.lng(),
    addressLine1: formatted_address,
  };
};
