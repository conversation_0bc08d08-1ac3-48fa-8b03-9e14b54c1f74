/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { LocationSelectorV2, LocationSelectorPropsV2 } from './LocationSelectorV2';

export default {
  title: 'Components/FormGenerator/LocationSelectorV2',
  component: LocationSelectorV2,
} as Meta;

const Template: Story<LocationSelectorPropsV2> = (args) => <LocationSelectorV2 {...args} />;

export const LocationSelectorForm = Template.bind({});
LocationSelectorForm.args = {
  name: 'location',
  value: {
    addressLine1: 'Texas, USA',
    country: 'US',
    latitude: 31.9685988,
    longitude: -99.9018131,
    state: 'TX',
  },
  label: 'Select location',
  placeholder: 'Enter your location v2',
  apiKey: '',
  onChange: (value) => console.log(value),
  configurations: { componentRestrictions: { country: ['US'] } },
};
