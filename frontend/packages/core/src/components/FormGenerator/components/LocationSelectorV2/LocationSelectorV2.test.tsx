import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { LocationSelectorV2 } from './LocationSelectorV2';

const data = {
  name: 'name',
  label: 'Full name',
  placeholder: 'Enter Full Name',
  subType: 'text',
  value: '',
  labelCss: {},
  handleBlur: function () {},
  handleChange: function () {},
  apiKey: '',
  configurations: {},
  onChange: function () {},
};

describe('<LocationSelectorV2 />', () => {
  test('it should mount', () => {
    render(<LocationSelectorV2 {...data} />);

    const LocationSelector = screen.getByTestId('LocationSelector');

    expect(LocationSelector).toBeInTheDocument();
  });
});
