import moment from 'moment';
import TimePicker from 'rc-time-picker';
import 'rc-time-picker/assets/index.css';
import React from 'react';
import styles from './TimeField.module.css';
export interface TimeFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * Checks if item is read only or not
   */
  readOnly: boolean;
  /**
   * The function to be called set value
   */
  setFieldValue: Function;
  /**
   * The difference over selectable time
   */
  difference?: number;
  /**
   * Should the format to be used be 12 hours or 24 hours
   */
  use12Hours?: boolean;
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The value object
   */
  value?: string;
  /**
   * The error handling object
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange?: React.ChangeEventHandler<HTMLInputElement>;
  /**
   * The function to be called on nlur
   */
  handleBlur?: React.FocusEventHandler<HTMLInputElement>;
}

export const TimeField: React.FC<TimeFieldProps> = ({
  label,
  name,
  setFieldValue,
  readOnly,
  difference,
  use12Hours,
  labelCss,
  value,
  error,
  touched,
  handleChange,
  handleBlur,
  ...props
}) => {
  const [dispatchTime, setDispatchTime] = React.useState(moment());
  use12Hours = use12Hours || false;
  readOnly = readOnly || false;
  difference = difference || 10;

  const handleValueChange = (value: any) => {
    setDispatchTime(value);
    setFieldValue(name, moment(value).format('hh:mm A'));
  };

  return (
    <div className={'form-group my-2'}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ''
      )}
      <br />
      {/* @ts-ignore - Temporary fix for React types conflict between react-time-picker and @testing-library/react */}
      <TimePicker
        value={dispatchTime}
        className={styles.formControlTime}
        use12Hours={use12Hours}
        minuteStep={difference}
        showSecond={false}
        inputReadOnly={readOnly}
        onChange={handleValueChange}
      />
      {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
    </div>
  );
};
