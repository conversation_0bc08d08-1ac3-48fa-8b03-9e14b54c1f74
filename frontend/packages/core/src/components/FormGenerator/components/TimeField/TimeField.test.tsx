import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { TimeField } from './TimeField';

const data = {
  name: 'path',
  label: 'Image',
  value: '',
  labelCss: {},
  readOnly: false,
  setFieldValue: function () {},
};

describe('<TimeField />', () => {
  test('it should mount', () => {
    render(<TimeField {...data} />);

    const timeField = screen.getByTestId('TimeField');

    expect(timeField).toBeInTheDocument();
  });
});
