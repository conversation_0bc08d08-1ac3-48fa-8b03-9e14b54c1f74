/* eslint-disable */
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import React from 'react';
import { TimeField, TimeFieldProps } from './TimeField';
export default {
  title: 'Components/FormGenerator/TimeField',
  component: TimeField,
} as Meta;

const Template: Story<TimeFieldProps> = (args) => <TimeField {...args} />;

export const TimeForm = Template.bind({});
TimeForm.args = {
  name: 'time',
  label: 'Select time',
  value: '',
  labelCss: {},
};
