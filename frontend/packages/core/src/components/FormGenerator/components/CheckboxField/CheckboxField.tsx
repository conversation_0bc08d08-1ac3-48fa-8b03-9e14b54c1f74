import React, { useState } from 'react';
import styles from './CheckboxField.module.css';
export interface CheckboxFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The selectable options
   */
  options: any[];
  /**
   * The value object
   */
  value?: string | number | readonly string[];
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The field is readonly
   */
  readOnly: boolean;
  /**
   * The error handling of data
   */
  error?: Record<string, any>;
  /**
   * The touch handling of data
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange: React.ChangeEventHandler<HTMLInputElement>;
  /**
   * The function to be called on blur
   */
  handleBlur: React.FocusEventHandler<HTMLInputElement>;
  /**
   * The function to be called set value
   */
  setFieldValue: Function;
}

export const CheckboxField: React.FC<CheckboxFieldProps> = ({
  label,
  name,
  options,
  labelCss,
  error,
  touched,
  readOnly,
  handleChange,
  handleBlur,
  setFieldValue,
  ...props
}) => {
  const [checkedItems, setCheckedItems] = useState(new Map());
  // to avoid conflict with key name in handle click item from event
  const fieldName = name;
  readOnly = readOnly || false;

  const handleCheckItem = (event: any) => {
    const { name, value } = event.target;
    let items = new Map(checkedItems);
    if (checkedItems.has(name)) {
      items.delete(name);
    } else {
      items.set(name, JSON.parse(value));
    }
    setCheckedItems(items);
    if (typeof setFieldValue === 'function') {
      setFieldValue(fieldName, Array.from(items.values()));
    }
  };
  return (
    <div className={'form-group my-2'}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ''
      )}
      <div className={'form-check row d-flex'}>
        {options.map((opt, index) => {
          return (
            <div className={'checkbox col-6'}>
              <label
                className={`form-check-label ${styles.formCheckLabel}`}
                key={index}
                htmlFor={`${fieldName}-${index}`}
              >
                <input
                  type="checkbox"
                  name={`${fieldName}-${index}`}
                  className={'form-check-input'}
                  value={JSON.stringify(opt)}
                  readOnly={readOnly}
                  onBlur={handleBlur}
                  checked={checkedItems.get(`${fieldName}-${index}`) || false}
                  onChange={handleCheckItem}
                />
                {opt.name}
              </label>
            </div>
          );
        })}
        {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
      </div>
    </div>
  );
};
