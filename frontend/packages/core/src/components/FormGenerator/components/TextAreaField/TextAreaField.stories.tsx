/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { TextAreaField, TextAreaFieldProps } from './TextAreaField';

export default {
  title: 'Components/FormGenerator/TextAreaField',
  component: TextAreaField,
} as Meta;

const Template: Story<TextAreaFieldProps> = (args) => <TextAreaField {...args} />;

export const TextAreaForm = Template.bind({});
TextAreaForm.args = {
  name: 'name',
  label: 'Full name',
  placeholder: 'Enter Full Name',
  value: '',
};
