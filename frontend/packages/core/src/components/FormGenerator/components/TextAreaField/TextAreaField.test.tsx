import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { TextAreaField } from './TextAreaField';

const data = {
  name: 'name',
  label: 'Full name',
  placeholder: 'Enter Full Name',
  value: '',
  labelCss: {},
  handleChange: function () {},
};

describe('<TextAreaField />', () => {
  test('it should mount', () => {
    render(<TextAreaField {...data} />);

    const textAreaField = screen.getByTestId('TextAreaField');

    expect(textAreaField).toBeInTheDocument();
  });
});
