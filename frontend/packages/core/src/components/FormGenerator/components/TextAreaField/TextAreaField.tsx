import React from 'react';
import InfoBox from '../../../InfoBox/InfoBox.lazy';
import styles from './TextAreaField.module.css';
export interface TextAreaFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The place holder of the field
   */
  placeholder: string;
  /**
   * The field is readonly
   */
  readOnly?: boolean;
  /**
   * The value object
   */
  value?: string | number | readonly string[];
  /**
   * The error handling object
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange: React.ChangeEventHandler<HTMLTextAreaElement>;
}

export const TextAreaField: React.FC<TextAreaFieldProps> = ({
  label,
  name,
  placeholder,
  readOnly,
  labelCss,
  value,
  error,
  touched,
  handleChange,
  ...props
}) => {
  value = value || '';
  readOnly = readOnly || false;

  return (
    <div className={'form-group my-2'}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ''
      )}
      <textarea
        name={name}
        value={value}
        placeholder={placeholder}
        onChange={handleChange}
        readOnly={readOnly}
        className={'form-control form-control-lg'}
        rows={3}
      ></textarea>
      {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
    </div>
  );
};
