/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { TextField, TextFieldProps } from './TextField';

export default {
  title: 'Components/FormGenerator/TextField',
  component: TextField,
} as Meta;

const Template: Story<TextFieldProps> = (args) => <TextField {...args} />;

export const TextForm = Template.bind({});
TextForm.args = {
  name: 'name',
  label: 'Full name',
  placeholder: 'Enter Full Name',
  subType: 'text',
  value: '',
};
