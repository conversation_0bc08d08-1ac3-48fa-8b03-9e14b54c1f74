import React from 'react';
import styles from './TextField.module.css';
export interface TextFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The type field
   */
  subType?: string;
  /**
   * The place holder of the field
   */
  placeholder: string;
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The field is readonly
   */
  readOnly?: boolean;
  /**
   * The value object
   */
  value?: string | number | readonly string[];
  /**
   * The error handling object
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange: React.ChangeEventHandler<HTMLInputElement>;
  /**
   * The function to be called on blur
   */
  handleBlur: React.FocusEventHandler<HTMLInputElement>;
}

export const TextField: React.FC<TextFieldProps> = ({
  label,
  name,
  subType,
  placeholder,
  readOnly,
  labelCss,
  value,
  error,
  touched,
  handleChange,
  handleBlur,
  ...props
}) => {
  subType = subType || 'text';
  value = value || '';
  readOnly = readOnly || false;

  return (
    <div className={'form-group my-2'}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ''
      )}
      <input
        className={`form-control form-control-lg`}
        type={subType}
        name={name}
        placeholder={placeholder}
        value={value}
        readOnly={readOnly}
        onChange={handleChange}
        onBlur={handleBlur}
      />
      {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
    </div>
  );
};
