/* eslint-disable */
import React from 'react';
import { <PERSON>a, Story } from '@storybook/react';
import { HtmlContentDisplay, HtmlContentDisplayProps } from './HtmlContentDisplay';

export default {
  title: 'Components/FormGenerator/HtmlContentDisplay',
  component: HtmlContentDisplay,
} as Meta;

const Template: Story<HtmlContentDisplayProps> = (args) => <HtmlContentDisplay {...args} />;

export const HtmlContentDisplayForm = Template.bind({});
HtmlContentDisplayForm.args = {
  content: '',
};
