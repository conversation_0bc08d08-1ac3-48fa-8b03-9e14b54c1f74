import React from 'react';
import DOMPurify from 'dompurify';

export interface HtmlContentDisplayProps {
  /**
   * The HTML content to display
   */
  content: string;
  /**
   * Optional CSS class for styling the container
   */
  containerClass?: string;
}

// Hook to allow target="_blank" and ensure security with rel="noopener noreferrer"
DOMPurify.addHook('afterSanitizeAttributes', (node) => {
  if (node.tagName === 'A' && node.getAttribute('target') === '_blank') {
    node.setAttribute('rel', 'noopener noreferrer');
  }
});

export const HtmlContentDisplay: React.FC<HtmlContentDisplayProps> = ({ content, containerClass }) => {
  // Sanitize the content using DOMPurify
  // Added allowed tags and attribut to open privacy policy in new tab in auburn website
  const sanitizedContent = DOMPurify.sanitize(content, {
    ALLOWED_TAGS: ['a', 'div', 'span', 'p', 'strong'],
    ALLOWED_ATTR: ['href', 'style', 'target', 'rel'],
  });

  return (
    <div className={'form-group my-2'}>
      <div className={containerClass}>
        <div dangerouslySetInnerHTML={{ __html: sanitizedContent }} />
      </div>
    </div>
  );
};
