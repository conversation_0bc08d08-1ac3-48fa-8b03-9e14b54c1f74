import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { HtmlContentDisplay } from './HtmlContentDisplay';

const data = {
  content: '',
};

describe('<HtmlContentDisplay />', () => {
  test('it should mount', () => {
    render(<HtmlContentDisplay {...data} />);

    const htmlContentDisplay = screen.getByTestId('HtmlContentDisplay');

    expect(htmlContentDisplay).toBeInTheDocument();
  });
});
