import React from 'react';
import styles from './PhoneField.module.css';
import PhoneInput, { CountryData } from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import getUserLocale from 'get-user-locale';

export interface PhoneFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The type field
   */
  subType?: string;
  /**
   * The place holder of the field
   */
  placeholder?: string;
  /**
   * The label css
   */
  labelCss?: Record<string, string>;
  /**
   * The field is readonly
   */
  readOnly?: boolean;
  /**
   * The value object
   */
  value?: string | number | readonly string[];
  /**
   * The error handling object
   */
  allowCountries?: string[];

  defaultCountry?: string;
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange?: React.ChangeEventHandler<HTMLInputElement>;
  /**
   * The function to be called on blur
   */
  handleBlur?: React.FocusEventHandler<HTMLInputElement>;

  /**
   *
   */
  onHandleChange?: ({}) => void;
}

export const PhoneField: React.FC<PhoneFieldProps> = ({
  label,
  name,
  subType,
  placeholder,
  readOnly,
  labelCss,
  value,
  error,
  touched,
  allowCountries,
  defaultCountry,
  handleChange = () => {},
  handleBlur,
  onHandleChange = () => {},
  ...props
}) => {
  const data = props as any;
  const userLocale = getUserLocale();
  const allowCountriesValues = data?.configurations?.allowCountries || allowCountries || [];
  const defaultCountryValue =
    data?.configurations?.defaultCountry || defaultCountry || userLocale?.split('-')[1]?.toLowerCase();
  subType = subType || 'text';
  value = value || '';
  readOnly = readOnly || false;
  const onChange = (value: string, data: {} | CountryData, event: React.ChangeEvent<HTMLInputElement>) => {
    onHandleChange!({ value, data });
    handleChange!(event);
  };
  return (
    <div className={'form-group my-2'}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ''
      )}
      <PhoneInput
        inputProps={{
          name: name,
          id: name,
        }}
        inputClass={`${styles.phoneInput} ${styles.phoneInputformcontrollg}`}
        country={defaultCountryValue}
        value={`${value}`?.replace(/[^0-9]/g, '')}
        onlyCountries={allowCountriesValues}
        onChange={(value, data, event) => onChange(value, data, event)}
      />
      {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
    </div>
  );
};
