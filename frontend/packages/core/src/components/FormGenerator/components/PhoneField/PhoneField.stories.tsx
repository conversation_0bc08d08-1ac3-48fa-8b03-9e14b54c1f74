/* eslint-disable */
import React from 'react';
import { <PERSON>a, Story } from '@storybook/react';
import { PhoneField, PhoneFieldProps } from './PhoneField';

export default {
  title: 'Components/FormGenerator/PhoneField',
  component: PhoneField,
} as Meta;

const Template: Story<PhoneFieldProps> = (args) => <PhoneField {...args} />;

export const PhoneFieldForm = Template.bind({});
PhoneFieldForm.args = {
  name: 'phone',
  label: 'Phone number',
  subType: 'text',
  value: '380986554544',
  onHandleChange: (value) => {
    console.log(value);
  },
};
