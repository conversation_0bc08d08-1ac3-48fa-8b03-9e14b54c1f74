.errorText {
  color: red;
  font-size: 12px;
  padding-top: 0.3rem;
}
.phoneInput {
  display: block !important;
  width: 100% !important;
  padding: 0.375rem 0.75rem;
  font-size: 1rem !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  color: #212529 !important;
  background-color: #fff !important;
  background-clip: padding-box !important;
  border: 1px solid #ced4da !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0.25rem !important;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.phoneInputformcontrollg {
  min-height: calc(1.5em + 1rem + 2px);
  /* padding: 0.5rem 1rem; */
  font-size: 1.25rem !important;
  border-radius: 0.3rem;
}
