import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { PhoneField } from './PhoneField';

const data = {
  name: 'name',
  label: 'Full name',
  placeholder: 'Enter Full Name',
  subType: 'text',
  value: '',
  labelCss: {},
  handleBlur: function () {},
  handleChange: function () {},
};

describe('<PhoneField />', () => {
  test('it should mount', () => {
    render(<PhoneField {...data} />);

    const phoneField = screen.getByTestId('PhoneField');

    expect(phoneField).toBeInTheDocument();
  });
});
