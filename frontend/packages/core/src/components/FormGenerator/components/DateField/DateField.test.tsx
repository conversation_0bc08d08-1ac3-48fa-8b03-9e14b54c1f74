import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { DateField } from './DateField';

const data = {
  name: 'dob',
  label: 'Birthdate',
  readOnly: false,
  labelCss: {},
  handleBlur: function () {},
  setFieldValue: function () {},
  configurations: {},
};

describe('<DateField />', () => {
  test('it should mount', () => {
    render(<DateField {...data} />);

    const dateField = screen.getByTestId('DateField');

    expect(dateField).toBeInTheDocument();
  });
});
