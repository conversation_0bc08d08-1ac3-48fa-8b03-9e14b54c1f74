/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { DateField, DateFieldProps } from './DateField';

export default {
  title: 'Components/FormGenerator/DateField',
  component: DateField,
} as Meta;

const Template: Story<DateFieldProps> = (args) => <DateField {...args} />;

export const DateForm = Template.bind({});
DateForm.args = {
  name: 'dob',
  label: 'Birthdate',
  maxAllowedDays: 5,
  minAllowedDays: 5,
  showTimeSelect: true,
  showMonthSelect: true,
  showYearSelect: false,
};
