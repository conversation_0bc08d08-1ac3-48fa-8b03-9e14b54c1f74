import React, { useEffect } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import styles from './DateField.module.css';
import moment from 'moment';
import { size } from 'lodash';

export interface DateFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The value object
   */
  value?: Date | string;
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The field is readonly
   */
  readOnly: boolean;
  /**
   * The error handling object
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * The max buffer allowed for days to be selectable
   */
  maxAllowedDays?: number;
  /**
   * The max buffer allowed for days to be selectable
   */
  minAllowedDays?: number;
  /**
   * The time selection should be visible or not
   */
  showTimeSelect?: boolean;
  /**
   * The month selection should be visible or not
   */
  showMonthSelect?: boolean;
  /**
   * The year selection should be visible or not
   */
  showYearSelect?: boolean;
  /**
   * How to display the date
   */
  displayFormat?: string;
  /**
   * The interval of time which a user can select
   */
  timeIntervals?: number;

  /**
   *
   */
  isRequired?: boolean;
  /**
   * The function to be called on blur
   */
  handleBlur: React.FocusEventHandler<HTMLInputElement>;
  /**
   * The function to be called set value
   */
  setFieldValue: Function;
  /**
   * The configurations for location selector component
   */
  configurations: Record<string, any>;
  getDateTimeOnChange?: (val: Date) => void;
}

export const DateField: React.FC<DateFieldProps> = ({
  label,
  name,
  readOnly,
  value,
  labelCss,
  error,
  touched,
  timeIntervals,
  maxAllowedDays,
  minAllowedDays,
  showTimeSelect,
  isRequired,
  showMonthSelect,
  showYearSelect,
  displayFormat,
  handleBlur,
  setFieldValue,
  configurations,
  getDateTimeOnChange,
  ...props
}) => {
  maxAllowedDays = maxAllowedDays || 0;
  showTimeSelect = showTimeSelect !== undefined ? showTimeSelect : true;
  showMonthSelect = showMonthSelect !== undefined ? showMonthSelect : true;
  showYearSelect = showYearSelect !== undefined ? showYearSelect : true;
  timeIntervals = timeIntervals || 30;
  if (!displayFormat) {
    displayFormat = showTimeSelect ? 'ddd, MMM Do YYYY, h:mm a' : 'ddd, MMM Do YYYY';
  }
  const maxAllowedDate = maxAllowedDays ? addDays(new Date(), maxAllowedDays) : new Date();
  const minAllowedDate =
    minAllowedDays && minAllowedDays >= 0 ? addDays(new Date(), -Math.abs(minAllowedDays)) : new Date();

  useEffect(() => {
    if (size(value) > 0) {
      if (configurations?.isEditMode) {
        value = value ? moment(value).format(displayFormat) : new Date().toString();
      } else {
        value =
          value || new Date(maxAllowedDate) < new Date() ? new Date(maxAllowedDate).toString() : new Date().toString();
        value = showTimeSelect ? new Date(value).toString() : new Date(value).toDateString();
        setFieldValue(name, new Date(value));
      }
    }
  }, []);

  function addDays(date: Date, days: number) {
    var result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  function calculateMinTime(date: Date) {
    let isToday = moment(date).isSame(moment(), 'day');
    if (isToday) {
      let nowAddOneHour = moment(new Date()).toDate();
      return nowAddOneHour;
    }
    return moment().startOf('day').toDate();
  }

  function setDate(date: any) {
    setFieldValue(name, new Date(date));
    value = moment(date).format(displayFormat);
    getDateTimeOnChange!(new Date(date));
  }

  return (
    <div className={'form-group my-2'}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ''
      )}
      <br />
      <div className={`form-control form-control-lg`}>
        {/* @ts-ignore - Temporary fix for React types conflict between react-datepicker and @testing-library/react */}
        <DatePicker
          name={name}
          wrapperClassName={styles.datePickerWrapper}
          className={styles.customDatePickerWidth}
          selected={value ? new Date(moment(value).format()) : null}
          value={value ? moment(value).format(displayFormat) : undefined}
          readOnly={readOnly}
          onBlur={handleBlur}
          maxDate={maxAllowedDate}
          minDate={minAllowedDate}
          required={isRequired}
          minTime={value ? calculateMinTime(new Date(value)) : new Date()}
          maxTime={moment().endOf('day').toDate()}
          timeFormat="hh:mm aa"
          timeIntervals={timeIntervals}
          showTimeSelect={showTimeSelect}
          dropdownMode="select"
          showMonthDropdown={showMonthSelect}
          showYearDropdown={showYearSelect}
          timeCaption="Time"
          dateFormat={displayFormat}
          onChange={(val) => {
            setDate(val);
          }}
        />
      </div>
      {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
    </div>
  );
};
