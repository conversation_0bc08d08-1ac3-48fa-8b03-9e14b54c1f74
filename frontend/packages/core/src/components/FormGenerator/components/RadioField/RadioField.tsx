import React, { useEffect, useState } from 'react';
// import './RadioField.module.css';
import styles from './RadioField.module.css';
export interface RadioFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The selectable options
   */
  options: any[];
  /**
   * The value object
   */
  value?: any;
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The field is readonly
   */
  readOnly: boolean;
  /**
   * The error handling of data
   */
  error?: Record<string, any>;
  /**
   * The touch handling of data
   */
  touched?: Record<string, any>;
  /**
   * The function to be called on change
   */
  handleChange: React.ChangeEventHandler<HTMLInputElement>;
  /**
   * The function to be called on blur
   */
  handleBlur: React.FocusEventHandler<HTMLInputElement>;
  /**
   * The function to be called set value
   */
  setFieldValue: Function;
}

export const RadioField: React.FC<RadioFieldProps> = ({
  label,
  name,
  options,
  labelCss,
  error,
  touched,
  readOnly,
  handleChange,
  handleBlur,
  setFieldValue,
  value,
  ...props
}) => {
  const [selectedItem, setSelectedItem] = useState<string | undefined | null>(null);

  useEffect(() => {
    if (value) {
      setSelectedItem(value);
    }
  }, []);

  const fieldName = name;
  readOnly = readOnly || false;

  const handleSelectItem = (event: any) => {
    const { name, value } = event.target;
    setSelectedItem(name);
    setFieldValue(fieldName, value.id);
  };

  return (
    <div className={'form-group my-2'}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ''
      )}
      <div className={'form-radio row'}>
        {options.map((opt, index) => {
          return (
            <div className={'radiobox col-6'}>
              <label
                className={`form-radio-label ${styles.formRadioLabel}`}
                key={index}
                htmlFor={`${fieldName}-${index}`}
              >
                <input
                  type="radio"
                  name={`${fieldName}-${index}`}
                  className={'form-select-input'}
                  value={JSON.stringify(opt)}
                  readOnly={readOnly}
                  disabled={readOnly}
                  onBlur={handleBlur}
                  checked={selectedItem ? selectedItem === `${fieldName}-${index}` : opt.checked}
                  onChange={handleSelectItem}
                />
                {opt.name}
              </label>
            </div>
          );
        })}
        {error && touched && touched[name] && <div className={styles.errorText}> {error} </div>}
      </div>
    </div>
  );
};
