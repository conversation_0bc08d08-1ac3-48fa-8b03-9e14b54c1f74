/* eslint-disable */
import React from 'react';
import { <PERSON><PERSON>, Story } from '@storybook/react';
import { RadioField, RadioFieldProps } from './RadioField';

export default {
  title: 'Components/FormGenerator/RadioField',
  component: RadioField,
} as Meta;

const Template: Story<RadioFieldProps> = (args) => <RadioField {...args} />;

export const RadioForm = Template.bind({});
RadioForm.args = {
  name: 'name',
  label: 'Full name',
  value: '',
  options: [
    { id: 1, name: 'K' },
    { id: 2, name: 'A' },
    { id: 3, name: 'V' },
    { id: 4, name: 'I' },
    { id: 5, name: 'S' },
    { id: 6, name: 'H' },
  ],
};
