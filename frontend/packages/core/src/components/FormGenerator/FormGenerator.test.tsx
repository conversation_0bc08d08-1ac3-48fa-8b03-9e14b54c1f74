import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import 'bootstrap/dist/css/bootstrap.min.css';
import { FormGenerator } from './FormGenerator';

export const data = [
  {
    id: 'name',
    label: 'Full name',
    placeholder: 'Enter full name',
    type: 'text',
    validationType: 'string',
    value: '',
    validations: [
      {
        type: 'required',
        params: ['name is required'],
      },
      {
        type: 'min',
        params: [5, 'Nannot be less than 5 characters'],
      },
      {
        type: 'max',
        params: [10, 'Name cannot be more than 10 characters'],
      },
    ],
  },
];

export const FormGeneratorData = {
  fields: { ...data },
  formikProps: {},
  labelColor: {},
  backgroundColor: {},
  buttonColor: {},
};

describe('<FormGenerator />', () => {
  test('it should mount', () => {
    render(<FormGenerator {...FormGeneratorData} />);

    const formGenerator = screen.getByTestId('FormGenerator');

    expect(formGenerator).toBeInTheDocument();
  });
});
