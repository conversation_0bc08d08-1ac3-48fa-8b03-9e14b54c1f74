/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { UserCard, UserCardProps } from './UserCard';

export default {
  title: 'Components/UserCard',
  component: UserCard,
} as Meta;

const Template: Story<UserCardProps> = (args) => <UserCard {...args} />;

export const UserCardBox = Template.bind({});
UserCardBox.args = {
  id: '123',
  familyName: '<PERSON><PERSON>',
  givenName: '<PERSON>',
  email: '<EMAIL>',
  contactNumber: '1234567890',
};
