import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { UserCard } from './UserCard';

const data = {
  id: '123',
  familyName: '<PERSON><PERSON>',
  givenName: '<PERSON>',
  email: '<EMAIL>',
  contactNumber: '1234567890',
};

describe('<UserCard />', () => {
  test('it should mount', () => {
    render(<UserCard {...data} />);

    const userCard = screen.getByTestId('UserCard');

    expect(userCard).toBeInTheDocument();
  });
});
