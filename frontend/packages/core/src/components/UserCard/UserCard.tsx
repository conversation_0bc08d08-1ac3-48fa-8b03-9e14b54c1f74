import React from 'react';
import { Col, Row } from 'react-bootstrap';
import styles from './UserCard.module.css';

export interface UserCardProps {
  /**
   * The id of user
   */
  id?: string;
  /**
   * The family name to be displayed
   */
  familyName: string;
  /**
   * The given name to be displayed
   */
  givenName: string;
  /**
   * The email to be displayed
   */
  email?: string;
  /**
   * The contact number to be displayed
   */
  contactNumber?: string;
  /**
   * The profile picture to be displayed
   */
  profilePicture?: string;
}

export const UserCard: React.FC<UserCardProps> = ({
  id,
  givenName,
  familyName,
  email,
  contactNumber,
  profilePicture,
  ...props
}) => {
  const image = profilePicture || '';

  return (
    <Row className={`${styles.rowDisplay} mx-1 my-1`}>
      <div className={styles.card}>
        <Col
          sm={12}
          md={4}
          key={id || Math.random().toString()}
          className={`${styles.colStyle} d-inline-flex justify-content-center`}
        >
          <div className={styles.avatar}>
            <img src={image} className={`card-img-top ${styles.avatar}`} alt="UserImage" />
          </div>
        </Col>
        <Col sm={12} md={4} className={styles.colStyle}>
          <h4>
            {givenName} {familyName}
          </h4>
          <h5 className={styles.cardText}>
            {email}
            <br />
            <span>{contactNumber}</span>
          </h5>
        </Col>
      </div>
    </Row>
  );
};
