.errorHandlerOnComponent {
  color: #f2f2f2;
  text-align: center;
}

.errorHandlerOnFullScreen {
  color: #f2f2f2;
  text-align: center;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  line-height: 2.25rem;
}

.errorCode {
  font-size: 10rem;
}

.errorMessage {
  color: #999999;
  font-size: 1rem;
  margin-bottom: 1rem;
  padding-left: 2rem;
  padding-right: 2rem;
}

.tryAgainButton {
  padding: 0.5rem 3.5rem;
  background-color: var(--bg-primary, #3b8396);
  color: var(--text-primary, #ffffff);
  border-color: var(--bg-secondary, #2a5e6c);
}

.tryAgainButton:hover {
  border-color: var(--bg-primary, #3b8396);
  background-color: var(--bg-secondary, #2a5e6c);
}
