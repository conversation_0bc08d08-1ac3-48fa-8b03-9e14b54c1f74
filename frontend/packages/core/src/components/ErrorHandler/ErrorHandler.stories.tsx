/* eslint-disable */
import React from 'react';
import { <PERSON>a, Story } from '@storybook/react';
import { <PERSON>rror<PERSON>andler, ErrorHandlerProps } from './ErrorHandler';

export default {
  title: 'Components/ErrorHandler',
  component: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} as Meta;

const Template: Story<ErrorHandlerProps> = (args) => <ErrorHandler {...args} />;

export const ErrorHandlerComponent = Template.bind({});
ErrorHandlerComponent.args = { errorCode: '404', errorMsg: 'No data found' };
