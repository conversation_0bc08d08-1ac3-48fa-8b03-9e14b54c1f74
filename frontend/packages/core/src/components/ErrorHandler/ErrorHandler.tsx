import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Col } from 'react-bootstrap';
import styles from './ErrorHandler.module.css';

export interface ErrorHandlerProps {
  /**
   * The error code of error
   */
  errorCode?: string;
  /**
   * The error message of error
   */
  errorMsg?: string;
  /**
   * The show try again button is required
   */
  showTryAgainButton?: boolean;
  /**
   * Should the component occupy full screen
   */
  occupyFullScreen?: boolean;
}

export const ErrorHandler: React.FC<ErrorHandlerProps> = ({
  errorCode,
  errorMsg,
  showTryAgainButton,
  occupyFullScreen,
  ...props
}) => {
  errorCode = errorCode || '500';
  errorMsg = errorMsg || 'Something went wrong. Please Try Again!';
  showTryAgainButton = showTryAgainButton || false;
  occupyFullScreen = occupyFullScreen || false;

  function refreshPage() {
    window.location.reload();
  }

  return (
    <div
      data-testid="ErrorHandler"
      className={occupyFullScreen ? styles.errorHandlerOnFullScreen : styles.errorHandlerOnComponent}
      {...props}
    >
      <Row>
        <Col sm={12}>
          <span className={styles.errorCode}>{errorCode}</span>
        </Col>
        <Col sm={12}>
          <div className={styles.errorMessage}>
            <span>{errorMsg}</span>
          </div>
        </Col>
        {showTryAgainButton ? (
          <Col sm={12}>
            <Button size="sm" id="tryAgainClick" type="submit" className={styles.tryAgainButton} onClick={refreshPage}>
              <span>Try Again</span>
            </Button>
          </Col>
        ) : (
          ''
        )}
      </Row>
    </div>
  );
};
