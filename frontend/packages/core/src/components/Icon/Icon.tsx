import React from 'react';
import { IconBaseProps } from 'react-icons';
import * as ReactIcons from 'react-icons/fa';
import styles from './Icon.module.css';

export interface IconProps {
  /**
   * The icon to be displayed
   */
  iconName: keyof typeof ReactIcons;
  /**
   * Icon color for the icon
   */
  iconColor?: string;
  /**
   * Icon label next to the icon
   */
  label?: string;
  /**
   * Font color for the label
   */
  fontColor?: string;
  /**
   * Optional click handler
   */
  onClick?: () => void;
}

export const Icon: React.FC<IconProps> = ({ iconName, iconColor, fontColor, label, ...props }) => {
  const icon: React.FunctionComponentElement<IconBaseProps> = React.createElement(ReactIcons[iconName]);
  const iconStyle: any = iconColor ? { color: iconColor } : { color: 'inherit' };
  const labelStyle: any = fontColor ? { color: fontColor } : { color: 'inherit' };

  return (
    <div data-testid="Icon" {...props}>
      <i style={iconStyle} className={styles.size}>
        {icon}
      </i>
      <span className={styles.ml3} style={labelStyle}>
        {label ? label : ''}
      </span>
    </div>
  );
};
