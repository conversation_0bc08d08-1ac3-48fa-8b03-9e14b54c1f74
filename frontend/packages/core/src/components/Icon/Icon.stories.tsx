/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { Icon, IconProps } from './Icon';

export default {
  title: 'Components/Icon',
  component: Icon,
} as Meta;

const Template: Story<IconProps> = (args) => <Icon {...args} />;

export const ReactIcon = Template.bind({});
ReactIcon.args = { label: 'Promotions', iconName: 'FaBeer', iconColor: 'orange', fontColor: 'blue' };
