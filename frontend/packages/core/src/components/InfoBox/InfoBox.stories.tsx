/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { InfoBox, InfoBoxProps } from './InfoBox';

export default {
  title: 'Components/InfoBox',
  component: InfoBox,
} as Meta;

const Template: Story<InfoBoxProps> = (args) => <InfoBox {...args} />;

export const DescriptionBox = Template.bind({});
DescriptionBox.args = {
  text: 'This is where you can view and manage client details',
  iconName: 'FaBeer',
  background: 'red',
  iconColor: 'yellow',
  fontColor: 'green',
};
