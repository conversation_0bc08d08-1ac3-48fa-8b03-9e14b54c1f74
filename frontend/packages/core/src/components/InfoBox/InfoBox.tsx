import React from 'react';
import { Alert } from 'react-bootstrap';
import * as ReactIcons from 'react-icons/fa';
import { Icon } from '../Icon/Icon';
import styles from './InfoBox.module.css';

export interface InfoBoxProps {
  /**
   * The icon to be displayed
   */
  iconName: keyof typeof ReactIcons;
  /**
   * The font color of text
   */
  iconColor?: string;
  /**
   * The descriptive message of the info box
   */
  text: string;
  /**
   * The color of the box
   */
  background?: string;
  /**
   * The font color of text
   */
  fontColor?: string;
  /**
   * The opacity of color
   */
  opacity?: string;
  /**
   * Add margin to the box
   */
  noGutter?: boolean;
}

export const InfoBox: React.FC<InfoBoxProps> = ({
  text,
  iconName,
  iconColor,
  fontColor,
  background,
  noGutter,
  opacity,
  ...props
}) => {
  let bgColor = background;
  if (background && /^#[0-9A-F]{6}$/i.test(background)) {
    bgColor = opacity ? `${background}${opacity}` : `${background}0D`;
  }
  let boxStyle: any = background ? { background: bgColor } : { background: 'inherit' };
  boxStyle = noGutter === true ? { ...boxStyle } : { ...boxStyle };

  return (
    <Alert data-testid="InfoBox" style={boxStyle} className={styles.infoBox}>
      <Icon iconName={iconName} label={text} iconColor={iconColor} fontColor={fontColor} />
    </Alert>
  );
};
