import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { MediaViewer } from './MediaViewer';

const data = {
  items: [
    {
      original: 'https://picsum.photos/id/1018/1000/600/',
      thumbnail: 'https://picsum.photos/id/1018/250/150/',
    },
    {
      original: 'https://picsum.photos/id/1015/1000/600/',
      thumbnail: 'https://picsum.photos/id/1015/250/150/',
    },
    {
      original: 'https://picsum.photos/id/1019/1000/600/',
      thumbnail: 'https://picsum.photos/id/1019/250/150/',
    },
  ],
};

describe('<MediaViewer />', () => {
  test('it should mount', () => {
    render(<MediaViewer {...data} />);

    const mediaViewer = screen.getByTestId('MediaViewer');

    expect(mediaViewer).toBeInTheDocument();
  });
});
