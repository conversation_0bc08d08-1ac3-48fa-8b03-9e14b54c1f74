/* eslint-disable */
import React from 'react';
import { <PERSON><PERSON>, Story } from '@storybook/react';
import { MediaViewer, MediaViewerProps } from './MediaViewer';

export default {
  title: 'Components/MediaViewer',
  component: MediaViewer,
} as Meta;

const Template: Story<MediaViewerProps> = (args) => <MediaViewer {...args} />;

export const MediaViewerBox = Template.bind({});
MediaViewerBox.args = {
  items: [
    {
      original: 'https://picsum.photos/id/1018/1000/600/',
      thumbnail: 'https://picsum.photos/id/1018/250/150/',
      description: 'test 1',
    },
    {
      original: 'https://picsum.photos/id/1015/1000/600/',
      thumbnail: 'https://picsum.photos/id/1015/250/150/',
      description: 'test 2',
    },
    {
      original: 'https://picsum.photos/id/1019/1000/600/',
      thumbnail: 'https://picsum.photos/id/1019/250/150/',
      description: 'test 3',
    },
  ],
  autoPlay: false,
  lazyLoad: true,
  showIndex: true,
  useTranslate3D: true,
};
