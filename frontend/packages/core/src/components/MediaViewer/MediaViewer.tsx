import React from 'react';
import ImageGallery, { ReactImageGalleryItem } from 'react-image-gallery';
import 'react-image-gallery/styles/css/image-gallery.css';

export interface MediaViewerProps {
  items: ReadonlyArray<ReactImageGalleryItem>;
  autoPlay?: boolean | undefined;
  lazyLoad?: boolean | undefined;
  infinite?: boolean | undefined;
  showIndex?: boolean | undefined;
  useTranslate3D?: boolean | undefined;
}

export const MediaViewer: React.FC<MediaViewerProps> = ({ ...props }) => {
  const images = props.items;
  {/* @ts-ignore - Temporary fix for /React types conflict between react-image-gallery and @testing-library/react */}
  return <ImageGallery {...props} items={images} showBullets={true} showPlayButton={false} disableKeyDown={false} />;
};
