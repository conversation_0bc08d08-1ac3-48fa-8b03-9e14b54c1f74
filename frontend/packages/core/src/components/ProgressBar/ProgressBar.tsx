import React from 'react';
import Loader, { LoaderProps } from 'react-loader-spinner';
import styles from './ProgressBar.module.css';

export interface ProgressBarProps extends LoaderProps {
  /**
   * The overlay background color to be displayed
   */
  background?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({ background, ...props }) => {
  const progressOverlayColor: any = background ? { background } : { background: 'inherit' };

  return (
    <div data-testid="ProgressBar" className={styles.progressOverlay} style={progressOverlayColor}>
      <div className={styles.progressBarCenterLayout}>
          {/* @ts-ignore - Temporary fix for /React types conflict between react-loader-spinner and @testing-library/react */}
        <Loader {...props} />
      </div>
    </div>
  );
};
