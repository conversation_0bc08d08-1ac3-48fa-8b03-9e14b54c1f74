/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { ProgressBar, ProgressBarProps } from './ProgressBar';

export default {
  title: 'Components/ProgressBar',
  component: ProgressBar,
} as Meta;

const Template: Story<ProgressBarProps> = (args) => <ProgressBar {...args} />;

export const Loader = Template.bind({});
Loader.args = { type: 'Puff', color: 'red', height: 100, width: 40, background: 'orange' };
