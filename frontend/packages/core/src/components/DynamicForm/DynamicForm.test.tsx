import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { DynamicForm } from './DynamicForm';

export const data = [
  {
    id: 'name',
    label: 'Full name',
    placeholder: 'Enter full name',
    type: 'text',
    validationType: 'string',
    value: '',
    validations: [
      {
        type: 'required',
        params: ['name is required'],
      },
      {
        type: 'min',
        params: [5, 'Name cannot be less than 5 characters'],
      },
      {
        type: 'max',
        params: [10, 'Name cannot be more than 10 characters'],
      },
    ],
  },
];

describe('<DynamicForm />', () => {
  test('it should mount', () => {
    render(
      <DynamicForm fields={data} onSubmit={(data: any) => new Function()} onReset={(data: any) => new Function()} />,
    );

    const dynamicForm = screen.getByTestId('DynamicForm');

    expect(dynamicForm).toBeInTheDocument();
  });
});
