import React, { useRef } from 'react';
import { Formik } from 'formik';
import { Col, Row, Button } from 'react-bootstrap';
import * as yup from 'yup';
import { createYupSchema } from './components/YupSchema.js';
import FormGenerator from '../FormGenerator/FormGenerator.lazy';
import PageHeader from '../PageHeader/PageHeader.lazy';
import styles from './DynamicForm.module.css';

export const FIELD_TYPES = [
  'text',
  'select',
  'textarea',
  'radio',
  'checkbox',
  'upload',
  'date',
  'time',
  'html',
  'location',
  'asyncSelect',
];

export const VALIDATION_TYPES = ['string', 'number', 'boolean', 'date', 'array', 'object'];
export interface ThemeProps {
  /**
   * The background color of form;
   */
  backgroundColor: string;
  /**
   * The form title color of form;
   */
  formTitleColor: string;
  /**
   * The field title color of form;
   */
  fieldTitleColor: string;
  /**
   * The button primary color of form;
   */
  buttonPrimaryColor: string;
}
export interface DynamicFormProps {
  /**
   * The field whose form needs to be created
   */
  fields: Record<string, any>[];
  /**
   * The color theme
   */
  colorTheme?: ThemeProps;
  /**
   * The title to be shown on top
   */
  title?: string;
  /**
   * The space between form field and submit button
   */
  spacer?: number;

  buttonPosition?: boolean;

  /**
   * The label to be shown on submit
   */
  submitLabel?: string;
  /**
   * The label to be shown on cancel
   */
  cancelLabel?: string;
  /**
   * The cancel button should be shown or not
   */
  disableCancelButton?: boolean;
  /**
   * The API configurations to be used
   */
  apiConfigurations?: Record<string, string>;
  /**
   * The Action CSS configurations to be used
   */
  actionItemStyle?: Record<string, string>;

  showRequiredIcon?: boolean;
  /**
   * The on submit button callback
   */
  onSubmit: (data: any, actions?: any) => void;
  /**
   * The on reset button callback
   */
  onReset: (e: any) => void;
  /**
   * The callback to be called when the form is updated or changed
   */
  onHandleChange?: (e: any) => void;
}

export const DynamicForm: React.FC<DynamicFormProps> = ({
  fields,
  title,
  colorTheme,
  apiConfigurations,
  submitLabel,
  cancelLabel,
  buttonPosition,
  disableCancelButton,
  spacer,
  actionItemStyle,
  onSubmit,
  onReset,
  onHandleChange = (e: any) => {},
  ...props
}) => {
  let formRef = useRef() as any;
  const initialValues: any = {};
  submitLabel = submitLabel || 'SUBMIT';
  cancelLabel = cancelLabel || 'CANCEL';
  disableCancelButton = disableCancelButton || false;
  const backgroundColor = colorTheme?.backgroundColor
    ? { background: colorTheme.backgroundColor }
    : { background: 'inherit' };
  const buttonColor = colorTheme?.buttonPrimaryColor
    ? { background: colorTheme.buttonPrimaryColor }
    : { background: 'inherit' };
  const fieldTitleColor = colorTheme?.fieldTitleColor
    ? { color: colorTheme.fieldTitleColor, marginBottom: '0.25rem' }
    : { color: 'inherit' };
  const spacerStyle = spacer ? { height: `${spacer}rem` } : { height: '10rem' };
  // const positionStyle = buttonPosition ? { position: 'fixed' as 'fixed' } : { position: 'unset' as 'unset' };

  fields.forEach((item: any) => {
    initialValues[item.id] = item.value || '';
  });

  const yupSchema = fields.reduce(createYupSchema, {});

  const validateSchema = yup.object().shape(yupSchema);

  if (props.showRequiredIcon) {
    document.head.insertAdjacentHTML('beforeend', `<style>.required:after {content:" *";color:red}</style>`);
    const validateFields = validateSchema.fields;
    Object.keys(validateFields).forEach(function (key) {
      const field = validateFields[key];
      if (field && field.exclusiveTests && field.exclusiveTests.required) {
        setTimeout(() => {
          const label = findByAttributeValue('for', key, 'label') as HTMLElement;
          label.classList.add('required');
        }, 500);
      }
    });
  }
  function findByAttributeValue(attribute: string, value: string, elementType: string) {
    elementType = elementType || '*';
    let labels = document.getElementsByTagName(elementType);
    for (let i = 0; i < labels.length; i++) {
      if (labels[i].getAttribute(attribute) == value) {
        return labels[i];
      }
    }
  }

  return (
    <div style={backgroundColor}>
      <Formik
        initialValues={initialValues}
        validationSchema={validateSchema}
        onSubmit={(values, action) => onSubmit(values, action)}
      >
        {(formikProps) => (
          <form
            onSubmit={(event) => {
              event.preventDefault();
              formikProps.handleSubmit();
            }}
            className={`m-2 form-margin-bottom ${styles.dynamicFromContainer}`}
            id="dynamicForm"
            ref={formRef}
          >
            {title ? <h2 className={`${styles.formTitle} pb-3`}>{title} </h2> : ''}
            <FormGenerator
              fields={fields}
              labelColor={fieldTitleColor}
              backgroundColor={backgroundColor}
              buttonColor={buttonColor}
              formikProps={formikProps}
              apiConfigurations={apiConfigurations}
              onHandleChange={onHandleChange}
            />
            {/* <Row style={spacerStyle}></Row> */}
            <Row className={`${styles.alignItems} py-3`} style={{ ...actionItemStyle }}>
              <Col xs={disableCancelButton ? 12 : 5}>
                <Button size="sm" block id="submitClick" type="submit" className={`${styles.submitButton}`}>
                  {submitLabel}
                </Button>
              </Col>
              {!disableCancelButton ? (
                <Col xs={5}>
                  <Button
                    size="sm"
                    block
                    id="resetClick"
                    type="button"
                    onClick={(e) => onReset(e)}
                    className={`${styles.resetButton}`}
                  >
                    {cancelLabel}
                  </Button>
                </Col>
              ) : (
                ''
              )}
            </Row>
          </form>
        )}
      </Formik>
    </div>
  );
};
