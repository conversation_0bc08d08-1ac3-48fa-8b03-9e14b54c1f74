.dynamicFromContainer {
  height: calc(100vh - 15vh);
  overflow-y: auto;
  padding-bottom: 60px;
  overflow-x: clip;
}

.alignItems {
  bottom: 0rem;
  background-color: var(--primary, #ffffff);
  position: absolute;
  z-index: 999;
  width: 80%;
}
@media only screen and (min-width: 992px) {
  .alignItems {
    width: 65%;
  }
}

.division {
  height: 10rem;
}

.formTitle {
  color: var(--bg-primary, #3b8396);
  border-bottom: 1px solid #00000014;
}

.submitButton {
  text-transform: uppercase;
  padding: 0.5rem;
  width: 100%;
  background-color: var(--bg-primary, #3b8396) !important;
  color: var(--text-primary, #ffffff);
  border-color: var(--bg-secondary, #2a5e6c) !important;
}

.submitButton:hover {
  background-color: var(--bg-secondary, #2a5e6c) !important;
  color: var(--text-primary, #ffffff) !important;
  border-color: var(--bg-primary, #3b8396) !important;
}

.resetButton {
  padding: 0.5rem;
  text-transform: uppercase;
  width: 100%;
  background-color: var(--primary, #ffffff) !important;
  color: var(--text-secondary, #3b8396) !important;
  border-color: var(--bg-secondary, #2a5e6c) !important;
}

.resetButton:hover {
  background-color: var(--bg-secondary, #2a5e6c) !important;
  color: var(--text-primary, #ffffff) !important;
  border-color: var(--bg-primary, #3b8396) !important;
}
