/* eslint-disable */
import React from 'react';
import { <PERSON>a, Story } from '@storybook/react';
import { DynamicForm, DynamicFormProps } from './DynamicForm';

const textData = [
  {
    id: 'name',
    label: 'Full name',
    placeholder: 'Enter Full name',
    type: 'text',
    subType: 'text',
    validationType: 'string',
    value: '',
  },
  {
    id: 'givenName',
    label: 'First name',
    placeholder: 'Enter First name',
    type: 'text',
    subType: 'text',
    validationType: 'string',
    value: '',
    validations: [
      {
        type: 'required',
        params: ['First Name is required'],
      },
      {
        type: 'min',
        params: [5, 'First Name cannot be less than 5 characters'],
      },
      {
        type: 'max',
        params: [10, 'First Name cannot be more than 10 characters'],
      },
    ],
  },
  {
    id: 'familyName',
    label: 'Last name',
    placeholder: 'Enter Last Name',
    type: 'text',
    subType: 'password',
    validationType: 'string',
    value: '',
    validations: [
      {
        type: 'required',
        params: ['Last Name is required'],
      },
      {
        type: 'min',
        params: [5, 'Last Name cannot be less than 5 characters'],
      },
      {
        type: 'max',
        params: [10, 'Last Name cannot be more than 10 characters'],
      },
    ],
  },
];

const textAreaData = [
  {
    id: 'address',
    label: 'Address',
    placeholder: 'Enter Address',
    type: 'textArea',
    validationType: 'string',
    value: '',
    validations: [
      {
        type: 'required',
        params: ['Address is required'],
      },
      {
        type: 'min',
        params: [50, 'Address cannot be less than 50 characters'],
      },
      {
        type: 'max',
        params: [100, 'Address cannot be more than 100 characters'],
      },
    ],
  },
];

const selectData = [
  {
    id: 'city',
    label: 'City Address',
    placeholder: 'Please Select',
    type: 'select',
    validationType: 'string',
    value: '',
    options: [
      { id: '123', name: 'Batam' },
      { id: '456', name: 'Jakarta' },
      { id: '789', name: 'Bandung' },
    ],
    validations: [
      {
        type: 'required',
        params: ['city address is required'],
      },
    ],
  },
];

const checkboxData = [
  {
    id: 'hobbies',
    label: 'Hobbies',
    placeholder: '',
    type: 'checkbox',
    validationType: 'array',
    value: '',
    options: [
      { id: 1, name: 'Playing Football' },
      { id: 2, name: 'Online Games' },
      { id: 3, name: 'Travelling' },
    ],
    validations: [
      {
        type: 'min',
        params: [1, 'Atleast 1 hobby needs to be selected'],
      },
    ],
  },
];

const radioData = [
  {
    id: 'gender',
    label: 'Gender',
    placeholder: '',
    type: 'radio',
    validationType: 'number',
    value: '1',
    options: [
      { id: 1, name: 'Male' },
      { id: 2, name: 'Female' },
    ],
    validations: [
      {
        type: 'min',
        params: [1, 'Atleast 1 gender needs to be selected'],
      },
    ],
  },
];

const uploadData = [
  {
    id: 'photo',
    label: 'Photo',
    placeholder: '',
    type: 'upload',
    validationType: 'mixed',
    validations: [
      {
        type: 'required',
        params: ['Photo is required'],
      },
    ],
    configurations: {
      style: {
        backgroundColor: '#f7f7f7',
        width: '100px',
        height: '100px',
        borderRadius: '50%',
        borderStyle: 'none',
      },
      circle: true,
      iconColor: '#CCC',
    },
  },
  {
    id: 'photo1',
    label: 'Mobile banner image',
    placeholder: '',
    type: 'upload',
    validationType: 'mixed',
    validations: [
      {
        type: 'required',
        params: ['Photo is required'],
      },
    ],
    configurations: {
      style: {
        backgroundColor: '#f7f7f7',
        width: '200px',
        height: '100px',
        borderRadius: '4px',
        borderStyle: 'dashed',
      },
      circle: false,
      iconColor: '#CCC',
      uploadIconType: 'FaPlus',
    },
  },
  {
    id: 'location',
    type: 'locationV2',
    name: 'your-location',
    label: 'Your location',
    placeholder: 'Enter your location 4',
    apiKey: '',
    configurations: { componentRestrictions: { country: ['US'] } },
  },
];

const dateData = [
  {
    id: 'scheduledAt',
    label: 'Scheduled For',
    placeholder: 'Enter Scheduled Time',
    type: 'date',
    showTimeSelect: false,
    maxAllowedDays: -1,
    minAllowedDays: 60,
    validationType: 'date',
    value: '',
    validations: [
      {
        type: 'required',
        params: ['Scheduled time is required'],
      },
    ],
  },
];

const timeData = [
  {
    id: 'openTime',
    label: 'Open Time',
    type: 'time',
    validationType: 'string',
    use12Hours: true,
    validations: [
      {
        type: 'required',
        params: ['Open Time is required'],
      },
    ],
  },
];

const htmlContentData = [
  {
    id: 'content',
    content: `
      <div style="font-size: 14px; color: #555; line-height: 1.5;">
      <p>
      <strong style="color: red">Disclaimer:</strong> By providing my contact information to 
      <span style="font-weight: bold;">Clevealnd</span>, I acknowledge and give my explicit consent to be contacted via SMS and receive emails for various purposes, which may include marketing and promotional content.
      Refer to our <a href="https://www.termsfeed.com/live/5c9dc56c-5516-4edf-8315" target="_blank" style="color: #1498D5; text-decoration: none;">Privacy Policy</a> 
      for more information.
      </p>
      </div>
    `,
    type: 'htmlContent',
  },
];

const htmlData = [
  {
    id: 'content',
    label: 'Post',
    type: 'html',
    validationType: 'string',
    validations: [
      {
        type: 'required',
        params: ['Post is required'],
      },
    ],
  },
];

const locationData = [
  {
    id: 'location',
    type: 'location',
    name: 'your location',
    label: 'Your location',
    placeholder: 'Enter your location',
    apiKey: '',
  },
];

const locationDataV2 = [
  {
    id: 'location',
    type: 'locationV2',
    name: 'your location',
    label: 'Your location',
    placeholder: 'Enter your location',
    apiKey: '',
    value: {
      addressLine1: 'Texas, USA',
      country: 'US',
      latitude: 31.9685988,
      longitude: -99.9018131,
      state: 'TX',
    },
    onChange: (value: any) => console.log(value),
    configurations: { componentRestrictions: { country: ['US'] } },
  },
];

const switchData = [
  {
    id: 'content',
    label: 'Post',
    type: 'switch',
    validationType: 'boolean',
    validations: [
      {
        type: 'required',
        params: ['Post is required'],
      },
    ],
  },
];

const AsyncSelectData = [
  {
    id: 'asyncData',
    label: 'Select City',
    type: 'asyncSelect',
    validationType: 'string',
    validations: [
      {
        type: 'required',
        params: ['City is required'],
      },
    ],
    options: [
      { id: '123', name: 'Batam' },
      { id: '456', name: 'Jakarta' },
      { id: '789', name: 'Bandung' },
    ],
    searchable: false,
  },
];

const infoBoxData = [
  {
    id: 'info',
    value:
      'Shayne Smith referred Michael Benjamin. Both will get 100 loyalty points when you add points for Michael Benjamin',
    type: 'infoBox',
    validations: [],
  },
];

const testData = [
  {
    id: 'givenName',
    label: 'First name',
    placeholder: 'Enter First name',
    type: 'text',
    subType: 'text',
    validationType: 'string',
    value: 'Kavish',
    validations: [
      {
        type: 'required',
        params: ['First name is required'],
      },
      {
        type: 'max',
        params: [20, 'First Name cannot be more than 20 characters'],
      },
    ],
  },
  {
    id: 'info',
    value:
      'Shayne Smith referred Michael Benjamin. Both will get 100 loyalty points when you add points for Michael Benjamin',
    type: 'infoBox',
    validations: [],
  },
  {
    id: 'familyName',
    label: 'Last name',
    placeholder: 'Enter Last Name',
    type: 'text',
    subType: 'text',
    validationType: 'string',
    value: 'Kapadia',
    validations: [
      {
        type: 'required',
        params: ['Last Name is required'],
      },
      {
        type: 'max',
        params: [20, 'Last Name cannot be more than 20 characters'],
      },
    ],
  },
  {
    id: 'email',
    label: 'Email Address',
    placeholder: 'Enter Email Address',
    type: 'text',
    subType: 'text',
    validationType: 'string',
    value: '<EMAIL>',
    readOnly: true,
    validations: [
      {
        type: 'required',
        params: ['Email Address is required'],
      },
      {
        type: 'email',
        params: ['Email Address seems to be invalid'],
      },
    ],
  },
  {
    id: 'contactNumber',
    label: 'Mobile Number',
    placeholder: 'Enter Mobile Number (without country code)',
    type: 'text',
    subType: 'text',
    value: '9408746509',
    validationType: 'number',
    validations: [
      {
        type: 'required',
        params: ['Mobile Number is required'],
      },
      {
        type: 'typeError',
        params: ['Mobile number should have numeric digits'],
      },
    ],
  },
  {
    id: 'phone',
    label: 'Phone number',
    placeholder: 'Enter Phone Number',
    type: 'phone',
    validationType: 'string',
    name: 'punePhoneNumber',
    value: '',
    validations: [
      {
        type: 'required',
        params: ['Mobile Number is required'],
      },
      {
        type: 'typeError',
        params: ['Mobile number should have numeric digits'],
      },
    ],
    configurations: {
      allowCountries: [],
    },
  },
];

export default {
  title: 'Components/DynamicForm',
  component: DynamicForm,
} as Meta;

const Template: Story<DynamicFormProps> = (args) => <DynamicForm {...args} />;

export const GenerateDynamicFormText = Template.bind({});
GenerateDynamicFormText.args = {
  fields: textData,
  showRequiredIcon: true,
};

export const GenerateDynamicFormTextArea = Template.bind({});
GenerateDynamicFormTextArea.args = {
  fields: textAreaData,
  showRequiredIcon: true,
};

export const GenerateDynamicFormSelect = Template.bind({});
GenerateDynamicFormSelect.args = {
  fields: selectData,
};

export const GenerateDynamicFormCheckbox = Template.bind({});
GenerateDynamicFormCheckbox.args = {
  fields: checkboxData,
};

export const GenerateDynamicFormRadio = Template.bind({});
GenerateDynamicFormRadio.args = {
  fields: radioData,
};

export const GenerateDynamicFormSwitch = Template.bind({});
GenerateDynamicFormSwitch.args = {
  fields: switchData,
};

export const GenerateDynamicFormAsyncSelect = Template.bind({});
GenerateDynamicFormAsyncSelect.args = {
  fields: AsyncSelectData,
};

export const GenerateDynamicFormUpload = Template.bind({});
GenerateDynamicFormUpload.args = {
  fields: uploadData,
  apiConfigurations: {
    baseUrl: 'http://localhost:3100',
    userId: '607e6bd098b6013bdf13d854',
    token: '',
  },
};

export const GenerateDynamicFormDate = Template.bind({});
GenerateDynamicFormDate.args = {
  fields: dateData,
  title: 'TEST',
  submitLabel: 'UPDATE',
  colorTheme: {
    backgroundColor: 'white',
    formTitleColor: 'orange',
    fieldTitleColor: 'grey',
    buttonPrimaryColor: 'green',
  },
};

export const GenerateDynamicFormTime = Template.bind({});
GenerateDynamicFormTime.args = {
  fields: timeData,
};

export const GenerateDynamicFormHtml = Template.bind({});
GenerateDynamicFormHtml.args = {
  fields: htmlData,
};

export const GenerateDynamicFormHtmlContent = Template.bind({});
GenerateDynamicFormHtmlContent.args = {
  fields: htmlContentData,
};

export const GenerateDynamicFormLocation = Template.bind({});
GenerateDynamicFormLocation.args = {
  fields: locationData,
};

export const GenerateDynamicFormLocationV2 = Template.bind({});
GenerateDynamicFormLocationV2.args = {
  fields: locationDataV2,
};

export const GenerateDynamicFormInfoBox = Template.bind({});
GenerateDynamicFormInfoBox.args = {
  fields: infoBoxData,
};

export const GenerateTestForm = Template.bind({});
GenerateTestForm.args = {
  fields: testData,
  title: 'Edit Clients',
  submitLabel: 'UPDATE',
  colorTheme: {
    backgroundColor: '#FFFFFF',
    formTitleColor: '#3B8396',
    fieldTitleColor: '#2A5E6C',
    buttonPrimaryColor: '#3B8396',
  },
};
