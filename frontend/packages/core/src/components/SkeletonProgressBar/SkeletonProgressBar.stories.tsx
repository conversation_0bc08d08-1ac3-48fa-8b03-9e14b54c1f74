/* eslint-disable */
import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import { SkeletonProgressBar, SkeletonProgressBarProps } from './SkeletonProgressBar';

export default {
  title: 'Components/SkeletonProgressBar',
  component: SkeletonProgressBar,
} as Meta;

const Template: Story<SkeletonProgressBarProps> = (args) => <SkeletonProgressBar {...args} />;

export const Loader = Template.bind({});
Loader.args = { count: 3 };
