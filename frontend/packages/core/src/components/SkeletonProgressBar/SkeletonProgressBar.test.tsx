import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { SkeletonProgressBar } from './SkeletonProgressBar';

describe('<SkeletonProgressBar />', () => {
  test('it should mount', () => {
    render(<SkeletonProgressBar />);

    const skeletonProgressBar = screen.getByTestId('SkeletonProgressBar');

    expect(skeletonProgressBar).toBeInTheDocument();
  });
});
