import React, { CSSProperties } from 'react';
import Skeleton, { SkeletonProps, SkeletonTheme, SkeletonThemeProps } from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import styles from './SkeletonProgressBar.module.css';

export interface SkeletonProgressBarProps extends SkeletonProps, SkeletonThemeProps {
  baseColor?: string;
  highlightColor?: string;
  width?: string | number;
  height?: string | number;
  borderRadius?: string | number;
  inline?: boolean;
  duration?: number;
  direction?: 'ltr' | 'rtl';
  enableAnimation?: boolean;
  count?: number;
  wrapper?: React.FunctionComponent;
  className?: string;
  containerClassName?: string;
  containerTestId?: string;
  circle?: boolean;
  style?: CSSProperties;
}

export const SkeletonProgressBar: React.FC<SkeletonProgressBarProps> = (props) => {
  return (
    <SkeletonTheme {...props}>
      <p>
        <Skeleton {...props} />
      </p>
    </SkeletonTheme>
  );
};
