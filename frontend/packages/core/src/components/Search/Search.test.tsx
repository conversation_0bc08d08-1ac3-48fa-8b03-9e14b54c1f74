import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { Search } from './Search';

const data = { dataColumns: [], handleSearchChangeFor: (queryParam: string) => new Function() };

describe('<Search />', () => {
  test('it should mount', () => {
    render(<Search {...data} />);

    const search = screen.getByTestId('Search');

    expect(search).toBeInTheDocument();
  });
});
