/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { Search, SearchProps } from './Search';

export default {
  title: 'Components/Search',
  component: Search,
} as Meta;

const Template: Story<SearchProps> = (args) => <Search {...args} />;

export const SearchComponent = Template.bind({});
SearchComponent.args = {
  dataColumns: [
    {
      id: 'givenName',
      Header: 'First Name',
      filter: 'fuzzyText',
    },
    {
      id: 'familyName',
      Header: 'Last Name',
      filter: 'fuzzyText',
    },
    {
      id: 'email',
      Header: 'Email',
      filter: 'fuzzyText',
    },
    {
      id: 'contactNumber',
      Header: 'Mobile Number',
    },
    {
      id: 'earnedCredits',
      Header: 'Credits',
      filter: 'exact',
    },
    {
      id: 'createdAt',
      Header: 'Joined On',
      filter: 'date',
    },
    {
      id: 'referralId',
      Header: 'Referrer Code',
      filter: 'fuzzyText',
    },
  ],
  handleSearchChangeFor: (queryParam: string) => new Function(),
};
