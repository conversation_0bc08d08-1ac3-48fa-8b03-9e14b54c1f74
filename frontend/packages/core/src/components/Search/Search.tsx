import React, { useState } from 'react';
import styles from './Search.module.css';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Icon } from '../Icon/Icon';

export interface DataColumns {
  id: string;
  Header: string;
  filter?: 'exact' | 'fuzzyText' | 'date';
}

export interface SearchProps {
  dataColumns: DataColumns[];
  handleSearchChangeFor: (queryParam: string) => void;
}

export const Search: React.FC<SearchProps> = ({ dataColumns, ...props }) => {
  const todayDate = new Date();
  const maxAllowedSelectableDate = new Date(todayDate.getFullYear(), todayDate.getMonth() + 1, todayDate.getDate());
  const searchColumns = dataColumns.filter((item: DataColumns) => item.filter !== undefined);
  const [selectedSearchColumn, setSelectedSearchColumn] = useState(searchColumns[0]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [fromDate, setFromDate] = useState('');
  const [parsingFromDate, setParsingFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [minDate, setMinDate] = useState(
    new Date(todayDate.getFullYear() - 10, todayDate.getMonth(), todayDate.getDate()),
  );

  const onSearchColumnSelect = (event: any) => {
    reset();
    const val = event.target.value;
    let selectedColumn = searchColumns.filter((column) => column?.Header === val);
    setSelectedSearchColumn(selectedColumn[0]);
  };

  function reset() {
    setSearchKeyword('');
    setFromDate('');
    setToDate('');
  }

  const onSearchInputChange = (e: any) => {
    const val = e.target.value;
    setSearchKeyword(val);
    if (selectedSearchColumn.filter === 'exact' ? val.length > 0 : val.length > 2) {
      setQueryParameter(val);
    } else if (val.length === 0) {
      props.handleSearchChangeFor('');
    }
  };

  function setQueryParameter(searchingText: string) {
    if (selectedSearchColumn !== null) {
      const selectedColumn = selectedSearchColumn;
      const searchScopeType = selectedSearchColumn.filter;
      let queryParam = '';
      switch (searchScopeType) {
        case 'exact':
          queryParam = `${selectedColumn.id}=${searchingText}`;
          break;
        default:
          queryParam = `${selectedColumn.id}=/${searchingText}/i`;
          break;
      }
      props.handleSearchChangeFor(queryParam);
    }
  }

  function setDateQueryParameter(date: string, isFromDate: boolean) {
    if (selectedSearchColumn !== null) {
      const selectedColumn = selectedSearchColumn;
      let queryParam = '';
      if (isFromDate) {
        queryParam = `${selectedColumn.id}>=${date}`;
      } else {
        if (parsingFromDate !== undefined) {
          queryParam = `${selectedColumn.id}<=${date}&${selectedColumn.id}>=${parsingFromDate}`;
        } else {
          queryParam = `${selectedColumn.id}<=${date}`;
        }
      }
      props.handleSearchChangeFor(queryParam);
    }
  }

  const handleFromDateChange = (date: Date) => {
    setMinDate(new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1));
    const displayDate = new Date(date).toLocaleDateString();
    const passingDate = new Date(date).toISOString();
    setFromDate(displayDate);
    if (toDate && toDate < displayDate) {
      setToDate('');
    }
    setParsingFromDate(passingDate);
    setDateQueryParameter(passingDate, true);
  };

  const handleToDateChange = (date: Date) => {
    const displayDate = new Date(date).toLocaleDateString();
    setToDate(displayDate);
    const passingDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1).toISOString();
    setDateQueryParameter(passingDate, false);
  };

  return (
    <div className="row no-gutters">
      <div className="col-md-4">
        <div className="row no-gutters">
          <div className={styles.selectBox}>
            <select
              className="form-control form-select form-control-md"
              id="searchSelect"
              value={selectedSearchColumn?.Header}
              onChange={onSearchColumnSelect}
            >
              {searchColumns.map((column) => (
                <option>{column?.Header}</option>
              ))}
            </select>
          </div>
        </div>
      </div>
      <div className="col-md-7">
        {selectedSearchColumn.filter !== 'date' ? (
          <div className="row no-gutters mx-md-3">
            <div className={`ps-2 col-md-7 col-lg-7 col-xl-7 ${styles.selectBox}`}>
              <input
                className={`form-control form-control-md searchInput`}
                placeholder={
                  selectedSearchColumn.filter === 'fuzzyText' ? 'Type minimum 3 characters' : 'Type a character'
                }
                onChange={onSearchInputChange}
                value={searchKeyword}
                type="text"
                id="search_input"
                required={true}
              />
            </div>
          </div>
        ) : (
          <div className="row no-gutters">
            <div className="col-md-6 ps-0">
              <div className={`m-0 row no-gutters ${styles.selectBox}`}>
                <div className="d-flex">
                  <div className={`p-0 m-0 pe-2   my-auto ${styles.labelWidth}`}>From</div>
                  <div className="p-0 m-0 col-md-10 align-self-center ml-2 ps-1">
        
                    {/* @ts-ignore - Temporary fix for React types conflict between react-datepicker and @testing-library/react */}
                      <DatePicker
                      value={fromDate}
                      onChange={handleFromDateChange}
                      placeholderText="From Date"
                      minDate={minDate}
                      maxDate={maxAllowedSelectableDate}
                      className={['form-control', `${styles.dateInput}`].join(' ')}
                    />
                  </div>
                  <div className="p-0 m-0 col-md-2 ml-2  my-auto ps-2 mb-2">
                    <Icon iconName="FaCalendarAlt" />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-6">
              <div className={`m-0 row no-gutters ${styles.selectBox}`}>
                <div className="d-flex">
                  <div className={`p-0 m-0 pe-2 my-auto ${styles.labelWidth}`}>To</div>
                    <div className="p-0 m-0 col-md-10 align-self-center">
                    {/* @ts-ignore - Temporary fix for React types conflict between react-datepicker and @testing-library/react */}
                    <DatePicker
                      value={toDate}
                      onChange={handleToDateChange}
                      placeholderText="To Date"
                      minDate={minDate}
                      maxDate={maxAllowedSelectableDate}
                      className={['form-control', `${styles.dateInput}`].join(' ')}
                    />
                  </div>
                  <div className="p-0 m-0 ps-2 col-md-2 ml-2  my-auto mb-2">
                    <Icon iconName="FaCalendarAlt" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
