/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { Share, ShareProps } from './Share';

export default {
  title: 'Components/Share',
  component: Share,
} as Meta;

const Template: Story<ShareProps> = (args) => <Share {...args} />;

export const ShareComponent = Template.bind({});
ShareComponent.args = {
  title: 'Share something',
  description: 'no description',
  contentUrl: 'https://google.co.in',
  shareConfigurations: {
    telegram: false,
    whatsapp: false,
  },
};
