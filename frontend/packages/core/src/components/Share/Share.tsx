import React from 'react';
import {
  FacebookShareButton,
  FacebookIcon,
  TwitterShareButton,
  TwitterIcon,
  WhatsappShareButton,
  WhatsappIcon,
  TelegramShareButton,
  TelegramIcon,
  LinkedinShareButton,
  LinkedinIcon,
  EmailIcon,
  EmailShareButton,
} from 'react-share';

export interface ShareProps {
  /**
   * The title to be displayed
   */
  title?: string | undefined;
  /**
   * The description to be displayed
   */
  description?: string | undefined;
  /**
   * The URl to share to be displayed
   */
  contentUrl: string;
  /**
   * The icon background style to be displayed
   */
  iconbgStyle?: import('react').CSSProperties | undefined;
  /**
   * The icon border radius to be displayed
   */
  iconBorderRadius?: number | undefined;
  /**
   * The icon fill color to be displayed
   */
  iconFillColor?: string | undefined;
  /**
   * The icon round to be displayed or not
   */
  iconRound?: boolean | undefined;
  /**
   * The icon size to be displayed
   */
  iconSize?: string | number | undefined;
  /**
   * The configurations of share
   */
  shareConfigurations?: Record<string, any>;
}

export const Share: React.FC<ShareProps> = ({
  title,
  description,
  contentUrl,
  iconbgStyle,
  iconBorderRadius,
  iconFillColor,
  iconRound,
  iconSize,
  shareConfigurations,
}) => {
  const socialIcons = {
    marginLeft: '10px',
    marginRight: '10px',
  };
  const configurations = {
    facebook: true,
    twitter: true,
    whatsapp: true,
    linkedin: true,
    telegram: true,
    email: true,
    ...shareConfigurations,
  };

  return (
    <div>
      {configurations.facebook ? (
        <FacebookShareButton style={socialIcons} url={contentUrl} quote={title}>
          <FacebookIcon
            size={iconSize}
            round={iconRound}
            bgStyle={iconbgStyle}
            borderRadius={iconBorderRadius}
            iconFillColor={iconFillColor}
          />
        </FacebookShareButton>
      ) : (
        ''
      )}
      {configurations.twitter ? (
        <TwitterShareButton style={socialIcons} url={contentUrl} title={title}>
          <TwitterIcon
            size={iconSize}
            round={iconRound}
            bgStyle={iconbgStyle}
            borderRadius={iconBorderRadius}
            iconFillColor={iconFillColor}
          />
        </TwitterShareButton>
      ) : (
        ''
      )}
      {configurations.whatsapp ? (
        <WhatsappShareButton style={socialIcons} url={contentUrl} title={title}>
          <WhatsappIcon
            size={iconSize}
            round={iconRound}
            bgStyle={iconbgStyle}
            borderRadius={iconBorderRadius}
            iconFillColor={iconFillColor}
          />
        </WhatsappShareButton>
      ) : (
        ''
      )}
      {configurations.telegram ? (
        <TelegramShareButton style={socialIcons} url={contentUrl} title={title}>
          <TelegramIcon
            size={iconSize}
            round={iconRound}
            bgStyle={iconbgStyle}
            borderRadius={iconBorderRadius}
            iconFillColor={iconFillColor}
          />
        </TelegramShareButton>
      ) : (
        ''
      )}
      {configurations.linkedin ? (
        <LinkedinShareButton style={socialIcons} url={contentUrl} title={title} summary={description}>
          <LinkedinIcon
            size={iconSize}
            round={iconRound}
            bgStyle={iconbgStyle}
            borderRadius={iconBorderRadius}
            iconFillColor={iconFillColor}
          />
        </LinkedinShareButton>
      ) : (
        ''
      )}
      {configurations.email ? (
        <EmailShareButton style={socialIcons} url={contentUrl} subject={title} body={description}>
          <EmailIcon
            size={iconSize}
            round={iconRound}
            bgStyle={iconbgStyle}
            borderRadius={iconBorderRadius}
            iconFillColor={iconFillColor}
          />
        </EmailShareButton>
      ) : (
        ''
      )}
    </div>
  );
};
