import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { Share } from './Share';

const data = { dataColumns: [], handleShareChangeFor: (queryParam: string) => new Function(), contentUrl: '' };

describe('<Share />', () => {
  test('it should mount', () => {
    render(<Share {...data} />);

    const share = screen.getByTestId('Share');

    expect(share).toBeInTheDocument();
  });
});
