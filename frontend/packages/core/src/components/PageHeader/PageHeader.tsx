import React from 'react';
import { Col, Container } from 'react-bootstrap';
import styles from './PageHeader.module.css';

export interface PageHeaderProps {
  /**
   * The title of the header
   */
  title: string;
  /**
   * The color of the header
   */
  background?: string;
  /**
   * The font color of title
   */
  fontColor?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = ({ title, background, fontColor, ...props }) => {
  const titleStyle: any = fontColor ? { color: fontColor } : { color: 'inherit' };
  const headerStyle: any = background ? { background } : { background: 'inherit' };

  return (
    <Col data-testid="PageHeader" style={headerStyle} className={`pt-3 ps-0`}>
      <h2 style={titleStyle}>{title}</h2>
    </Col>
  );
};
