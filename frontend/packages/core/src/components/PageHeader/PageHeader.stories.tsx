/* eslint-disable */
import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import { PageHeader, PageHeaderProps } from './PageHeader';

export default {
  title: 'Components/PageHeader',
  component: PageHeader,
} as Meta;

const Template: Story<PageHeaderProps> = (args) => <PageHeader {...args} />;

export const Title = Template.bind({});
Title.args = {
  title: 'Client',
};
