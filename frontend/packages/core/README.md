# Cleveland Web Core

A comprehensive React component library for building modern admin dashboards and web applications.

## 📦 Installation

```bash
npm install @sworksio/cleveland-web-core
# or
yarn add @sworksio/cleveland-web-core
```

## 🚀 Quick Start

```jsx
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PageHeader } from '@sworksio/cleveland-web-core';

function App() {
  return (
    <div>
      <PageHeader title="My Dashboard" />
      <FormGenerator fields={formFields} onSubmit={handleSubmit} />
    </div>
  );
}
```

## 🧩 Components

### Form Components

#### `FormGenerator`
Dynamically creates form fields based on configuration with Formik integration.

```jsx
import { FormGenerator } from '@sworksio/cleveland-web-core';

const formFields = [
  { name: 'email', type: 'text', label: 'Email', required: true },
  { name: 'password', type: 'password', label: 'Password', required: true }
];

<FormGenerator fields={formFields} onSubmit={handleSubmit} />
```

#### `DynamicForm`
Higher-level form component with built-in validation using Yup schemas.

```jsx
import { DynamicForm } from '@sworksio/cleveland-web-core';

<DynamicForm 
  fields={fields} 
  validationSchema={yupSchema}
  onSubmit={handleSubmit}
  theme="primary"
/>
```

### UI Components

#### `PageHeader`
Customizable page title component with styling options.

```jsx
import { PageHeader } from '@sworksio/cleveland-web-core';

<PageHeader 
  title="Dashboard" 
  backgroundColor="#f8f9fa"
  fontColor="#333"
/>
```

#### `UserCard`
Display user information in a card format.

```jsx
import { UserCard } from '@sworksio/cleveland-web-core';

<UserCard 
  name="John Doe"
  email="<EMAIL>"
  phone="+1234567890"
  profilePicture="https://example.com/avatar.jpg"
/>
```

#### `ErrorHandler`
Display error messages with optional retry functionality.

```jsx
import { ErrorHandler } from '@sworksio/cleveland-web-core';

<ErrorHandler 
  error="Something went wrong"
  errorCode="500"
  showRetryButton={true}
/>
```

#### `Search`
Advanced search component with multiple filter types.

```jsx
import { Search } from '@sworksio/cleveland-web-core';

<Search 
  columns={dataColumns}
  onSearch={handleSearch}
  filterTypes={['text', 'date', 'select']}
/>
```

#### `Share`
Social media sharing component.

```jsx
import { Share } from '@sworksio/cleveland-web-core';

<Share 
  url="https://example.com"
  title="Check this out!"
  platforms={['facebook', 'twitter', 'linkedin']}
/>
```

### Utility Components

- `Icon` - Customizable icon component using React Icons
- `InfoBox` - Information display boxes
- `MediaViewer` - Media content viewer
- `ProgressBar` - Loading progress indicators
- `SkeletonProgressBar` - Skeleton loading states

## 🛠 Development

### Prerequisites

- Node.js (v14 or higher)
- Yarn package manager
- `NODE_AUTH_TOKEN` environment variable set for GitHub packages

### Setup

```bash
# Install dependencies
yarn bootstrap

# Start Storybook development server
yarn start
```

Open [http://localhost:6006](http://localhost:6006) to view the component library in Storybook.

### Available Scripts

- `yarn start` - Start Storybook development server
- `yarn build` - Build the library for production
- `yarn test` - Run tests
- `yarn format` - Format code with Prettier
- `yarn verify` - Clean, install, and build (used in CI)
- `yarn build-storybook` - Build Storybook for deployment

### Testing

```bash
yarn test
```

Tests are written using React Testing Library and Jest.

## 🏗 Build & Distribution

The library is built using Rollup and outputs both CommonJS and ES modules:

- `lib/index.js` - CommonJS build
- `lib/index.esm.js` - ES modules build
- `lib/index.d.ts` - TypeScript declarations

## 📋 Dependencies

### Core Dependencies
- React 18.x
- Formik - Form management
- Yup - Schema validation
- Styled Components - CSS-in-JS styling
- React Bootstrap - UI components
- Axios - HTTP client

### Form Field Types
- Text, TextArea, Select, AsyncSelect
- Date, Time, Phone
- Checkbox, Radio, Switch
- HTML Editor (Draft.js)
- File Upload
- Location Selector (Google Maps)

## 🔧 Configuration

### TypeScript
The library is built with TypeScript and includes full type definitions.

### PostCSS
CSS processing with:
- CSS Modules
- Nested CSS
- Autoprefixer
- Modern CSS features

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run `yarn verify` to ensure everything builds
6. Submit a pull request

## 📄 License

ISC License

## 🔗 Links

- [Storybook Documentation](http://localhost:6006) (when running locally)
- [GitHub Repository](https://github.com/sworksio/cleveland-web-core)

## Available Scripts

In the project directory, you can run:

### `yarn start`

Runs the app in the development mode.\
Open [http://localhost:6006](http://localhost:6006) to view it in the browser.
It uses story books.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `yarn test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `yarn bootstrap`

Installs the dependency of the project.\
Ensure your NODE_AUTH_TOKEN is set at global level with appropriate rights.
