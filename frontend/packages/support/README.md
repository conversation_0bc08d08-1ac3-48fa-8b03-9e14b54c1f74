# Cleveland Web Support

A React component library for building admin dashboard interfaces with feedback management capabilities.

## Overview

This project provides a collection of reusable React components designed for admin dashboards, specifically focused on feedback management functionality. Built with TypeScript and styled with CSS modules, it offers a complete solution for displaying, managing, and interacting with feedback data.

## Features

- 📊 **Feedback Management Components** - Complete CRUD operations for feedback data
- 📱 **Responsive Design** - Components adapt to different screen sizes
- 🎨 **Storybook Integration** - Interactive component documentation and testing
- 📦 **TypeScript Support** - Full type safety and IntelliSense
- 🔧 **Modular Architecture** - Import only what you need
- 🎯 **CSS Modules** - Scoped styling to prevent conflicts

## Components

### Core Components

- **`Feedbacks`** - Main orchestrator component for feedback management with full CRUD operations
- **`FeedbackTable`** - Tabular display of feedback data with sorting and filtering
- **`FeedbackViewTable`** - Responsive wrapper for the feedback table
- **`FeedbackAccordian`** - Mobile-friendly accordion view for feedback items
- **`ActionForm`** - Modal form component for creating and editing feedback entries

### Shared Interfaces

- **`ThemeColors`** - Standardized color theming interface
- **`ApiConfigurations`** - API configuration structure

## Installation

```bash
# Using yarn (recommended)
yarn add @sworksio/cleveland-web-support

# Using npm
npm install @sworksio/cleveland-web-support
```

## Usage

```tsx
import { Feedbacks, FeedbackTable } from '@sworksio/cleveland-web-support';

// Basic usage
function App() {
  return (
    <div>
      <Feedbacks 
        apiConfig={{
          baseUrl: 'https://api.example.com',
          userId: 'user123',
          token: 'your-token'
        }}
      />
    </div>
  );
}
```

## Development

### Prerequisites

- Node.js (version 14 or higher)
- Yarn package manager
- `NODE_AUTH_TOKEN` environment variable set with appropriate GitHub package registry permissions

### Getting Started

1. **Install dependencies:**
   ```bash
   yarn bootstrap
   ```

2. **Start Storybook development server:**
   ```bash
   yarn start
   ```
   Open [http://localhost:6006](http://localhost:6006) to view components in Storybook.

3. **Run tests:**
   ```bash
   yarn test
   ```

4. **Build the library:**
   ```bash
   yarn build
   ```

### Available Scripts

| Script | Description |
|--------|-------------|
| `yarn start` | Start Storybook development server on port 6006 |
| `yarn test` | Run unit tests with Jest and React Testing Library |
| `yarn build` | Build the library for production using Rollup |
| `yarn bootstrap` | Install project dependencies |
| `yarn format` | Format code using Prettier |
| `yarn analyze` | Analyze bundle size with source-map-explorer |
| `yarn build-storybook` | Build static Storybook for deployment |

## Architecture

### Build System

- **Rollup** - Module bundler optimized for libraries
- **TypeScript** - Type checking and compilation
- **PostCSS** - CSS processing with modules support
- **Babel** - JavaScript transpilation

### Development Tools

- **Storybook** - Component development and documentation
- **Jest & React Testing Library** - Unit testing
- **Prettier** - Code formatting
- **ESLint** - Code linting

### Dependencies

#### Core Dependencies
- `@sworksio/dashboard-core` - Core dashboard functionality
- `react-bootstrap` - Bootstrap components for React
- `react-table` - Table component with sorting/filtering
- `styled-components` - CSS-in-JS styling
- `axios` - HTTP client for API calls
- `sweetalert2` - Beautiful alert dialogs

#### Peer Dependencies
- `react` ^17.0.2
- `react-dom` ^17.0.2

## Testing

The project includes comprehensive unit tests for all components using Jest and React Testing Library. Tests cover:

- Component rendering
- User interactions
- API integration
- Error handling
- Responsive behavior

Run tests with:
```bash
yarn test
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes and add tests
4. Run tests: `yarn test`
5. Format code: `yarn format`
6. Build the project: `yarn build`
7. Commit your changes: `git commit -am 'Add new feature'`
8. Push to the branch: `git push origin feature/new-feature`
9. Submit a pull request

## Publishing

The package is automatically published to GitHub Package Registry when a new release is created. The CI/CD pipeline handles:

- Dependency installation
- Testing
- Building
- Publishing to `@sworksio/cleveland-web-support`

## License

ISC

## Support

For issues and questions, please use the GitHub issue tracker.
