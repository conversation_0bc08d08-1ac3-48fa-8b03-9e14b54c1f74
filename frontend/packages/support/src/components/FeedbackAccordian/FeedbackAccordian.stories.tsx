/* eslint-disable */
import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import { FeedbackAccordian, FeedbackAccordianProps } from './FeedbackAccordian';

export default {
  title: 'Components/UsersAccordian',
  component: FeedbackAccordian,
} as Meta;

const Template: Story<FeedbackAccordianProps> = (args) => <FeedbackAccordian {...args} />;

export const UsersAccordianComponent = Template.bind({});
UsersAccordianComponent.args = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'matchUsers=true&type[$ne]=employee',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};
