.orderTabs a {
  margin-bottom: 0;
  border: none !important;
  font-size: 14px;
  color: #000;
}

.orderTabs a.active {
  border-bottom: 4px solid #000;
  margin: 0 0 -4px 0;
  color: #000;
}

.selectOptions {
  font-size: 14px;
  padding: 0 6px;
  opacity: 0.7;
}

.inputSearch {
  font-size: 14px;
  padding: 0 6px;
  opacity: 0.7;
}

.accordianParent {
  padding: 30px 0 15px 0;
}

.accordianCardHeader {
  padding: 0;
  border: none;
  background-color: transparent;
}

.accordianCardHeader button {
  padding: 8px 6px;
  color: #000 !important;
  font-size: 14px;
  width: 100%;
  text-align: left;
  text-decoration: none;
  font-weight: 600;
  min-height: 40px;
}
.accordianCardHeader button img {
  right: 12px;
  position: absolute;
  top: 10px;
}
.accordianCardHeader button:focus:not(:focus-visible) {
  background-color: rgba(0, 0, 0, 0.03);
}

.accordianCardHeader button:focus {
  outline: 0;
  box-shadow: none;
  text-decoration: none;
  transition: all 0.3s ease-in;
}

.accordianCardBody {
  padding: 12px 2px;
  font-size: 14px;
  background-color: rgba(0, 0, 0, 0.03);
}

.accordianCard {
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid #ebe8e8 !important;
  /* border: none; */
}

.accordianTableBody td,
.accordianTableBody th {
  border: none;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.026em;
}

.accordianEditBtn {
  border: none;
  background: transparent;
  color: rgb(4, 24, 204);
  padding: 0 15px 0 0;
  text-transform: capitalize;
}

.accordianBlockBtn {
  border: none;
  background: transparent;
  color: red;
}

.accordianTableBody tr td:first-child {
  white-space: pre;
}

.no-gutters {
  display: flex !important;
}

.tablebody {
  display: flex;
}

.tableheader {
  width: 30vw;
}
