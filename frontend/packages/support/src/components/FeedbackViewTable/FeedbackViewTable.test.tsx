import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { FeedbackViewTable } from './FeedbackViewTable';

const data = {
  feedbacks: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: 'matchFeedbacks=true&type[$ne]=employee',
    actions: [
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};

describe('<FeedbackViewTable />', () => {
  test('it should mount', () => {
    render(<FeedbackViewTable {...data} />);

    const feedbackViewTable = screen.getByTestId('FeedbackViewTable');

    expect(feedbackViewTable).toBeInTheDocument();
  });
});
