/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { FeedbackViewTable, FeedbackViewTableProps } from './FeedbackViewTable';

export default {
  title: 'Components/FeedbackViewTable',
  component: FeedbackViewTable,
} as Meta;

const Template: Story<FeedbackViewTableProps> = (args) => <FeedbackViewTable {...args} />;

export const FeedbackViewTableComponent = Template.bind({});
FeedbackViewTableComponent.args = {
  feedbacks: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'matchFeedbacks=true&type[$ne]=employee',
    actions: [
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};
