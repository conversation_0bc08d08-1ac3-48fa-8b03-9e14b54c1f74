/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { FeedbackTable, FeedbackTableProps } from './FeedbackTable';

export default {
  title: 'Components/FeedbackTable',
  component: FeedbackTable,
} as Meta;

const Template: Story<FeedbackTableProps> = (args) => <FeedbackTable {...args} />;

export const FeedbackTableComponent = Template.bind({});
FeedbackTableComponent.args = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'matchFeedbacks=true&type[$ne]=employee',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};
