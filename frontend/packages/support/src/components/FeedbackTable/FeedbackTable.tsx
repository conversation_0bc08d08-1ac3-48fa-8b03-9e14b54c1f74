import React from 'react';
import { useTable, useFilters, useGlobalFilter, useSortBy } from 'react-table';
import { ThemeColors } from '../SharedExports';
import styles from './FeedbackTable.module.css';
export interface FeedbackTableProps {
  /**
   * The feedbacks data
   */
  data: any;
  /**
   * The colors to be utilized for the component
   */
  themeColors?: ThemeColors;
  /**
   * The configurations required for module
   */
  moduleConfigurations?: Record<string, any>;
  /**
   * The columns data
   */
  columns: any;
  /**
   * The edit callback call
   */
  callback?: any;
}

export const FeedbackTable: React.FC<FeedbackTableProps> = ({
  data,
  columns,
  moduleConfigurations,
  themeColors,
  callback,
  ...props
}) => {
  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } = useTable(
    {
      columns,
      data,
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
  );
  const actions = moduleConfigurations?.actions || [];

  return (
    <div className={styles.FeedbackTable} data-testid="FeedbackTable">
      <table {...getTableProps()}>
        <thead>
          {headerGroups.map((headerGroup: any) => (
            <tr {...headerGroup.getHeaderGroupProps()}>
              {headerGroup.headers.map((column: any) => (
                <th {...column.getHeaderProps(column.getSortByToggleProps())}>{column.render('Header')}</th>
              ))}
              {actions?.length > 0 ? <th>Action</th> : ''}
            </tr>
          ))}
        </thead>
        <tbody {...getTableBodyProps()}>
          {rows.map((row: any, i: number) => {
            prepareRow(row);
            return (
              <tr {...row.getRowProps()}>
                {row.cells.map((cell: any) => {
                  return <td {...cell.getCellProps()}>{cell.render('Cell')}</td>;
                })}

                {actions?.length > 0 ? (
                  <td>
                    {actions.map((action: any, index: number) => {
                      return (
                        <span
                          key={`${index}`}
                          className={styles.actionLabel}
                          onClick={() => callback(row, action.type)}
                        >
                          {action.label}
                        </span>
                      );
                    })}
                  </td>
                ) : (
                  ''
                )}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};
