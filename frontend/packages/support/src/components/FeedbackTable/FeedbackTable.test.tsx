import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { FeedbackTable } from './FeedbackTable';

const data = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'matchFeedbacks=true&type[$ne]=employee',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};

describe('<FeedbackTable />', () => {
  test('it should mount', () => {
    render(<FeedbackTable {...data} />);

    const feedbackTable = screen.getByTestId('FeedbackTable');

    expect(feedbackTable).toBeInTheDocument();
  });
});
