import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { Feedbacks } from './Feedbacks';

const data = {
  title: 'Client',
  helpText: 'This is where you can view and manage client details',
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: 'matchFeedbacks=true&type[$ne]=employee',
    actions: [
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
  apiConfigurations: {
    baseUrl: 'http://localhost:3109',
    userId: '607e6bd098b6013bdf13d854',
    token: '',
  },
};

describe('<Feedbacks />', () => {
  test('it should mount', () => {
    render(<Feedbacks {...data} />);

    const feedbacks = screen.getByTestId('Feedbacks');

    expect(feedbacks).toBeInTheDocument();
  });
});
