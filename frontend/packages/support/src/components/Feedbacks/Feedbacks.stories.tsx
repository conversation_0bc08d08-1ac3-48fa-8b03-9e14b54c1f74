/* eslint-disable */
import React from 'react';
import { <PERSON>a, <PERSON> } from '@storybook/react';
import { Feedbacks, FeedbacksProps } from './Feedbacks';

export default {
  title: 'Components/Feedbacks',
  component: Feedbacks,
} as Meta;

const Template: Story<FeedbacksProps> = (args) => <Feedbacks {...args} />;

export const FeedbackComponent = Template.bind({});
FeedbackComponent.args = {
  title: 'Ratings & Reviews',
  helpText: 'This is where you can view your client ratings and reviews',
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    isFilterEnable: false,
    isImportEnable: false,
    isAddEnable: true,
    isSearchEnable: true,
    defaultQueryParams: 'isActive=true&sort=-_id',
    actions: [
      {
        type: 'soft-delete',
        label: 'DELETE',
      },
    ],
    dataColumns: [
      {
        id: 'userId',
        Header: 'User ID',
        accessor: 'userId',
      },
      {
        id: 'ratedBy.givenName',
        Header: 'First Name',
        filter: 'fuzzyText',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.ratedBy?.givenName ? d.ratedBy?.givenName : '-';",
          },
        },
      },
      {
        id: 'ratedBy.familyName',
        Header: 'Last Name',
        filter: 'fuzzyText',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.ratedBy?.familyName ? d.ratedBy?.familyName : '-';",
          },
        },
      },
      {
        id: 'ratedBy.email',
        Header: 'Email',
        filter: 'fuzzyText',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.ratedBy?.email ? d.ratedBy?.email : '-';",
          },
        },
      },
      {
        id: 'ratedBy.contactNumber',
        Header: 'Mobile Number',
        filter: 'fuzzyText',
        accessor: (d: any) => {
          return d.sortOrder ? d.sortOrder : '-';
        },
      },
      {
        id: 'ratings.score',
        Header: 'Ratings',
        filter: 'exact',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.ratings?.score ? d.ratings?.score : '-';",
          },
        },
      },
      {
        id: 'description',
        Header: 'Description',
        filter: 'fuzzyText',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.description ? d.description : '-';",
          },
        },
      },
      {
        id: 'createdAt',
        Header: 'Created At',
      },
    ],
  },
  apiConfigurations: {
    baseUrl: 'http://localhost:3109',
    userId: '607e6bd098b6013bdf13d854',
    token: '',
  },
};
