import React, { useEffect, useState } from 'react';
import { Icon, InfoBox, PageHeader, ProgressBar, Search, ErrorHandler } from '@frontend/core';
import '@frontend/core/lib/index.css';
import { Button, Col, Row } from 'react-bootstrap';
import axios, { AxiosRequestConfig } from 'axios';
import Dot from 'dot-object';
import { size } from 'lodash';
import Pagination from 'react-js-pagination';
import 'bootstrap/dist/css/bootstrap.css';
import 'react-datepicker/dist/react-datepicker.css';
import { ActionForm } from '../ActionForm/ActionForm.lazy';
import { FeedbackViewTable } from '../FeedbackViewTable/FeedbackViewTable.lazy';
import styles from './Feedbacks.module.css';
import { ApiConfigurations, ThemeColors } from '../SharedExports';
import { FeedbackAccordian } from '../FeedbackAccordian/FeedbackAccordian.lazy';
import { Form } from 'react-bootstrap';

export interface FeedbacksProps {
  /**
   * The colors to be utilized for the component
   */
  themeColors: ThemeColors;
  /**
   * The help text of the component
   */
  helpText: string;
  /**
   * The title of the header
   */
  title: string;
  /**
   * The type of loader to be used
   */
  loaderType?: any;
  /**
   * The configurations required for API call
   */
  apiConfigurations: ApiConfigurations;
  /**
   * The configurations required for module
   */
  moduleConfigurations: Record<string, any>;
}

const staticColumns = [
  {
    id: 'createdAt',
    filter: 'date',
    accessor: (d: any) => {
      return new Date(d.createdAt).toLocaleDateString();
    },
  },
];
const dotHyphen = new Dot('-');
const dot = new Dot('.');

export const Feedbacks: React.FC<FeedbacksProps> = ({
  themeColors,
  helpText,
  title,
  loaderType,
  moduleConfigurations,
  apiConfigurations,
  ...props
}) => {
  let skip = 0;
  const limit = 10;
  const isSearchEnable = moduleConfigurations.isSearchEnable || false;
  const isFilterEnable = moduleConfigurations.isFilterEnable || false;
  const isImportEnable = moduleConfigurations.isImportEnable || false;
  const isAddEnable = moduleConfigurations.isAddEnable || false;
  loaderType = loaderType || 'Audio';

  const [feedbacks, setFeedbacks] = useState();
  const [feedbackFetched, setFeedbackFetched] = useState(false);
  const [apiResponse, setApiResponse] = useState<any>({});
  const [activePage, setActivePage] = useState(1);
  const [dataColumns, setDataColumns] = useState([]);
  const [isColumnsSet, setIsColumnsSet] = useState(false);
  const [queryParams, setQueryParams] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState();
  const [formType, setFormType] = useState('');
  const [editId, setEditId] = useState('');
  const screenWidth = window.innerWidth;

  useEffect(() => {
    combinedDataColumns(moduleConfigurations.dataColumns, staticColumns);
    callGetFeedbacksService(skip, '');
  }, []);

  function callGetFeedbacksService(skipValue: number, queryString: string) {
    setFeedbackFetched(false);
    let defaultQuery = `?offset=${skipValue}&limit=${limit}`;
    if (size(moduleConfigurations?.defaultQueryParams) > 0) {
      defaultQuery += `&${moduleConfigurations.defaultQueryParams}`;
    }
    if (size(queryString) > 0) {
      defaultQuery += `&${queryString}`;
    }
    const url = `${apiConfigurations.baseUrl}/support/feedback${defaultQuery}`;
    const requestConfig: AxiosRequestConfig = {
      method: 'GET',
      url,
      headers: {
        'content-type': 'application/json',
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
    };
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        processDataOnGet(res);
      })
      .catch((err: any) => {
        processErrorDataOnGet(err);
      });
  }

  function callDeleteFeedbackService(id: number) {
    setFeedbackFetched(false);
    const url = `${apiConfigurations.baseUrl}/support/feedback/${id}`;
    const requestConfig: AxiosRequestConfig = {
      method: 'DELETE',
      url,
      headers: {
        'content-type': 'application/json',
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
    };
    axios
      .delete(url, requestConfig)
      .then((res: any) => {
        processDataOnDelete(res);
      })
      .catch((err: any) => {
        processErrorDataOnDelete(err);
      });
  }

  function callEditFeedbackService(data: any, staticData: any) {
    setFeedbackFetched(false);
    staticData = staticData || {};
    const url = `${apiConfigurations.baseUrl}/support/feedback/${editId}`;
    const requestConfig: AxiosRequestConfig = {
      method: 'PATCH',
      url,
      headers: {
        'content-type': 'application/json',
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data: { ...data, ...staticData },
    };
    axios
      .patch(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnEdit(res);
      })
      .catch((err: any) => {
        processErrorDataOnEdit(err);
      });
  }

  function callCreateFeedbackService(data: any, staticData: any) {
    staticData = staticData || {};
    setFeedbackFetched(false);
    const url = `${apiConfigurations.baseUrl}/support/feedback`;
    const requestConfig: AxiosRequestConfig = {
      method: 'POST',
      url,
      headers: {
        'content-type': 'application/json',
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data: {
        ...data,
        ...staticData,
      },
    };
    axios
      .post(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnCreate(res);
      })
      .catch((err: any) => {
        processErrorDataOnCreate(err);
      });
  }

  function processDataOnGet(res: any) {
    if (res.data?.result) {
      setApiResponse(res.data.result);
      const feedbacks = res.data.result?.feedbacks || [];
      setFeedbacks(feedbacks);
      setFeedbackFetched(true);
    }
  }

  function processErrorDataOnGet(err: any) {}

  function processDataOnDelete(res: any) {
    if (res.data?.result) {
      callGetFeedbacksService(skip, queryParams);
    }
  }

  function processErrorDataOnDelete(err: any) {}

  function processDataOnEdit(res: any) {
    if (res.data?.result) {
      dismissActionForm();
      callGetFeedbacksService(skip, queryParams);
    }
  }

  function processErrorDataOnEdit(err: any) {}

  function processDataOnCreate(res: any) {
    if (res.data?.result) {
      dismissActionForm();
      callGetFeedbacksService(skip, queryParams);
    }
  }

  function processErrorDataOnCreate(err: any) {}

  function handlePageChange(pageNumber: number) {
    if (pageNumber > 1) {
      skip = (pageNumber - 1) * limit;
    } else {
      skip = 0;
    }
    setActivePage(pageNumber);
    callGetFeedbacksService(skip, queryParams);
  }

  function callback(data: any, action: string) {
    switch (action) {
      case 'edit':
        showEditView(data.original);
        break;
      case 'delete':
        break;
      case 'soft-delete':
        softDeleteRow(data.original);
        break;
      default:
        break;
    }
  }

  function combinedDataColumns(dataColumns: any, staticColumns: any) {
    if (!isColumnsSet) {
      for (let item of dataColumns) {
        Object.assign(
          item,
          staticColumns.find((y: any) => y?.id === item?.id),
        );
        if (typeof item.accessor === 'object' && item.accessor.function) {
          item.accessor = new Function(item.accessor.function.arguments, item.accessor.function.body);
        }
      }
      setIsColumnsSet(true);
      setDataColumns(dataColumns);
    }
  }

  function softDeleteRow(data: any) {
    callDeleteFeedbackService(data._id);
  }

  function filterFeedbacks(queryString: string) {
    skip = 0;
    setActivePage(1);
    setQueryParams(queryString);
    callGetFeedbacksService(skip, queryString);
  }

  function onCancelClick() {
    dismissActionForm();
  }

  function dismissActionForm() {
    setShowModal(false);
    setModalConfig(undefined);
    setFormType('');
    setEditId('');
  }

  function showCreateView() {
    setFormType('create');
    const createConfig = moduleConfigurations.formConfig?.createFeedback;
    setModalConfig(createConfig);
    setShowModal(true);
  }

  function showEditView(data: any) {
    setEditId(data._id);
    setFormType('edit');
    const editConfig = JSON.parse(JSON.stringify(moduleConfigurations.formConfig?.editFeedback));
    data = dot.dot(data);
    for (let item of editConfig.config) {
      let id = item.id;
      id = id.replace(/-/g, '.');
      let value = data[id] || item.value;
      item.value = value;
    }
    setModalConfig(editConfig);
    setShowModal(true);
  }

  function onSubmitClick(data: any) {
    dotHyphen.object(data);
    data = dot.dot(data);
    const formConfig = moduleConfigurations.formConfig;
    if (formType === 'create') {
      callCreateFeedbackService(data, formConfig.createFeedback?.staticData);
    } else if (formType === 'edit') {
      callEditFeedbackService(data, formConfig.editFeedback?.staticData);
    }
  }

  return (
    <>
      {screenWidth > 450 ? (
        <div data-testid="Feedbacks">
          <PageHeader title={title} fontColor={themeColors?.textSecondary} />
          <InfoBox
            text={helpText}
            noGutter={false}
            iconName={'FaLightbulb'}
            background={themeColors.backgroundSecondary}
            iconColor={themeColors?.textSecondary}
            fontColor={themeColors.textAlternate}
          />
          <Row>
            {isSearchEnable && dataColumns?.length > 0 ? (
              <Col>
                <Search
                  dataColumns={dataColumns}
                  key={'search-component'}
                  handleSearchChangeFor={(queryParams: string) => filterFeedbacks(queryParams)}
                />
              </Col>
            ) : (
              ''
            )}
            {isFilterEnable ? (
              <Col lg={2} xl={2} className={`d-flex justify-content-end mb-auto mx-auto`}>
                <Icon
                  iconName={'FaFilter'}
                  iconColor={themeColors.textSecondary}
                  label={'Filters'}
                  fontColor={themeColors.textSecondary}
                />
              </Col>
            ) : (
              ''
            )}
            {isImportEnable ? (
              <Col lg={3} xl={3} className={`d-flex justify-content-end mb-auto mx-auto`}>
                <Button size="sm" block id="addNewClick" type="submit" className={styles.addNewButton}>
                  {`Upload ${title}`}
                </Button>
              </Col>
            ) : (
              ''
            )}
            {isAddEnable ? (
              <Col lg={3} xl={3} className={`d-flex justify-content-end mb-auto mx-auto`}>
                <Button
                  size="sm"
                  block
                  id="addNewClick"
                  className={styles.addNewButton}
                  type="button"
                  onClick={() => showCreateView()}
                >
                  {`Add ${title}`}
                </Button>
              </Col>
            ) : (
              ''
            )}
          </Row>
          <Row className={`d-block`}>
            {feedbackFetched ? (
              <div>
                {' '}
                {size(feedbacks) > 0 ? (
                  <>
                    <Col className={`d-flex justify-content-center`}>
                      <FeedbackViewTable
                        feedbacks={feedbacks}
                        dataColumns={dataColumns}
                        themeColors={themeColors}
                        moduleConfigurations={moduleConfigurations}
                        callback={callback}
                      />
                    </Col>
                    <Col className={`d-flex justify-content-center`}>
                      {/* @ts-ignore */}
                      <Pagination
                        activePage={activePage}
                        itemsCountPerPage={limit}
                        totalItemsCount={apiResponse?.total}
                        pageRangeDisplayed={5}
                        prevPageText={'Prev'}
                        nextPageText={'Next'}
                        onChange={handlePageChange}
                        activeLinkClass={styles.paginationLinkActive}
                        itemClass={`page-item`}
                        linkClass={`page-link ${styles.paginationLink}`}
                      />
                    </Col>
                  </>
                ) : (
                  <ErrorHandler
                    errorCode={'404'}
                    showTryAgainButton={false}
                    errorMsg={`We are unable to find any matching ${title.toLowerCase()} for your search`}
                  />
                )}
              </div>
            ) : (
              <ProgressBar type={loaderType} background={`#0000002F`} color={themeColors.backgroundSecondary} />
            )}
          </Row>
          {showModal ? (
            <ActionForm
              setModal={showModal}
              modalConfig={modalConfig}
              themeColors={themeColors}
              onCancelClick={() => onCancelClick()}
              onSubmitClick={(data: any) => onSubmitClick(data)}
            />
          ) : (
            ''
          )}
        </div>
      ) : (
        <section className={`${styles.ordersAccordianSection}`}>
          <div className={`${styles.topNav}`}>
            <h1>{title}</h1>
            <button className={`${styles.addNewBtn}`} onClick={() => showCreateView()}>
              {`Add ${title}`}
            </button>
          </div>

          <p className={`${styles.tagLine}`}>
            <img src="" alt="" /> {helpText}
          </p>
          <div>
            <div>
              <Form>
                <Row className="mt-4">
                  {isSearchEnable && dataColumns?.length > 0 ? (
                    <Col>
                      <Search
                        dataColumns={dataColumns}
                        key={'search-component'}
                        handleSearchChangeFor={(queryParams: string) => filterFeedbacks(queryParams)}
                      />
                    </Col>
                  ) : (
                    ''
                  )}
                  {isFilterEnable ? (
                    <Col>
                      <Icon
                        iconName={'FaFilter'}
                        iconColor={themeColors.textSecondary}
                        label={'Filters'}
                        fontColor={themeColors.textSecondary}
                      />
                    </Col>
                  ) : (
                    ''
                  )}
                </Row>
              </Form>
            </div>
            {feedbacks && (
              <FeedbackAccordian
                data={feedbacks}
                columns={dataColumns}
                themeColors={themeColors}
                moduleConfigurations={moduleConfigurations}
                callback={callback}
              />
            )}
          </div>
        </section>
      )}
    </>
  );
};
