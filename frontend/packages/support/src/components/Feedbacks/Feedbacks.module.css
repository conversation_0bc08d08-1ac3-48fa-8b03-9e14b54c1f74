.paginationLinkActive {
  background-color: var(--bg-primary, #3b8396) !important;
  color: var(--text-primary, #ffffff) !important;
  border-color: var(--bg-secondary, #2a5e6c) !important;
}

.paginationLink {
  color: var(--text-secondary);
  background-color: var(--primary);
  color: var(--text-secondary, #3b8396);
  background-color: var(--primary, #ffffff);
}

.addNewButton {
  padding: 0.5rem;
  background-color: var(--bg-primary, #3b8396);
  color: var(--text-primary, #ffffff);
  border-color: var(--bg-secondary, #2a5e6c);
}

.addNewButton:hover {
  background-color: var(--bg-secondary, #2a5e6c);
  color: var(--text-primary, #ffffff);
  border-color: var(--bg-primary, #3b8396);
}

.margin {
  margin: 0.1rem;
}

/* responsive  */
.topNav {
  display: flex;
  position: relative;
  align-items: center;
  padding: 24px 10px;
}

.topNav h1 {
  font-size: 18px;
  margin: 0;
  font-weight: 600;
}
.topNav button {
  border: none;
}

.addNewBtn {
  position: absolute;
  right: 10px;
  background: #000;
  color: #fff;
  font-size: 12px;
  padding: 6px 20px;
  border-radius: 6px;
  cursor: pointer;
}

.tagLine {
  font-size: 13px;
  background: #f4f6fd;
  padding: 13px 10px;
  border-radius: 6px;
  margin: 0 10px;
}

.tagLine img {
  width: 12px;
  margin: 0 8px 0 0;
}
