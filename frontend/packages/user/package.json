{"name": "@frontend/user", "version": "0.0.0", "description": "This is dashboard user library project", "main": "lib/index.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "scripts": {"analyze": "source-map-explorer 'lib/*'", "bootstrap": "yarn install", "build": "rollup -c", "clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write \"src/**/*\"", "start": "start-storybook -p 6006", "test": "yarn test", "verify": "npm-run-all clean bootstrap build", "preversion": "yarn verify", "build-storybook": "build-storybook"}, "keywords": ["dashbaord", "user", "react"], "author": "", "license": "ISC", "dependencies": {"@frontend/core": "*", "axios": "^0.27.2", "bootstrap": "^5.2.3", "dot-object": "^2.1.4", "lodash": "^4.17.21", "react-bootstrap": "^1.5.2", "react-js-pagination": "^3.0.3", "react-router-dom": "5.3.4", "react-table": "^7.8.0", "styled-components": "^5.3.6", "sweetalert2": "^11.4.35"}, "devDependencies": {"@babel/core": "^7.19.3", "@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-node-resolve": "^14.1.0", "@storybook/addon-actions": "^6.5.12", "@storybook/addon-essentials": "^6.5.12", "@storybook/addon-links": "^6.5.12", "@storybook/addon-postcss": "^2.0.0", "@storybook/react": "^6.5.12", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@types/dot-object": "^2.1.2", "@types/jest": "^29.1.1", "@types/react": "^17.0.2", "@types/react-js-pagination": "^3.0.4", "@types/react-router-dom": "5.3.3", "@types/react-table": "^7.7.12", "@types/styled-components": "^5.1.26", "babel-loader": "^8.2.5", "generate-react-cli": "^7.3.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.17", "prettier": "^2.7.1", "react": "^17.0.2", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.34.0", "source-map-explorer": "^2.5.3", "storybook-css-modules-preset": "^1.1.1", "terser": "^5.15.0", "typescript": "^4.8.4"}, "files": ["/lib"], "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}