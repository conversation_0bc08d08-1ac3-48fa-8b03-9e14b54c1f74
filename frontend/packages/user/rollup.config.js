import peerDepsExternal from 'rollup-plugin-peer-deps-external';
import babel from 'rollup-plugin-babel';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from 'rollup-plugin-typescript2';
import packageJson from './package.json';
import { terser } from 'rollup-plugin-terser';
import postcss from 'rollup-plugin-postcss';

export default {
  input: 'src/index.ts',
  output: [
    {
      file: packageJson.main,
      format: 'cjs',
      sourcemap: true,
    },
    {
      file: packageJson.module,
      format: 'esm',
      sourcemap: true,
    },
  ],
  inlineDynamicImports: true,
  external: [...Object.keys(packageJson.dependencies || {})],
  plugins: [
    babel({
      exclude: 'node_modules/**',
    }),
    typescript({
      typescript: require('typescript'),
    }),
    peerDepsExternal(),
    resolve(),
    commonjs(),
    postcss({
      sourceMap: true,
      extract: true,
      modules: true,
      minimize: true,
    }),
    terser(),
  ],
};
