import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { ActionForm } from './ActionForm';

const data = {
  setModal: true,
  modalConfig: [],
  apiConfigurations: {},
  themeColors: {},
  onCancelClick: () => new Function(),
  onSubmitClick: () => new Function(),
};

describe('<ActionForm />', () => {
  test('it should mount', () => {
    render(<ActionForm {...data} />);

    const actionForm = screen.getByTestId('ActionForm');

    expect(actionForm).toBeInTheDocument();
  });
});
