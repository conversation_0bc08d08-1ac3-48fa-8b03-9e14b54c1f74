import React from 'react';
import styles from './ActionForm.module.css';
import { Modal } from 'react-bootstrap';
import { DynamicForm } from '@frontend/core';
export interface ActionFormProps {
  setModal: boolean;
  modalConfig: any;
  themeColors: any;
  apiConfigurations: Record<string, string>;
  onCancelClick: () => void;
  onSubmitClick: (data: any) => void;
}

export const ActionForm: React.FC<ActionFormProps> = ({ themeColors, apiConfigurations, ...props }) => {
  function onCancelClick(e: any) {
    props.onCancelClick();
  }

  function onSubmitClick(data: any) {
    props.onSubmitClick(data);
  }

  return (
    <Modal
      show={true}
      dialogClassName={styles.modalDialog}
      aria-labelledby="example-custom-modal-styling-title"
      className={styles.modalRight}
      contentClassName={styles.modalContent}
      scrollable={true}
      animation={false}
    >
      <Modal.Body>
        <DynamicForm
          fields={props.modalConfig.config}
          title={props.modalConfig.title}
          submitLabel={props.modalConfig.submitLabel}
          cancelLabel={props.modalConfig.cancelLabel || 'CANCEL'}
          disableCancelButton={props.modalConfig.disableCancelButton || false}
          colorTheme={{
            backgroundColor: themeColors.primary || '#FFFFFF',
            formTitleColor: themeColors.textSecondary || '#3B8396',
            fieldTitleColor: themeColors.backgroundSecondary || '#2A5E6C',
            buttonPrimaryColor: themeColors.backgroundPrimary || '#3B8396',
          }}
          apiConfigurations={apiConfigurations}
          onSubmit={(data: any) => onSubmitClick(data)}
          onReset={(e: any) => onCancelClick(e)}
        />
      </Modal.Body>
    </Modal>
  );
};
