import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { ViewUserDetail } from './ViewUserDetail';

const data = {
  setModal: true,
  modalConfig: [],
  data: [],
  themeColors: {},
  onCancelClick: () => new Function(),
};

describe('<ViewUserDetail />', () => {
  test('it should mount', () => {
    render(<ViewUserDetail {...data} />);

    const viewUserDetail = screen.getByTestId('ViewUserDetail');

    expect(viewUserDetail).toBeInTheDocument();
  });
});
