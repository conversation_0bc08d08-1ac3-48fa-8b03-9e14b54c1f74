import React from 'react';
import styles from './ViewUserDetail.module.css';
import { size } from 'lodash';
import { Modal } from 'react-bootstrap';

export interface ViewUserDetailProps {
  setModal: boolean;
  modalConfig: any;
  data: any;
  themeColors: any;
  onCancelClick: () => void;
}

export const ViewUserDetail: React.FC<ViewUserDetailProps> = ({ themeColors, data, modalConfig, ...props }) => {
  const [error, seterror] = React.useState(false);

  function onCancelClick(e: any) {
    props.onCancelClick();
  }

  const RenderUserDetails = ({ item, ...props }: Record<string, any>) => {
    return (
      <div className="w-100 mb-3">
        <div className="d-flex flex-direction-row">
          <div className="w-50">
            <h6 className={`mb-0 d-inline-block ${styles.labelText}`}>{item.Header || 'N/A'}</h6>
          </div>
          <div className={`text-break ${styles.showdataitems}`}>
            <p className={`mb-0 ${styles.labelValue}`}>{data[item.id] || 'N/A'}</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Modal
      show={true}
      dialogClassName={styles.modalDialog}
      aria-labelledby="example-custom-modal-styling-title"
      className={styles.modalRight}
      contentClassName={styles.modalContent}
      scrollable={true}
      animation={false}
    >
      <Modal.Body>
        <div className="modal-header">
          <h4 className={`modal-title ${styles.labelText}`}>{data.subject || modalConfig?.title || 'User Detail'}</h4>
          <button
            type="button"
            className="close"
            data-dismiss="modal"
            aria-label="Close"
            onClick={(e) => onCancelClick(e)}
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div className={`${styles.cardBody} mt-3`}>
          <div className={styles.userProfile}>
            {/* check if url is present but image is not loaded then */}
            {!error ? (
              <img
                className="center"
                src={size(data.profilePicture) > 0 ? data.profilePicture : ''}
                alt="profilepicture"
                onError={(e) => {
                  seterror(true);
                }}
              />
            ) : (
              <img className="center" src={''} alt="profilepicture" />
            )}
            <p className={`mb-0`}>{data.name || 'N/A'}</p>
          </div>
          {modalConfig.config?.map((item: any, index: any) => {
            return <RenderUserDetails item={item} key={index} />;
          })}
        </div>
      </Modal.Body>
    </Modal>
  );
};
