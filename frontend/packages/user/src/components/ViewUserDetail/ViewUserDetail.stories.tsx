/* eslint-disable */
import React from 'react';
import { <PERSON>a, <PERSON> } from '@storybook/react';
import { ViewUserDetail, ViewUserDetailProps } from './ViewUserDetail';

export default {
  title: 'Components/ViewUserDetail',
  component: ViewUserDetail,
} as Meta;

const Template: Story<ViewUserDetailProps> = (args) => <ViewUserDetail {...args} />;

export const ViewUserDetailComponent = Template.bind({});
ViewUserDetailComponent.args = {
  setModal: true,
  modalConfig: {
    title: 'View User Detail',
  },
  themeColors: {
    textSecondary: 'red',
  },
  data: {
    id: '60aca539a690497dc97c523b',
    address: { location: {}, country: 'india' },
    createdAt: '2021-05-25T07:20:25.922Z',
    email: '<EMAIL>',
    familyName: '<PERSON>ber',
    givenName: '<PERSON>',
    languages: [],
    name: '<PERSON>',
    onboardingCompleted: false,
    professionalDetails: {
      portfolio: { hourlyRateIn: '$', projects: [] },
      projectType: [],
      skills: [],
      experience: [],
    },
    profilePicture: '',
    customData: { phoneNumberCountryCode: '+1' },
    type: 'user',
    rating: 0,
    totalProjects: 0,
    company: null,
    referralId: 'ROL1013',
    updatedAt: '2021-06-02T12:10:52.909Z',
    contactNumber: '1582158274',
    stripeCustomerId: 'cus_JZY1QqwJIQYVFn',
    roles: ['user'],
    vehicleDetails: {},
    totalEarnedAmount: 0,
    rank: 0,
    followedTopics: [],
    membership: {},
    isTestUser: false,
    followersCount: {},
    followingCount: {},
    settings: {},
    profileUrl: null,
    blockedList: { users: [], sources: [] },
    countryCode: '+1',
    notifications: { welcomeMail: true },
    isActive: true,
    dashboardId: null,
    additionalMedia: [
      {
        url: '',
        documentId: 'h',
        type: 'image',
        label: 'Driver License',
      },
      {
        type: 'image',
        documentId: '',
        url: '',
        label: 'Insurance Card',
      },
    ],
  },
};
