import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { UserSelect } from './UserSelect';

const data = {
  name: 'name',
  label: 'Full name',
  value: { lable: 'Select', value: 'K' },
  options: [
    { id: 1, name: 'K' },
    { id: 2, name: 'A' },
  ],
  labelCss: {},
  apiConfigurations: {
    baseUrl: '',
    userId: '',
    token: '',
  },
  handleBlur: function () {},
  handleChange: function () {},
  onChange: function () {},
  newOptionHandler: function () {},
};

describe('<UserSelect />', () => {
  test('it should mount', () => {
    render(<UserSelect {...data} />);

    const userSelect = screen.getByTestId('UserSelect');

    expect(userSelect).toBeInTheDocument();
  });
});
