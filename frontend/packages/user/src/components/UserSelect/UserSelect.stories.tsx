/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { UserSelect, UserSelectProps } from './UserSelect';

export default {
  title: 'Components/UserSelect',
  component: UserSelect,
} as Meta;

const Template: Story<UserSelectProps> = (args) => <UserSelect {...args} />;

export const SelectUser = Template.bind({});
SelectUser.args = {
  name: 'name',
  label: 'Choose Customer',
  apiConfigurations: {
    baseUrl: 'http://localhost:3100',
    userId: '609e488663f214601d20a830',
    token: '',
  },
};
