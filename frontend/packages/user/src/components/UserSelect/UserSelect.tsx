import React, { useState, useEffect } from 'react';
// import styles from './UserSelect.module.css';
import { AsyncSelect } from '@frontend/core';
import { debounce, size } from 'lodash';
import axios, { AxiosRequestConfig } from 'axios';
import { ApiConfigurations } from '../SharedExports';
export interface UserSelectProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The label css
   */
  labelCss?: Record<string, string>;
  /**
   * selected value
   */
  value?: {
    lable: string;
    value: any;
  };
  /**
   * The place holder of the field
   */
  placeholder?: string;
  /**
   * The default query for user data
   */
  defaultQueryParams?: string;
  /**
   * The field is readonly
   */
  readOnly?: boolean;
  /**
   * The error handling object
   */
  error?: Record<string, any>;
  /**
   * The touch handling of object
   */
  touched?: Record<string, any>;
  /**
   * debounce time
   */
  debounceTime?: number;

  /**
   * this is going to use the clear input field on select
   */

  isInputClearOnSelect?: boolean;
  /**
   * The function to be called on change
   */
  onChange: React.ChangeEventHandler<any>;
  /**
   * The function to be called on change
   */
  handleBlur?: React.FocusEventHandler<HTMLSelectElement>;

  /**
   * The function to be called on change
   */
  handleChange?: React.ChangeEventHandler;

  /**
   * The function to be called on click on new option button
   */
  newOptionHandler?: React.EventHandler<any>;
  /**
   * The configurations required for API call
   */
  apiConfigurations: ApiConfigurations;
}

export const UserSelect: React.FC<UserSelectProps> = ({
  handleBlur,
  handleChange,
  apiConfigurations,
  debounceTime,
  newOptionHandler,
  value,
  defaultQueryParams,
  isInputClearOnSelect,
  ...props
}) => {
  const limit = 10;
  const [options, setOptions] = useState([]);
  const [userFetched, setUserFetched] = useState(true);
  value = value && parseOption([value])[0];

  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  useEffect(() => {
    callGetUsersService('');
  }, []);

  const callGetUsersService = debounce((searchedText): void => {
    setUserFetched(false);
    let defaultQuery = `?limit=${limit}`;
    if (size(searchedText) > 0) {
      defaultQuery += `&name=/${searchedText}/i`;
    }
    if (size(defaultQueryParams) > 0) {
      defaultQuery += `&${defaultQueryParams}`;
    }
    const url = `${apiConfigurations.baseUrl}/user/filter${defaultQuery}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'GET',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        if (res.data?.result?.users) {
          setOptions(parseOption(res?.data?.result?.users));
        }
        setUserFetched(true);
      })
      .catch((err: any) => {
        // handle error
      });
  }, debounceTime || 500);

  return (
    <div className={'form-group'}>
      <AsyncSelect
        isInputClearOnSelect={isInputClearOnSelect}
        handleChange={handleChange}
        options={options}
        onInputChange={callGetUsersService}
        loading={!userFetched}
        newOptionHandler={newOptionHandler}
        newOptionLabel="Create New"
        value={value}
        labelKey="name"
        searchable={true}
        labelCss={props.labelCss!}
        {...props}
      />
    </div>
  );
};

const parseOption = (options: any[] | undefined): any => {
  if (!options?.length) return [];

  return options?.map((user: any) => ({
    name: `${user?.givenName || ''} ${user?.familyName || ''} ${
      user.contactNumber ? `(${user?.countryCode || ''}${user?.contactNumber || ''})` : ''
    }`,
    value: user,
  }));
};
