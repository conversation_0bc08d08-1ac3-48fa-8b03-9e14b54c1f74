import React, { useEffect, useState } from 'react';
import { Icon, InfoBox, PageHeader, ProgressBar, Search, ErrorHandler } from '@frontend/core';
import '@frontend/core/lib/index.css';
import { But<PERSON>, Col, Row } from 'react-bootstrap';
import axios, { AxiosRequestConfig } from 'axios';
import { size } from 'lodash';
import Pagination from 'react-js-pagination';
import 'bootstrap/dist/css/bootstrap.css';
import 'react-datepicker/dist/react-datepicker.css';
import Dot from 'dot-object';
import { UserViewTable } from '../UserViewTable/UserViewTable.lazy';
import styles from './Users.module.css';
import { ActionForm } from '../ActionForm/ActionForm.lazy';
import { ViewUserDetail } from '../ViewUserDetail/ViewUserDetail.lazy';
import { ViewDocuments } from '../ViewDocuments/ViewDocuments.lazy';
import { ApiConfigurations, ThemeColors } from '../SharedExports';
import Swal from 'sweetalert2';
import { useHistory } from 'react-router-dom';
import { UsersAccordian } from '../UsersAccordian/UsersAccordian.lazy';
import { Form } from 'react-bootstrap';

const dotHyphen = new Dot('-');
const dot = new Dot('.');

export interface UsersProps {
  /**
   * The colors to be utilized for the component
   */
  themeColors: ThemeColors;
  /**
   * The help text of the component
   */
  helpText: string;
  /**
   * The title of the header
   */
  title: string;
  /**
   * The type of loader to be used
   */
  loaderType?: any;
  /**
   * redirect url for create user
   */
  createRedirectUrl?: string;
  /**
   * The configurations required for API call
   */
  apiConfigurations: ApiConfigurations;
  /**
   * The configurations required for module
   */
  moduleConfigurations: Record<string, any>;
}

const staticColumns = [
  {
    id: 'createdAt',
    filter: 'date',
    accessor: (d: any) => {
      return new Date(d.createdAt).toLocaleDateString();
    },
  },
];

export const Users: React.FC<UsersProps> = ({
  themeColors,
  helpText,
  title,
  loaderType,
  moduleConfigurations,
  apiConfigurations,
  createRedirectUrl,
  ...props
}) => {
  let skip = 0;
  const limit = 10;
  const isFilterEnable = moduleConfigurations.isFilterEnable || false;
  const isAddEnable = moduleConfigurations.isAddEnable || false;
  const isSearchEnable = moduleConfigurations.isSearchEnable || false;
  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  loaderType = loaderType || 'Audio';

  const [users, setUsers] = useState([]);
  const [userFetched, setUserFetched] = useState(false);
  const [apiResponse, setApiResponse] = useState<any>({});
  const [activePage, setActivePage] = useState(1);
  const [dataColumns, setDataColumns] = useState([]);
  const [isColumnsSet, setIsColumnsSet] = useState(false);
  const [queryParams, setQueryParams] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showUserDetailsModal, setUserDetailsModal] = useState(false);
  const [showUserDocumentsModal, setUserDocumentsModal] = useState(false);
  const [modalConfig, setModalConfig] = useState();
  const [formType, setFormType] = useState('');
  const [activeData, setActiveData] = useState<any>({});

  const history = useHistory();
  const location: any = history.location;
  
  useEffect(() => {
    if (location?.state?.create) {
      showCreateView();
    }
    combinedDataColumns(moduleConfigurations.dataColumns, staticColumns);
    callGetUsersService(skip, '');
  }, []);

  function callGetUsersService(skipValue: number, queryString: string) {
    if (skipValue === 0) {
      setActivePage(1);
    }
    setUserFetched(false);
    let defaultQuery = `?offset=${skipValue}&limit=${limit}`;
    if (size(moduleConfigurations?.defaultQueryParams) > 0) {
      defaultQuery += `&${moduleConfigurations.defaultQueryParams}`;
    }
    if (size(queryString) > 0) {
      defaultQuery += `&${queryString}`;
    }
    const url = `${apiConfigurations.baseUrl}/user/filter${defaultQuery}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'GET',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        processGetUsersData(res);
      })
      .catch((err: any) => {
        processErrorOnGetUsersData(err);
      });
  }

  function callEditUserApi(data: any, staticData: any, id?: string) {
    setUserFetched(false);
    staticData = staticData || {};
    if (id && !activeData.id) {
      activeData.id = id;
    }
    const url = `${apiConfigurations.baseUrl}/users/${activeData.id}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'PATCH',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data: dot.dot({ ...data, ...staticData }),
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .patch(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processEditUserData(res);
      })
      .catch((err: any) => {
        processErrorOnEditUserData(err);
      });
  }

  function callCreateUserApi(data: any, staticData: any, generateEmailConfig: Record<string, any>) {
    staticData = staticData || {};
    setUserFetched(false);
    if (generateEmailConfig?.isEnable) {
      const name = data.givenName || '';
      const postfixId = generateRandomId(8);
      const emailIdWithName = `${name}_${postfixId}`;
      const domain = generateEmailConfig.domain;
      const emailIdWithNameAndDomain = `${emailIdWithName}@${domain}`;
      data.email = emailIdWithNameAndDomain;
      data.customData = {
        ...data.customData,
        generatedEmail: true,
      };
    }
    if (data.email) {
      data.email = data.email.toLowerCase();
    }
    const url = `${apiConfigurations.baseUrl}/user/register`;
    let requestConfig: AxiosRequestConfig = {
      method: 'POST',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data: { ...data, ...staticData },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .post(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processCreateUserData(res);
      })
      .catch((err: any) => {
        processErrorOnCreateUserData(err);
      });
  }

  function callDeleteUserService(id: number, type: string) {
    setUserFetched(false);
    const url = `${apiConfigurations.baseUrl}/user/${id}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'DELETE',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      params: {
        type,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .delete(url, requestConfig)
      .then((res: any) => {
        processDataOnDelete(res);
      })
      .catch((err: any) => {
        processErrorDataOnDelete(err);
      });
  }

  function processEditUserData(res: any) {
    if (res.data) {
      dismissActionForm();
      callGetUsersService(skip, queryParams);
    } else {
      processErrorOnEditUserData(res);
    }
  }

  function processErrorOnEditUserData(err: any) {
    setUserFetched(true);
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: `We were unable to update the ${title.toLowerCase()}! Please try again after some time!`,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processCreateUserData(res: any) {
    if (res.data && res.data.status === 1) {
      dismissActionForm();
      if (location?.state?.create) {
        history.push(createRedirectUrl || location?.state?.from, {
          user: res.data,
          subEventKey: location?.state?.returnSubEventKey,
          eventKey: location?.state?.returnEventKey,
          returnEventKey: location?.state?.eventKey,
          returnSubEventKey: location?.state?.subEventKey,
        });
      } else {
        callGetUsersService(skip, queryParams);
      }
    } else {
      processErrorOnCreateUserData(res);
    }
  }

  function processErrorOnCreateUserData(err: any) {
    setUserFetched(true);
    const errorMessage = `We were unable to create ${title.toLowerCase()}! Please try again after some time!`;
    const message = err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: message,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processDataOnDelete(res: any) {
    if (res.data?.result) {
      callGetUsersService(skip, queryParams);
    }
  }

  function processErrorDataOnDelete(err: any) {
    setUserFetched(true);
    const errorMessage = `We were unable to remove ${title.toLowerCase()}! Please contact support if urgent!`;
    const message = err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: message,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processGetUsersData(res: any) {
    if (res.data?.result?.users) {
      setApiResponse(res.data.result);
      const users = res.data.result.users || [];
      setUsers(users);
      setUserFetched(true);
    }
  }

  function processErrorOnGetUsersData(err: any) {}

  function hardDeleteRow(data: any, type: string) {
    Swal.fire({
      title: 'Are you sure?',
      text: `This is an irreversible action! App usage for the ${title.toLowerCase()} might get impacted! Do it, only if you are sure!`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: themeColors.backgroundPrimary,
      cancelButtonColor: themeColors.backgroundSecondary,
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        callDeleteUserService(data.id, type);
      }
    });
  }

  function softDeleteRow(data: any, type: string) {
    if (type === 'remove') {
      Swal.fire({
        title: 'Are you sure?',
        text: `App usage for the ${title.toLowerCase()} might get impacted!`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: themeColors.backgroundPrimary,
        cancelButtonColor: themeColors.backgroundSecondary,
        confirmButtonText: 'Yes, deactivate it!',
      }).then((result) => {
        if (result.isConfirmed) {
          callDeleteUserService(data.id, type);
        }
      });
    } else {
      Swal.fire({
        title: 'Are you sure?',
        text: `App usage for the ${title.toLowerCase()} will get enabled!`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: themeColors.backgroundPrimary,
        cancelButtonColor: themeColors.backgroundSecondary,
        confirmButtonText: 'Yes, enable it!',
      }).then((result) => {
        if (result.isConfirmed) {
          callDeleteUserService(data.id, type);
        }
      });
    }
  }

  function generateRandomId(length: number): string {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i += 1) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  function handlePageChange(pageNumber: number) {
    if (pageNumber > 1) {
      skip = (pageNumber - 1) * limit;
    } else {
      skip = 0;
    }
    setActivePage(pageNumber);
    callGetUsersService(skip, queryParams);
  }

  function callback(data: any, actionType: string, action: Record<string, any>) {
    switch (actionType) {
      case 'edit':
        showEditView(data.original);
        break;
      case 'delete':
        hardDeleteRow(data.original, 'hard-delete');
        break;
      case 'soft-delete':
        softDeleteRow(data.original, 'remove');
        break;
      case 'reactivate':
        softDeleteRow(data.original, 'reactivate');
        break;
      case 'userDetails':
        showUserDetailsView(data.original);
        break;
      case 'userDocuments':
        showUserDocumentsView(data.original);
        break;
      case 'status':
        approveOrRejectUser(data);
        break;
      default:
        break;
    }
  }

  function approveOrRejectUser(data: any) {
    setShowModal(true);
    setFormType('approveOrRejectUser');
    showUpdateView(data.original);
    localStorage.setItem('userId', data.original.id);
  }

  async function updateUserStatus(data: any) {
    const userId = localStorage.getItem('userId');
    setShowModal(false);
    data.type = data.type === 'approved' || data.type === 'rejected' ? data.type : 'approved';
    processStatus(userId, data.type, apiConfigurations, themeColors);
  }

  async function processStatus(userId: any, type: string, config: any, themeColors: any) {
    try {
      const url = `${config.baseUrl}/user/${userId}`;
      let requestConfig: AxiosRequestConfig = {
        method: 'DELETE',
        url,
        headers: {
          'content-type': 'application/json',
          'x-sworks-timezone': userTimeZone,
          authorization: `Bearer ${config.token}`,
          userId: `${config.userId}`,
        },
        params: {
          type: type,
        },
      };
      requestConfig = JSON.parse(JSON.stringify(requestConfig));
      axios.delete(url, requestConfig).then((res: any) => {
        if (res.data.status === 1) {
          callEditUserApi({}, 'updateUserStatus', userId);
        } else {
          processError(res, themeColors);
        }
      });
    } catch (error) {
      processError(error, themeColors);
    }
  }

  function processError(err: any, themeColors: any) {
    const errorMessage = `Something went wrong! Please try again after some time!`;
    const message = err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: message,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processErrorData(errorMessage: string) {
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: errorMessage,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function combinedDataColumns(dataColumns: any, staticColumns: any) {
    if (!isColumnsSet) {
      for (let item of dataColumns) {
        Object.assign(
          item,
          staticColumns.find((y: any) => y?.id === item?.id),
        );
        if (typeof item.accessor === 'object' && item.accessor.function) {
          item.accessor = new Function(item.accessor.function.arguments, item.accessor.function.body);
        }
      }
      setIsColumnsSet(true);
      setDataColumns(dataColumns);
    }
  }

  function filterUsers(queryString: string) {
    skip = 0;
    setQueryParams(queryString);
    callGetUsersService(skip, queryString);
  }

  function onCancelClick() {
    dismissActionForm();
  }

  function onViewUserDetailsCancelClick() {
    dismissUserDetailsView();
  }

  function onViewDocumentCancelClick() {
    dismissDocumentView();
  }

  function onSubmitClick(data: any) {
    dotHyphen.object(data);
    const dotData = dot.dot(data);
    const formConfig = moduleConfigurations.formConfig;

    switch (formType) {
      case 'create':
        callCreateUserApi(data, formConfig.createUser.staticData, formConfig.createUser?.generateEmail);
        break;
      case 'edit':
        callEditUserApi(data, formConfig.editUser.staticData);
        break;
      case 'approveOrRejectUser':
        updateUserStatus(data);
        break;
      default:
        break;
    }
  }

  function dismissActionForm() {
    setShowModal(false);
    setModalConfig(undefined);
    setFormType('');
    setActiveData({});
  }

  function dismissUserDetailsView() {
    setUserDetailsModal(false);
    setModalConfig(undefined);
  }

  function dismissDocumentView() {
    setUserDocumentsModal(false);
    setModalConfig(undefined);
  }

  function showCreateView() {
    setFormType('create');
    const createConfig = moduleConfigurations.formConfig.createUser;
    if (createConfig) {
      setModalConfig(createConfig);
      setShowModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  function showEditView(data: any) {
    setActiveData(data);
    setFormType('edit');
    const editConfig = JSON.parse(JSON.stringify(moduleConfigurations.formConfig.editUser));
    data = dot.dot(data);
    for (let item of editConfig.config) {
      let id = item.id;
      id = id.replace(/-/g, '.');
      let value = data[id] || item.value;
      item.value = value;
    }
    if (editConfig) {
      setModalConfig(editConfig);
      setShowModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  function showUpdateView(data: any) {
    setActiveData(data);
    setFormType('approveOrRejectUser');
    const updateConfig = JSON.parse(JSON.stringify(moduleConfigurations.formConfig.updateStatus));
    data = dot.dot(data);
    for (let item of updateConfig.config) {
      let id = item.id;
      id = id.replace(/-/g, '.');
      let value = data[id] || item.value;
      item.value = value;
    }
    if (updateConfig) {
      setModalConfig(updateConfig);
      setShowModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  function showUserDetailsView(data: any) {
    setActiveData(data);
    if (moduleConfigurations.formConfig.userDetails) {
      const userDetailConfig = JSON.parse(JSON.stringify(moduleConfigurations.formConfig.userDetails));
      if (userDetailConfig) {
        setModalConfig(userDetailConfig);
        setUserDetailsModal(true);
      }
    }
  }

  function showUserDocumentsView(data: any) {
    setActiveData(data);
    const userDocumentsConfig = JSON.parse(JSON.stringify(moduleConfigurations.formConfig.userDocuments));
    if (userDocumentsConfig) {
      setModalConfig(userDocumentsConfig);
      setUserDocumentsModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  return (
    <>
      {window.innerWidth > 450 ? (
        <div data-testid="Users">
          <PageHeader title={title} fontColor={themeColors?.textSecondary} />
          <InfoBox
            text={helpText}
            noGutter={false}
            iconName={'FaLightbulb'}
            background={themeColors.backgroundSecondary}
            iconColor={themeColors?.textSecondary}
            fontColor={themeColors.textAlternate}
          />
          <Row>
            {isSearchEnable && dataColumns?.length > 0 ? (
              <Col>
                <Search
                  dataColumns={dataColumns}
                  key={'search-component'}
                  handleSearchChangeFor={(queryParams: string) => filterUsers(queryParams)}
                />
              </Col>
            ) : (
              ''
            )}
            {isFilterEnable ? (
              <Col lg={2} xl={2} className={`d-flex justify-content-end mb-auto mx-auto`}>
                <Icon
                  iconName={'FaFilter'}
                  iconColor={themeColors.textSecondary}
                  label={'Filters'}
                  fontColor={themeColors.textSecondary}
                />
              </Col>
            ) : (
              ''
            )}
            <Col lg={4} className="d-flex justify-content-end">
              {isAddEnable && (
                <div>
                  <Button
                    size="sm"
                    block
                    id="addNewClick"
                    type="button"
                    onClick={() => showCreateView()}
                    className={`${styles.addNewButton} p-2`}
                  >
                    {`Add ${title}`}
                  </Button>
                </div>
              )}
            </Col>
          </Row>
          <Row className={`d-block`}>
            {userFetched ? (
              <div>
                {' '}
                {size(users) > 0 ? (
                  <>
                    <Col className={`d-flex`} xs={12}>
                      <UserViewTable
                        users={users}
                        dataColumns={dataColumns}
                        themeColors={themeColors}
                        moduleConfigurations={moduleConfigurations}
                        callback={callback}
                        showUserDetailsView={showUserDetailsView}
                      />
                    </Col>
                    <Col className={`d-flex justify-content-center`}>
                      {/* @ts-ignore */}
                      <Pagination
                        activePage={activePage}
                        itemsCountPerPage={limit}
                        totalItemsCount={apiResponse?.total}
                        pageRangeDisplayed={5}
                        prevPageText={'Prev'}
                        nextPageText={'Next'}
                        onChange={handlePageChange}
                        activeLinkClass={styles.paginationLinkActive}
                        itemClass={`page-item`}
                        linkClass={`page-link ${styles.paginationLink}`}
                      />
                    </Col>
                  </>
                ) : (
                  <ErrorHandler
                    errorCode={'404'}
                    showTryAgainButton={false}
                    errorMsg={`We are unable to find any matching ${title.toLowerCase()} for your search`}
                  />
                )}
              </div>
            ) : (
              <ProgressBar type={loaderType} background={'#0000002F'} color={themeColors.backgroundSecondary} />
            )}
          </Row>
        </div>
      ) : (
        //Mobile view starts here
        <section className={`${styles.usersAccordianSection}`}>
          <div className={`${styles.topNav}`}>
            <h1>{title}</h1>
            <button className={`${styles.addNewBtn}`} onClick={() => showCreateView()}>{`+ ${title}`}</button>
          </div>

          <p className={`${styles.tagLine}`}>
            <img src="" alt="" /> {helpText}
          </p>
          <div>
            <div>
              <Form>
                <Row className="mt-4">
                  {isSearchEnable && dataColumns?.length > 0 ? (
                    <Col>
                      <Search
                        dataColumns={dataColumns}
                        key={'search-component'}
                        handleSearchChangeFor={(queryParams: string) => filterUsers(queryParams)}
                      />
                    </Col>
                  ) : (
                    ''
                  )}
                  {isFilterEnable ? (
                    <Col>
                      <Icon
                        iconName={'FaFilter'}
                        iconColor={themeColors.textSecondary}
                        label={'Filters'}
                        fontColor={themeColors.textSecondary}
                      />
                    </Col>
                  ) : (
                    ''
                  )}
                </Row>
              </Form>
            </div>
            <UsersAccordian
              data={users}
              columns={dataColumns}
              themeColors={themeColors}
              moduleConfigurations={moduleConfigurations}
              callback={callback}
              showUserDetailsView={showUserDetailsView}
            />
            </div>
            {/* @ts-ignore */}
          <Pagination
            activePage={activePage}
            itemsCountPerPage={limit}
            totalItemsCount={apiResponse?.total}
            pageRangeDisplayed={5}
            prevPageText={'Prev'}
            nextPageText={'Next'}
            onChange={handlePageChange}
            activeLinkClass={styles.paginationLinkActive}
            itemClass={`page-item`}
            linkClass={`page-link ${styles.paginationLink}`}
          />
        </section>
        //Mobile view ends here
      )}
      {showModal ? (
        <ActionForm
          setModal={showModal}
          modalConfig={modalConfig}
          themeColors={themeColors}
          apiConfigurations={apiConfigurations}
          onCancelClick={() => onCancelClick()}
          onSubmitClick={(data: any) => onSubmitClick(data)}
        />
      ) : (
        ''
      )}
      {showUserDetailsModal ? (
        <ViewUserDetail
          setModal={showUserDetailsModal}
          modalConfig={modalConfig}
          themeColors={themeColors}
          data={activeData}
          onCancelClick={() => onViewUserDetailsCancelClick()}
        />
      ) : (
        ''
      )}
      {showUserDocumentsModal ? (
        <ViewDocuments
          setModal={showUserDocumentsModal}
          modalConfig={modalConfig}
          themeColors={themeColors}
          data={activeData}
          onCancelClick={() => onViewDocumentCancelClick()}
        />
      ) : (
        ''
      )}
    </>
  );
};
