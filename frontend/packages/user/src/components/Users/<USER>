import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { Users } from './Users';

const data = {
  title: 'Client',
  helpText: 'This is where you can view and manage client details',
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'matchUsers=true&type[$ne]=employee',
    dataColumns: [],
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
  apiConfigurations: {
    baseUrl: 'http://localhost:3100',
    userId: '607e6bd098b6013bdf13d854',
    token: '',
  },
};

describe('<Users />', () => {
  test('it should mount', () => {
    render(<Users {...data} />);

    const users = screen.getByTestId('Users');

    expect(users).toBeInTheDocument();
  });
});
