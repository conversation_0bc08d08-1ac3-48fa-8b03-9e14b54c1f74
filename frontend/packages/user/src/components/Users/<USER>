.paginationLinkActive {
  background-color: var(--bg-primary, #3b8396) !important;
  color: var(--text-primary, #ffffff) !important;
  border-color: var(--bg-secondary, #2a5e6c) !important;
}

.paginationLink {
  color: var(--text-secondary, #3b8396);
  background-color: var(--primary, #ffffff);
  font-size: 14px;
}

.addNewButton {
  background-color: var(--bg-primary, #3b8396);
  color: var(--text-primary, #ffffff);
  border-color: var(--bg-secondary, #2a5e6c);
}

.addNewButton:hover {
  background-color: var(--bg-secondary, #2a5e6c);
  color: var(--text-primary, #ffffff);
  border-color: var(--bg-primary, #3b8396);
}

.UserHome {
  border: 20px solid #ececec;
}

/*** Mobile view  ***/

/* @font-face {
  font-family: Roboto Regular;
  src: url(../../fonts/Roboto-Regular.ttf);
} */

/* .usersAccordianSection {
  font-family: Roboto Regular;
} */

.tableAttribute {
  vertical-align: middle !important;
}

.actionLabel {
  cursor: pointer;
  font-size: 1rem;
  margin-right: 1rem;
  text-transform: uppercase;
  color: var(--text-secondary, #3b8396);
}

.componentImage {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
}

.topNav {
  display: flex;
  position: relative;
  align-items: center;
  padding: 24px 10px;
}

.topNav h1 {
  font-size: 18px;
  margin: 0;
  font-weight: 600;
}
.topNav button {
  border: none;
}
.addNewBtn {
  position: absolute;
  right: 10px;
  background: #000;
  color: #fff;
  font-size: 12px;
  padding: 6px 20px;
  border-radius: 6px;
  cursor: pointer;
}

.tagLine {
  font-size: 12px;
  background: #f4f6fd;
  padding: 12px 10px;
  border-radius: 6px;
  /* margin: 0 10px; */
}

.tagLine img {
  width: 12px;
  margin: 0 8px 0 0;
}

.userTabs {
  padding: 0 0;
  border: none;
  border-bottom: 4px solid #f4f6fd;
}

.userTabs a {
  margin-bottom: 0;
  border: none !important;
  font-size: 14px;
  color: #000;
}

.userTabs a.active {
  border-bottom: 4px solid #000;
  margin: 0 0 -4px 0;
  color: #000;
}

.selectOptions {
  font-size: 14px;
  padding: 0 10px;
  font-weight: 600;
  opacity: 0.8;
  min-height: 44px;
}
.selectInputs {
  padding: 24px 10px;
}
.selectInputs .row {
  margin: 0;
}
.selectInputsCols:first-child {
  padding: 0 6px 0 16px;
}
.selectInputsCols:last-child {
  padding: 0 16px 0 6px;
}
.inputSearch {
  font-size: 14px;
  padding: 0 10px;
  opacity: 0.8;
  min-height: 44px;
  font-weight: 600;
}
.inputSearch::placeholder {
  font-size: 12px;
  opacity: 1;
  font-weight: 600;
  opacity: 0.8;
}

.accordianParent {
  padding: 16px 0 16px 0;
}

.accordianCardHeader {
  padding: 8px 0 0 0;
  border: none;
  background-color: transparent;
}

.accordianCardHeader button {
  padding: 6px 10px;
  color: #000 !important;
  font-size: 12px;
  width: 100%;
  text-align: left;
  text-decoration: none;
  font-weight: 500;
}
.accordianCardHeader button img {
  right: 12px;
  position: absolute;
}
.accordianCardHeader button:focus:not(:focus-visible) {
  background-color: rgba(0, 0, 0, 0.03);
  font-weight: 600;
}
.accordianCardHeader button:focus img {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

.accordianCardHeader button:focus {
  outline: 0;
  box-shadow: none;
  text-decoration: none;
}

.accordianCardBody {
  padding: 12px 10px;
  font-size: 12px;
  background-color: rgba(0, 0, 0, 0.03);
}

.accordianCard {
  border: none;
}

.accordianTableBody td,
.accordianTableBody th {
  border: none;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.026em;
}

.accordianEditBtn {
  border: none;
  background: transparent;
  color: rgb(4, 24, 204);
  padding: 0 15px 0 0;
}

.accordianBlockBtn {
  border: none;
  background: transparent;
  color: red;
}
.userHeader {
  background-color: #000;
  padding: 24px 16px;
}
.userHeader button {
  float: right;
}
.userHeader span {
  color: #fff;
  font-size: 22px;
}
.hamburgBtn {
  background: transparent;
  border: none;
  padding: 4px 0;
}
.hamburgBtn img {
  width: 18px;
  height: 18px;
}
.customMarginRight {
  margin-right: 12px;
}
