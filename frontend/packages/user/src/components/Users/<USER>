/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { Users, UsersProps } from './Users';
import { MemoryRouter } from 'react-router-dom';

export default {
  title: 'Components/Users',
  component: Users,
  decorators: [
    (Story) => (
      // @ts-ignore
      <MemoryRouter>
        <Story />
      </MemoryRouter>
    ),
  ],
} as Meta;

const Template: Story<UsersProps> = (args) => <Users {...args} />;

export const UserComponent = Template.bind({});
UserComponent.args = {
  title: 'Client',
  helpText: 'This is where you can view and manage client details',
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#3B8396',
    primary: '#FFFFFF',
  },
  moduleConfigurations: {
    defaultQueryParams: 'type=sommelier',
    isSearchEnable: true,
    isFilterEnable: false,
    isImportEnable: true,
    isAddEnable: true,
    isExportEnable: true,
    downloadConfig: {
      exportMsg: ' you would receive a mail for the link',
      limit: 1000,
      title: 'Export XLS',
    },
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'userDetails',
        label: 'VIEW PRE REQUISITE',
      },
      {
        type: 'userDocuments',
        label: 'View documents',
      },
      {
        type: 'soft-delete',
        label: 'DEACTIVATE',
        antiLabel: 'ACTIVATE',
        antiType: 'reactivate',
        field: 'isActive',
        defaultValue: false,
      },
      {
        type: 'login-as',
        label: 'Login as',
      },
      {
        type: 'status',
        label: 'APPROVE / REJECT',
      },
    ],
    uploadConfig: {
      size: 1,
      type: 'guard',
    },
    formConfig: {
      editUser: {
        title: 'Edit Client',
        submitLabel: 'UPDATE',
        staticData: {},
        config: [
          {
            id: 'givenName',
            label: 'First name',
            placeholder: 'Enter First name',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            validations: [
              {
                type: 'required',
                params: ['First name is required'],
              },
              {
                type: 'max',
                params: [20, 'First Name cannot be more than 20 characters'],
              },
            ],
          },
          {
            id: 'familyName',
            label: 'Last name',
            placeholder: 'Enter Last Name',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            validations: [
              {
                type: 'required',
                params: ['Last Name is required'],
              },
              {
                type: 'max',
                params: [20, 'Last Name cannot be more than 20 characters'],
              },
            ],
          },
          {
            id: 'contactNumber',
            label: 'Mobile Number',
            placeholder: 'Enter Mobile Number (without country code)',
            type: 'text',
            subType: 'text',
            value: '',
            validationType: 'number',
            validations: [
              {
                type: 'required',
                params: ['Mobile Number is required'],
              },
              {
                type: 'typeError',
                params: ['Mobile number should have numeric digits'],
              },
            ],
          },
          {
            id: 'email',
            label: 'Email Address',
            placeholder: 'Enter Email Address',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            readOnly: false,
            validations: [
              {
                type: 'required',
                params: ['Email Address is required'],
              },
              {
                type: 'email',
                params: ['Email Address seems to be invalid'],
              },
            ],
          },
          {
            id: 'referralId',
            label: 'Referral code',
            placeholder: 'Referral code',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            readOnly: true,
            validations: [],
          },
          {
            id: 'earnedCredits',
            label: 'Total Rewards',
            placeholder: 'Enter Total Rewards',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '0',
            validations: [],
          },
        ],
      },
      updateStatus: {
        title: 'Update Status',
        submitLabel: 'SUBMIT',
        config: [
          {
            id: 'givenName',
            label: 'First name',
            placeholder: 'N/A',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            readOnly: true,
            validations: [],
          },
          {
            id: 'familyName',
            label: 'Last name',
            placeholder: 'N/A',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            readOnly: true,
            validations: [],
          },
          {
            id: 'contactNumber',
            label: 'Mobile Number',
            placeholder: 'N/A',
            type: 'text',
            subType: 'text',
            value: '',
            validationType: 'number',
            readOnly: true,
            validations: [],
          },
          {
            id: 'email',
            label: 'Email Address',
            placeholder: 'N/A',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            readOnly: true,
            validations: [],
          },
          {
            id: 'type',
            label: 'Status',
            placeholder: 'Select Status',
            type: 'select',
            validationType: 'string',
            options: [
              {
                id: 'approved',
                name: 'Approve',
              },
              {
                id: 'rejected',
                name: 'Reject',
              },
            ],
            validations: [
              {
                type: 'required',
                params: ['Please select status'],
              },
            ],
          },
        ],
      },
      createUser: {
        title: 'Add Client',
        submitLabel: 'SUBMIT',
        staticData: {},
        config: [
          {
            id: 'givenName',
            label: 'First name',
            placeholder: 'Enter First name',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            validations: [
              {
                type: 'required',
                params: ['First name is required'],
              },
              {
                type: 'max',
                params: [20, 'First Name cannot be more than 20 characters'],
              },
            ],
          },
          {
            id: 'familyName',
            label: 'Last name',
            placeholder: 'Enter Last Name',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            validations: [
              {
                type: 'required',
                params: ['Last Name is required'],
              },
              {
                type: 'max',
                params: [20, 'Last Name cannot be more than 20 characters'],
              },
            ],
          },
          {
            id: 'contactNumber',
            label: 'Mobile Number',
            placeholder: 'Enter Mobile Number (without country code)',
            type: 'text',
            subType: 'text',
            value: '',
            validationType: 'number',
            validations: [
              {
                type: 'required',
                params: ['Mobile Number is required'],
              },
              {
                type: 'typeError',
                params: ['Mobile number should have numeric digits'],
              },
            ],
          },
          {
            id: 'email',
            label: 'Email Address',
            placeholder: 'Enter Email Address',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            validations: [
              {
                type: 'required',
                params: ['Email Address is required'],
              },
              {
                type: 'email',
                params: ['Email Address seems to be invalid'],
              },
            ],
          },
          {
            id: 'referralId',
            label: 'Referral code',
            placeholder: 'Referral code',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            validations: [],
          },
        ],
      },
      userDetails: {
        title: 'View User Details',
        config: [
          {
            id: '_id',
            Header: 'Account Id',
            accessor: {
              function: {
                arguments: 'd',
                body: "return d._id ? d._id : '-';",
              },
            },
            filter: 'fuzzyText',
          },
          {
            id: 'activeStatus',
            Header: 'Status',
            accessor: 'activeStatus',
            filter: 'fuzzyText',
          },
          {
            id: 'givenName',
            Header: 'First Name',
            accessor: {
              function: {
                arguments: 'd',
                body: "return d.givenName ? d.givenName : '-';",
              },
            },
            filter: 'fuzzyText',
          },
          {
            id: 'familyName',
            Header: 'Last Name',
            accessor: {
              function: {
                arguments: 'd',
                body: "return d.familyName ? d.familyName : '-';",
              },
            },
            filter: 'fuzzyText',
          },
          {
            id: 'company',
            Header: 'HOA Name',
            accessor: {
              function: {
                arguments: 'd',
                body: "return d.company ? d.company : '-';",
              },
            },
            filter: 'fuzzyText',
          },
          {
            id: 'email',
            Header: 'Email',
            accessor: {
              function: {
                arguments: 'd',
                body: "return d.email ? d.email : '-';",
              },
            },
            filter: 'fuzzyText',
          },
          {
            id: 'contactNumber',
            Header: 'Mobile Number',
            filter: 'fuzzyText',
            accessor: { function: { arguments: 'd', body: "return d.contactNumber ? d.contactNumber : '-';" } },
          },
          {
            id: 'earnedCredits',
            Header: 'Credits',
            filter: 'exact',
            accessor: { function: { arguments: 'd', body: "return d.earnedCredits ? d.earnedCredits : '0';" } },
          },
          {
            id: 'createdAt',
            Header: 'Joined On',
            filter: 'date',
            accessor: { function: { arguments: 'd', body: "return d.createdAt ? d.createdAt : '-';" } },
          },
          {
            id: 'referralId',
            Header: 'Referrer Code',
            filter: 'fuzzyText',
            accessor: { function: { arguments: 'd', body: "return d.referralId ? d.referralId : '-';" } },
          },
        ],
      },
      userDocuments: {
        title: 'View Pre Requisite',
        noDataMessage: 'No data available of document',
        config: [],
      },
    },

    dataColumns: [
      {
        id: 'givenName',
        Header: 'First Name',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.givenName ? d.givenName : '-';",
          },
        },
        filter: 'fuzzyText',
      },
      {
        id: 'familyName',
        Header: 'Last Name',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.familyName ? d.familyName : '-';",
          },
        },
        filter: 'fuzzyText',
      },
      {
        id: 'company',
        Header: 'HOA Name',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.company ? d.company : '-';",
          },
        },
        filter: 'fuzzyText',
      },
      {
        id: 'email',
        Header: 'Email',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.email ? d.email : '-';",
          },
        },
        filter: 'fuzzyText',
      },
      {
        id: 'contactNumber',
        Header: 'Mobile Number',
        filter: 'fuzzyText',
        accessor: { function: { arguments: 'd', body: "return d.contactNumber ? d.contactNumber : '-';" } },
      },
      {
        id: 'activeStatus',
        Header: 'Status',
        accessor: 'activeStatus',
        filter: 'fuzzyText',
      },
      {
        id: 'createdAt',
        Header: 'Joined On',
        filter: 'date',
        accessor: { function: { arguments: 'd', body: "return d.createdAt ? d.createdAt : '-';" } },
      },
      {
        id: 'referralId',
        Header: 'Referrer Code',
        filter: 'fuzzyText',
        accessor: { function: { arguments: 'd', body: "return d.referralId ? d.referralId : '-';" } },
      },
    ],
  },
  apiConfigurations: {
    baseUrl: 'http://localhost:3100',
    userId: '6167f26df8e802dc55d03f09',
    token: '',
  },
};
