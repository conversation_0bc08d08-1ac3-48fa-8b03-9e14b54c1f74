/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { UserViewTable, UserViewTableProps } from './UserViewTable';

export default {
  title: 'Components/UserViewTable',
  component: UserViewTable,
} as Meta;

const Template: Story<UserViewTableProps> = (args) => <UserViewTable {...args} />;

export const UserViewTableComponent = Template.bind({});
UserViewTableComponent.args = {
  users: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: 'matchUsers=true&type[$ne]=employee',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'soft-delete',
        label: 'DELETE',
      },
    ],
  },
};
