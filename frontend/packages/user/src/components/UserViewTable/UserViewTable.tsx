import React from 'react';
import styled from 'styled-components';
import { ThemeColors } from '../SharedExports';
import { Table } from '../Table/Table.lazy';

export interface UserViewTableProps {
  /**
   * The users data
   */
  users: any;
  /**
   * The data mapping columns
   */
  dataColumns: { [key: string]: any }[];
  /**
   * The colors to be utilized for the component
   */
  themeColors?: ThemeColors;
  /**
   * The configurations required for module
   */
  moduleConfigurations?: Record<string, any>;
  /**
   * The edit api callback call
   */
  callback?: any;
  /**
   * The configurations required for module
   */
  apiConfigurations?: Record<string, any>;

  showUserDetailsView?: (e1: any) => void;
}

const Styles = styled.div`
  // padding: 1rem;
  width: 100%;
  padding-top: 0rem;
  //Most important below 4 lines to avoid full screen scrolling
  ${'' /* These styles are suggested for the table fill all available space in its containing element */}
  display: block;
  ${'' /* These styles are required for a horizontaly scrollable table overflow */}
  overflow: auto;
  table {
    border-spacing: 0;
    border: 1px solid #f2f2f2;
    font-size: 14px;
    tr {
      border-bottom: 1px solid #f2f2f2;
      background-color: var(--primary, #ffffff);
      :last-child {
        td {
          border-bottom: 0;
        }
      }
      :hover {
        background-color: #f5f5f5;
      }
    }
    th {
      padding: 0.75rem;
      border-bottom: 1px solid #f2f2f2;
      text-transform: uppercase;
      background-color: #fafafa;
    }
    ,
    td {
      margin: 0rem;
      padding: 6px;
      min-width: 10.5rem;
      text-transform: none;
      text-align: left;
      :last-child {
        border-right: 0;
      }
    }
  }
`;

export const UserViewTable: React.FC<UserViewTableProps> = ({
  users,
  dataColumns,
  themeColors,
  moduleConfigurations,
  callback,
  apiConfigurations,
  showUserDetailsView,
  ...props
}) => {
  const data = React.useMemo(() => users, [users]);
  const columns = React.useMemo(() => dataColumns, []);

  return (
    <Styles>
      <Table
        callback={callback}
        themeColors={themeColors}
        moduleConfigurations={moduleConfigurations}
        columns={columns}
        data={data}
        apiConfigurations={apiConfigurations}
        showUserDetailsView={showUserDetailsView}
      />
    </Styles>
  );
};
