import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { UserViewTable } from './UserViewTable';

const data = {
  users: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: 'matchUsers=true&type[$ne]=employee',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};

describe('<UserViewTable />', () => {
  test('it should mount', () => {
    render(<UserViewTable {...data} />);

    const userViewTable = screen.getByTestId('UserViewTable');

    expect(userViewTable).toBeInTheDocument();
  });
});
