import React from 'react';
import { useTable, useFilters, useGlobalFilter, useSortBy } from 'react-table';
import styles from './Table.module.css';
import { size } from 'lodash';
import { ThemeColors } from '../SharedExports';

export interface TableProps {
  /**
   * The users data
   */
  data: any;
  /**
   * The colors to be utilized for the component
   */
  themeColors?: ThemeColors;
  /**
   * The configurations required for module
   */
  moduleConfigurations?: Record<string, any>;
  /**
   * The columns data
   */
  columns: any;
  /**
   * The edit callback call
   */
  callback?: any;
  /**
   * The configurations required for module
   */
  apiConfigurations?: Record<string, any>;

  showUserDetailsView?: any;
}

export const Table: React.FC<TableProps> = ({
  data,
  columns,
  moduleConfigurations,
  themeColors,
  callback,
  apiConfigurations,
  showUserDetailsView,
  ...props
}) => {
  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } = useTable(
    {
      columns,
      data,
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
  );
  const actions = moduleConfigurations?.actions || [];

  const handleOnClick = (data: any) => {
    showUserDetailsView(data.original);
  };

  return (
    <div className={styles.Table} data-testid="Table">
      <table {...getTableProps()} className={styles.tablebody}>
        <thead>
          {headerGroups.map((headerGroup: any) => (
            <tr {...headerGroup.getHeaderGroupProps()}>
              {actions?.length > 0 ? <th>Action</th> : ''}
              <th>Account Id</th>

              {headerGroup.headers.map((column: any) => (
                <th {...column.getHeaderProps(column.getSortByToggleProps())}>{column.render('Header')}</th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody {...getTableBodyProps()}>
          {rows.map((row: any, i: number) => {
            prepareRow(row);
            return (
              <tr {...row.getRowProps()}>
                {actions?.length > 0 ? (
                  <td>
                    {actions.map((action: any, index: number) => {
                      return (action.field && row.original[action.field] === action.defaultValue) ||
                        (typeof row.original[action.field] === 'object' &&
                          Object.keys(row.original[action.field]).length > 0) ? (
                        <span
                          key={`${index}`}
                          className={styles.actionLabel}
                          onClick={() => callback(row, action.antiType, action)}
                        >
                          {action.antiLabel}
                          <br />
                        </span>
                      ) : (
                        <span
                          key={`${index}`}
                          className={styles.actionLabel}
                          onClick={() => callback(row, action.type, action)}
                        >
                          {action.label}
                          <br />
                        </span>
                      );
                    })}
                  </td>
                ) : (
                  ''
                )}
                <td
                  className={styles.accountId}
                  onClick={() => {
                    handleOnClick(row);
                  }}
                >
                  {row.original._id}
                </td>

                {row.cells.map((cell: any) => {
                  return <td {...cell.getCellProps()}>{cell.render('Cell')}</td>;
                })}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};
