/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { Table, TableProps } from './Table';

export default {
  title: 'Components/Table',
  component: Table,
} as Meta;

const Template: Story<TableProps> = (args) => <Table {...args} />;

export const TableComponent = Template.bind({});
TableComponent.args = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'matchUsers=true&type[$ne]=employee',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};
