import React from 'react';
import styles from './ViewDocuments.module.css';
import { Modal } from 'react-bootstrap';
import { MediaViewer } from '@frontend/core';
import Swal from 'sweetalert2';
import { size } from 'lodash';
import 'react-image-gallery/styles/css/image-gallery.css';
export interface ViewDocumentProps {
  setModal: boolean;
  modalConfig: any;
  data: any;
  themeColors: any;
  onCancelClick: () => void;
}

export const ViewDocuments: React.FC<ViewDocumentProps> = ({ themeColors, data, modalConfig, ...props }) => {
  const documents = data.additionalMedia || [];
  modalConfig.noDataMessage = modalConfig.noDataMessage || `Documents are not added by the user yet!`;
  const images = [];
  for (const document of documents) {
    let description = `${data.name}'s ${document.label}`;
    if (size(document.documentId) > 0) {
      description += ` (${document.documentId})`;
    }
    const item = {
      original: document.url,
      description,
    };
    images.push(item);
  }

  if (images.length === 0) {
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: `${modalConfig.noDataMessage}`,
      confirmButtonColor: themeColors.backgroundPrimary,
    }).then((result) => {
      if (result.isConfirmed) {
        props.onCancelClick();
      }
    });
  }

  function onCancelClick(e: any) {
    props.onCancelClick();
  }

  return (
    <>
      {images.length > 0 ? (
        <Modal
          show={true}
          aria-labelledby="example-custom-modal-styling-title"
          scrollable={true}
          animation={true}
          className={styles.modalAlign}
        >
          <Modal.Body>
            <div className="modal-header">
              <h5 className="modal-title">{modalConfig.title}</h5>
              <button
                type="button"
                className="close"
                data-dismiss="modal"
                aria-label="Close"
                onClick={(e) => onCancelClick(e)}
              >
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div className="clearfix">
              <MediaViewer items={images} />
            </div>
          </Modal.Body>
        </Modal>
      ) : (
        ''
      )}
    </>
  );
};
