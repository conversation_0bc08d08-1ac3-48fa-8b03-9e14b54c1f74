/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { ViewDocuments, ViewDocumentProps } from './ViewDocuments';

export default {
  title: 'Components/ViewDocuments',
  component: ViewDocuments,
} as Meta;

const Template: Story<ViewDocumentProps> = (args) => <ViewDocuments {...args} />;

export const ViewDocumentComponent = Template.bind({});
ViewDocumentComponent.args = {
  setModal: true,
  modalConfig: {
    title: 'View User Detail',
  },
  themeColors: {
    textSecondary: 'red',
  },
  data: {
    id: '60aca539a690497dc97c523b',
    address: { location: {}, country: 'india' },
    createdAt: '2021-05-25T07:20:25.922Z',
    email: '<EMAIL>',
    familyName: '<PERSON><PERSON>',
    givenName: '<PERSON>',
    languages: [],
    name: '<PERSON>',
    onboardingCompleted: false,
    professionalDetails: {
      portfolio: { hourlyRateIn: '$', projects: [] },
      projectType: [],
      skills: [],
      experience: [],
    },
    profilePicture: '',
    customData: { phoneNumberCountryCode: '+1' },
    type: 'user',
    rating: 0,
    totalProjects: 0,
    company: null,
    referralId: 'ROL1013',
    updatedAt: '2021-06-02T12:10:52.909Z',
    contactNumber: '1582158274',
    stripeCustomerId: 'cus_JZY1QqwJIQYVFn',
    roles: ['user'],
    vehicleDetails: {},
    totalEarnedAmount: 0,
    rank: 0,
    followedTopics: [],
    membership: {},
    isTestUser: false,
    followersCount: {},
    followingCount: {},
    settings: {},
    profileUrl: null,
    blockedList: { users: [], sources: [] },
    countryCode: '+1',
    notifications: { welcomeMail: true },
    isActive: true,
    dashboardId: null,
    additionalMedia: [
      {
        url: '',
        documentId: 'h',
        type: 'image',
        label: 'Driver License',
      },
      {
        type: 'image',
        documentId: '',
        url: '',
        label: 'Insurance Card',
      },
    ],
  },
};
