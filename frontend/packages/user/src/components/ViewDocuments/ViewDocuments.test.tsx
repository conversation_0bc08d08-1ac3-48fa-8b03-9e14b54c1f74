import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { ViewDocuments } from './ViewDocuments';

const data = {
  setModal: true,
  modalConfig: [],
  data: [],
  themeColors: {},
  onCancelClick: () => new Function(),
};

describe('<ViewDocuments />', () => {
  test('it should mount', () => {
    render(<ViewDocuments {...data} />);

    const viewDocuments = screen.getByTestId('ViewDocuments');

    expect(viewDocuments).toBeInTheDocument();
  });
});
