import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { EditUser } from './EditUser';

const data = {
  title: 'Client',
  helpText: 'This is where you can view and manage client details',
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {},
  apiConfigurations: {
    baseUrl: 'http://localhost:3100',
    userId: '607e6bd098b6013bdf13d854',
    token: '',
  },
};

describe('<EditUser />', () => {
  test('it should mount', () => {
    render(<EditUser {...data} />);

    const editUser = screen.getByTestId('EditUser');

    expect(editUser).toBeInTheDocument();
  });
});
