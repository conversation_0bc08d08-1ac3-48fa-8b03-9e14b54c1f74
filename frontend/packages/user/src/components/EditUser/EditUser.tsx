import React, { useEffect, useState } from 'react';
import { <PERSON>fo<PERSON><PERSON>, PageHeader, ProgressBar, ErrorHandler, DynamicForm } from '@frontend/core';
import '@frontend/core/lib/index.css';
import { Col, Row } from 'react-bootstrap';
import axios, { AxiosRequestConfig } from 'axios';
import 'bootstrap/dist/css/bootstrap.css';
import Dot from 'dot-object';
import { ApiConfigurations, ThemeColors } from '../SharedExports';
const dotHyphen = new Dot('-');
const dot = new Dot('.');

export interface EditUserProps {
  /**
   * The colors to be utilized for the component
   */
  themeColors: ThemeColors;
  /**
   * The help text of the component
   */
  helpText: string;
  /**
   * The title of the header
   */
  title: string;
  /**
   * The type of loader to be used
   */
  loaderType?: any;
  /**
   * The configurations required for API call
   */
  apiConfigurations: ApiConfigurations;
  /**
   * The configurations required for module
   */
  moduleConfigurations: Record<string, any>;
}

export const EditUser: React.FC<EditUserProps> = ({
  themeColors,
  helpText,
  title,
  loaderType,
  moduleConfigurations,
  apiConfigurations,
  ...props
}) => {
  const [user, setUser] = useState();
  const [userFetched, setUserFetched] = useState(false);
  const [modalConfig, setModalConfig] = useState<any>({});
  const [activeData, setActiveData] = useState<any>({ id: apiConfigurations.userId });
  const configurations: Record<string, string> = apiConfigurations as unknown as Record<string, string>;
  loaderType = loaderType || 'Audio';

  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  useEffect(() => {
    callGetUserService();
  }, []);

  function callGetUserService() {
    setUserFetched(false);
    const url = `${apiConfigurations.baseUrl}/users/${activeData.id}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'GET',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        processGetUsersData(res);
      })
      .catch((err: any) => {
        processErrorOnGetUsersData(err);
      });
  }

  function callEditUserApi(data: any, staticData: any) {
    setUserFetched(false);
    staticData = staticData || {};
    const url = `${apiConfigurations.baseUrl}/users/${activeData._id}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'PATCH',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data: { ...data, ...staticData },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .patch(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processEditUserData(res);
      })
      .catch((err: any) => {
        processErrorOnEditUserData(err);
      });
  }

  function processEditUserData(res: any) {
    if (res.data) {
      const user = res.data || {};
      setUser(user);
      showEditView(res.data);
      setUserFetched(true);
      localStorage.setItem('user', JSON.stringify(user));
    }
  }

  function processErrorOnEditUserData(err: any) {}

  function processGetUsersData(res: any) {
    if (res.data) {
      const user = res.data || {};
      setUser(user);
      showEditView(res.data);
      setUserFetched(true);
    }
  }

  function processErrorOnGetUsersData(err: any) {}

  function showEditView(data: any) {
    setActiveData(data);
    const editConfig = JSON.parse(JSON.stringify(moduleConfigurations.formConfig.editUser));
    data = dot.dot(data);
    for (let item of editConfig.config) {
      let id = item.id;
      id = id.replace(/-/g, '.');
      let value = data[id] || item.value;
      item.value = value;
    }
    setModalConfig(editConfig);
  }

  function onSubmitClick(data: any) {
    dotHyphen.object(data);
    const dotData = dot.dot(data);
    const formConfig = moduleConfigurations.formConfig;
    callEditUserApi(data, formConfig.editUser.staticData);
  }

  function onCancelClick(event: any) {
    location.reload();
  }

  return (
    <div data-testid="Users">
      <PageHeader title={title} fontColor={themeColors?.textSecondary} />
      <InfoBox
        text={helpText}
        noGutter={false}
        iconName={'FaLightbulb'}
        background={themeColors.backgroundSecondary}
        iconColor={themeColors?.textSecondary}
        fontColor={themeColors.textAlternate}
      />
      <Row className={`d-block`}>
        {userFetched ? (
          <div>
            {' '}
            {user ? (
              <Col md={6}>
                <DynamicForm
                  fields={modalConfig?.config}
                  title={modalConfig?.title}
                  submitLabel={modalConfig?.submitLabel}
                  cancelLabel={modalConfig?.cancelLabel || 'CANCEL'}
                  disableCancelButton={modalConfig?.disableCancelButton || false}
                  colorTheme={{
                    backgroundColor: themeColors.primary || '#FFFFFF',
                    formTitleColor: themeColors.textSecondary || '#3B8396',
                    fieldTitleColor: themeColors.backgroundSecondary || '#2A5E6C',
                    buttonPrimaryColor: themeColors.backgroundPrimary || '#3B8396',
                  }}
                  apiConfigurations={configurations}
                  onSubmit={(data: any) => onSubmitClick(data)}
                  onReset={(e: any) => onCancelClick(e)}
                />
              </Col>
            ) : (
              <ErrorHandler errorCode={'404'} showTryAgainButton={false} errorMsg={`We are unable to find the user`} />
            )}
          </div>
        ) : (
          <ProgressBar type={loaderType} background={'#0000002F'} color={themeColors.backgroundSecondary} />
        )}
      </Row>
    </div>
  );
};
