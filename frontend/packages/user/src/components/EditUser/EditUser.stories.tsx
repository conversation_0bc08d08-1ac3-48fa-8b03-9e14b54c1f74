/* eslint-disable */
import React from 'react';
import { <PERSON>a, <PERSON> } from '@storybook/react';
import { EditUser, EditUserProps } from './EditUser';

export default {
  title: 'Components/EditUser',
  component: EditUser,
} as Meta;

const Template: Story<EditUserProps> = (args) => <EditUser {...args} />;

export const EditUserComponent = Template.bind({});
EditUserComponent.args = {
  title: 'Client',
  helpText: 'This is where you can view and manage client details',
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#3B8396',
    primary: '#FFFFFF',
  },
  moduleConfigurations: {
    formConfig: {
      editUser: {
        submitLabel: 'UPDATE',
        cancelLabel: 'RESET',
        disableCancelButton: true,
        staticData: {},
        config: [
          {
            id: 'profilePicture',
            label: 'Profile Image',
            placeholder: '',
            type: 'upload',
            validationType: 'mixed',
            validations: [
              {
                type: 'required',
                params: ['Profile image is required'],
              },
            ],
          },
          {
            id: 'givenName',
            label: 'First name',
            placeholder: 'Enter First name',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            validations: [
              {
                type: 'required',
                params: ['First name is required'],
              },
              {
                type: 'max',
                params: [20, 'First Name cannot be more than 20 characters'],
              },
            ],
          },
          {
            id: 'familyName',
            label: 'Last name',
            placeholder: 'Enter Last Name',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            validations: [
              {
                type: 'required',
                params: ['Last Name is required'],
              },
              {
                type: 'max',
                params: [20, 'Last Name cannot be more than 20 characters'],
              },
            ],
          },
          {
            id: 'contactNumber',
            label: 'Mobile Number',
            placeholder: 'Enter Mobile Number (without country code)',
            type: 'text',
            subType: 'text',
            value: '',
            validationType: 'number',
            validations: [
              {
                type: 'required',
                params: ['Mobile Number is required'],
              },
              {
                type: 'typeError',
                params: ['Mobile number should have numeric digits'],
              },
            ],
          },
          {
            id: 'email',
            label: 'Email Address',
            placeholder: 'Enter Email Address',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            readOnly: false,
            validations: [
              {
                type: 'required',
                params: ['Email Address is required'],
              },
              {
                type: 'email',
                params: ['Email Address seems to be invalid'],
              },
            ],
          },
          {
            id: 'referralId',
            label: 'Referral code',
            placeholder: 'Referral code',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            readOnly: true,
            validations: [],
          },
          {
            id: 'earnedCredits',
            label: 'Total Rewards',
            placeholder: 'Enter Total Rewards',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '0',
            validations: [],
          },
        ],
      },
    },
  },
  apiConfigurations: {
    baseUrl: 'http://localhost:3100',
    userId: '609e488663f214601d20a830',
    token: '',
  },
};
