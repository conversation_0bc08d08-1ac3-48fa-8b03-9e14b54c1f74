/* @font-face {
  font-family: Roboto Regular;
  src: url(../../fonts/Roboto-Regular.ttf);
} */
/* .usersAccordianSection {
  font-family: Roboto Regular;
} */
.tableAttribute {
  vertical-align: middle !important;
}

.actionLabel {
  cursor: pointer;
  font-size: 1rem;
  margin-right: 1rem;
  text-transform: uppercase;
  color: var(--text-secondary, #3b8396);
}

.componentImage {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
}

.topNav {
  display: flex;
  position: relative;
  align-items: center;
  padding-bottom: 24px;
}

.topNav h1 {
  font-size: 18px;
  padding-left: 20px;
  margin: 0;
  font-weight: 600;
}
.topNav button {
  border: none;
}
.addNewBtn {
  position: absolute;
  right: 0;
  background: #000;
  color: #fff;
  font-size: 14px;
  padding: 4px 14px;
  border-radius: 6px;
  cursor: pointer;
}

.tagLine {
  font-size: 12px;
  background: #f4f6fd;
  padding: 12px 10px;
  border-radius: 6px;
}

.tagLine img {
  width: 12px;
  margin: 0 8px 0 0;
}

.userTabs {
  padding: 0 0;
  border: none;
  border-bottom: 4px solid #f4f6fd;
}

.userTabs a {
  margin-bottom: 0;
  border: none !important;
  font-size: 14px;
  color: #000;
}

.userTabs a.active {
  border-bottom: 4px solid #000;
  margin: 0 0 -4px 0;
  color: #000;
}

.selectOptions {
  font-size: 14px;
  padding: 0 6px;
  opacity: 0.7;
}

.inputSearch {
  font-size: 14px;
  padding: 0 6px;
  opacity: 0.7;
}

.accordianParent {
  padding: 16px 0 16px 0;
}

.accordianCardHeader {
  padding: 0;
  border: none;
  background-color: transparent;
}

.accordianCardHeader button {
  padding: 8px 8px;
  color: #000 !important;
  font-size: 14px;
  width: 100%;
  text-align: left;
  text-decoration: none;
  font-weight: 600;
  min-height: 40px;
}
.accordianCardHeader button img {
  right: 12px;
  position: absolute;
  top: 10px;
}
.accordianCardHeader button:focus:not(:focus-visible) {
  background-color: rgba(0, 0, 0, 0.03);
}

/* .accordianCardHeader button:focus img {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
} */

.accordianCardHeader button:focus {
  outline: 0;
  box-shadow: none;
  text-decoration: none;
  transition: all 0.3s ease-in;
}

.accordianCardBody {
  padding: 12px 2px;
  font-size: 14px;
  background-color: rgba(0, 0, 0, 0.03);
  overflow: auto;
}

.accordianCard {
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid #ebe8e8 !important;
  /* border: none; */
}

.accordianTableBody td,
.accordianTableBody th {
  border: none;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.026em;
}

.accordianEditBtn {
  border: none;
  background: transparent;
  color: rgb(4, 24, 204);
  padding: 0 15px 0 0;
  text-transform: capitalize;
}

.accordianBlockBtn {
  border: none;
  background: transparent;
  color: red;
}
.accordianTableBody tr td:first-child {
  white-space: pre;
}

.no-gutters {
  display: flex !important;
}

.main {
  display: flex;
}
.headers {
  width: max-content;
}
.minHeight {
  min-height: 31px;
}
.td_accountId {
  cursor: pointer;
  color: #528ff0;
  border: none;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.026em;
}
.accountId {
  padding: 0.3rem;
}
