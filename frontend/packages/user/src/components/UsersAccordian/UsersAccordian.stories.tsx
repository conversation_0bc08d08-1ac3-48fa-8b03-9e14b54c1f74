/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { UsersAccordian, UsersAccordianProps } from './UsersAccordian';

export default {
  title: 'Components/UsersAccordian',
  component: UsersAccordian,
} as Meta;

const Template: Story<UsersAccordianProps> = (args) => <UsersAccordian {...args} />;

export const UsersAccordianComponent = Template.bind({});
UsersAccordianComponent.args = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'matchUsers=true&type[$ne]=employee',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};
