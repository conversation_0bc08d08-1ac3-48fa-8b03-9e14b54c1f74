import React, { useState } from 'react';
import { useTable, useFilters, useGlobalFilter, useSortBy } from 'react-table';
import styles from './UsersAccordian.module.css';
import { ThemeColors } from '../SharedExports';
import { Accordion, Button, Card, Table } from 'react-bootstrap';
export interface UsersAccordianProps {
  /**
   * The users data
   */
  data: any;
  /**
   * The colors to be utilized for the component
   */
  themeColors?: ThemeColors;
  /**
   * The configurations required for module
   */
  moduleConfigurations?: Record<string, any>;
  /**
   * The columns data
   */
  columns: any;
  /**
   * The edit callback call
   */
  callback?: any;

  showUserDetailsView?: any;
}

export const UsersAccordian: React.FC<UsersAccordianProps> = ({
  data,
  columns,
  moduleConfigurations,
  themeColors,
  callback,
  showUserDetailsView,
  ...props
}) => {
  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } = useTable(
    {
      columns,
      data,
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
  );
  const actions = moduleConfigurations?.actions || [];
  // const rowData = (item: any) => {
  //   const object = {
  //     original: item,
  //   };
  //   return object;
  // };
  const [idx, setIdx] = useState<any>([]);

  const handleOnClick = (data: any) => {
    showUserDetailsView(data.original);
  };

  return (
    <div className={`${styles.accordianParent}`}>
      <Accordion>
        {rows?.map((row: any, index: number) => {
          prepareRow(row);
          return (
            <Card className={`${styles.accordianCard}`} key={index}>
              <Card.Header className={`${styles.accordianCardHeader}`}>
                <Accordion.Toggle
                  as={Button}
                  variant="link"
                  bsPrefix=""
                  eventKey={`${index}`}
                  className={`${styles.accordianToggleBtn}`}
                  onClick={() => {
                    if (idx.includes(index)) setIdx(idx.filter((i: any) => i !== index));
                    else setIdx([index]);
                  }}
                >
                  <span>{row?.original.givenName} </span>
                  <img src={idx.includes(index) ? '' : ''} alt="" />
                </Accordion.Toggle>
              </Card.Header>
              <Accordion.Collapse eventKey={`${index}`}>
                <Card.Body className={`${styles.accordianCardBody}`}>
                  <Table hover size="sm" {...getTableProps()}>
                    <tbody className={`${styles.accordianTableBody}`} {...getTableBodyProps()}>
                      <div className={styles.main}>
                        <div>
                          <th>Account Id</th>
                          <div className={styles.headers}>
                            {headerGroups.map((headerGroup: any) => (
                              <div {...headerGroup.getHeaderGroupProps()}>
                                {headerGroup.headers.map((column: any, key: any) => (
                                  <div>
                                    <td {...column.getHeaderProps(column.getSortByToggleProps())}>
                                      {column.render('Header')}
                                    </td>
                                  </div>
                                ))}
                              </div>
                            ))}
                          </div>
                          <td className={styles.accountId}>Actions</td>
                        </div>
                        <div>
                          <div>
                            <div
                              className={styles.accountId}
                              onClick={() => {
                                handleOnClick(row);
                              }}
                            >
                              <tr className={styles.td_accountId}>{row.original._id}</tr>
                            </div>
                            {row.cells.map((cell: any) => (
                              <div className={styles.minHeight}>
                                <td {...cell.getCellProps()}>{cell.render('Cell')}</td>
                              </div>
                            ))}
                          </div>
                          <div>
                            <td>
                              {actions.map((action: any, index: number) => {
                                return (action.field && row.original[action.field] === action.defaultValue) ||
                                  (typeof row.original[action.field] === 'object' &&
                                    Object.keys(row.original[action.field]).length > 0) ? (
                                  <button
                                    className={`${styles.accordianEditBtn}`}
                                    key={`${index}`}
                                    onClick={() => callback(row, action.antiType, action)}
                                  >
                                    {action.antiLabel.toLowerCase()}
                                  </button>
                                ) : (
                                  <button
                                    className={`${styles.accordianEditBtn}`}
                                    key={`${index}`}
                                    onClick={() => callback(row, action.type, action)}
                                  >
                                    {action.label.toLowerCase()}
                                  </button>
                                );
                              })}
                            </td>
                          </div>
                        </div>
                      </div>
                    </tbody>
                  </Table>
                </Card.Body>
              </Accordion.Collapse>
            </Card>
          );
        })}
      </Accordion>
    </div>
  );
};
