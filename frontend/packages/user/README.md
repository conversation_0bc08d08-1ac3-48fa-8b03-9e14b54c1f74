# Cleveland Web User

A React component library for the revamped smagic admin dashboard, providing reusable UI components for user management and administration.

## Overview

This library contains a collection of React components designed for building admin dashboards with user management capabilities. It includes components for displaying users in tables and accordions, editing user details, viewing documents, and handling various user actions.

## Installation

```bash
yarn add @sworksio/cleveland-web-user
```

**Note:** Ensure your `NODE_AUTH_TOKEN` is set at the global level with appropriate rights for accessing the GitHub package registry.

## Components

### Core Components

- **`Users`** - Main component for managing users with CRUD operations, pagination, and status management
- **`Table`** - Reusable table component with sorting, filtering, and action capabilities
- **`EditUser`** - Form component for editing user details
- **`ActionForm`** - Generic modal form component for various user actions
- **`UserSelect`** - Async select component for searching and selecting users
- **`ViewUserDetail`** - Modal component for displaying detailed user information
- **`ViewDocuments`** - Component for viewing user-associated documents/media
- **`UsersAccordian`** - Mobile-friendly accordion view for user data
- **`UserViewTable`** - Specialized table view for user data

## Usage

```jsx
import { Users, Table, EditUser } from '@sworksio/cleveland-web-user';

function App() {
  return (
    <div>
      <Users />
    </div>
  );
}
```

## Dependencies

### Peer Dependencies
- React ^17.0.2
- React DOM ^17.0.2

### Key Dependencies
- `@sworksio/dashboard-core` - Core dashboard components and utilities
- `react-bootstrap` - Bootstrap components for React
- `react-table` - Table functionality
- `axios` - HTTP client
- `styled-components` - CSS-in-JS styling
- `sweetalert2` - Alert dialogs

## Development

### Prerequisites
- Node.js
- Yarn package manager
- `NODE_AUTH_TOKEN` environment variable set with GitHub package registry access

### Available Scripts

#### `yarn start`
Runs Storybook in development mode at [http://localhost:6006](http://localhost:6006).
The page will reload when you make edits.

#### `yarn test`
Launches the test runner in interactive watch mode.

#### `yarn bootstrap`
Installs all project dependencies.

#### `yarn build`
Builds the library for production using Rollup.

#### `yarn clean`
Removes the `dist` directory.

#### `yarn format`
Formats source code using Prettier.

#### `yarn analyze`
Analyzes the bundle size using source-map-explorer.

#### `yarn verify`
Runs clean, bootstrap, and build in sequence.

#### `yarn build-storybook`
Builds Storybook for deployment.

### Development Workflow

1. **Setup:**
   ```bash
   yarn bootstrap
   ```

2. **Development:**
   ```bash
   yarn start
   ```

3. **Testing:**
   ```bash
   yarn test
   ```

4. **Build:**
   ```bash
   yarn build
   ```

## Build Configuration

- **Bundler:** Rollup with TypeScript, Babel, and PostCSS
- **Testing:** Jest with React Testing Library
- **Documentation:** Storybook
- **Styling:** CSS Modules with PostCSS
- **Code Quality:** ESLint, Prettier

## Publishing

This package is published to GitHub Package Registry. The build process is automated via GitHub Actions.

## File Structure

```
src/
├── components/
│   ├── Users/           # Main user management component
│   ├── Table/           # Reusable table component
│   ├── EditUser/        # User editing form
│   ├── ActionForm/      # Generic modal form
│   ├── UserSelect/      # User selection component
│   ├── ViewUserDetail/  # User detail viewer
│   ├── ViewDocuments/   # Document viewer
│   ├── UsersAccordian/  # Mobile accordion view
│   └── UserViewTable/   # User table view
├── fonts/               # Font assets
└── global.d.ts         # Global type definitions
```

## License

ISC

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run `yarn verify` to ensure everything builds correctly
6. Submit a pull request
