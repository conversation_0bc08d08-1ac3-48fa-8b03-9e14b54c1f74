/* eslint-disable */
import React from "react";
import { <PERSON>a, <PERSON> } from "@storybook/react";
import { CataloguesFilter, CataloguesFilterProps } from "./CataloguesFilter";

const data = [
  {
    key: "region",
    title: "Region",
    type: "radio",
    options: [
      {
        displayLabel: "IN",
        key: "IN",
      },
      {
        displayLabel: "UNITED STATES",
        key: "US",
      },
      {
        displayLabel: "UNITED KINGDOM",
        key: "UK",
      },
      {
        displayLabel: "INDIA",
        key: "IN",
      },
      {
        displayLabel: "UNITED STATES",
        key: "US",
      },
      {
        displayLabel: "UNITED KINGDOM",
        key: "UK",
      },
      {
        displayLabel: "INDIA",
        key: "IN",
      },
      {
        displayLabel: "UNITED STATES",
        key: "US",
      },
      {
        displayLabel: "UNITED KINGDOM",
        key: "UK",
      },
    ],
  },
  {
    key: "varietal",
    title: "Varietal",
    type: "checkbox",
    options: [
      {
        displayLabel: "Barbera",
        key: "barbera",
      },
      {
        displayLabel: "Dolcetto",
        key: "dolcetto",
      },
      {
        displayLabel: "Barbera",
        key: "barbera",
      },
      {
        displayLabel: "Dolcetto",
        key: "dolcetto",
      },
      {
        displayLabel: "Barbera",
        key: "barbera",
      },
      {
        displayLabel: "Dolcetto",
        key: "dolcetto",
      },
      {
        displayLabel: "Barbera",
        key: "barbera",
      },
      {
        displayLabel: "Dolcetto",
        key: "dolcetto",
      },
    ],
  },
  {
    key: "customData.product.color",
    title: "Type",
    type: "checkbox",
    options: [
      {
        displayLabel: "Red",
        key: "red",
      },
      {
        displayLabel: "White",
        key: "white",
      },
    ],
  },
  {
    key: "price",
    title: "Price",
    type: "range",
    min: 0,
    max: 1000,
    sliderBgColor: "#dc3545",
    color: "#000",
    currency: "$",
  },
  {
    key: "customData.product.vintage",
    title: "Year",
    type: "range",
    min: 1947,
    max: new Date().getFullYear(),
    sliderBgColor: "#dc3545",
    color: "#000",
  },
];

export default {
  title: "Components/CataloguesFilter",
  component: CataloguesFilter,
} as Meta;

const Template: Story<CataloguesFilterProps> = (args) => (
  <CataloguesFilter {...args} />
);

export const GenerateForm = Template.bind({});
GenerateForm.args = {
  fields: data,
  formikProps: {},
  apiConfigurations: {
    baseUrl: "http://localhost:3104",
    userId: "607e6bd098b6013bdf13d854",
    token: "",
  },
  onChangeFilter: (query: string) => {
    console.log(query);
  },
  onApplyFilter: (query: string) => {
    console.log(query);
  },
};
