import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { CheckboxField } from "./CheckboxField";

const data = {
  name: "name",
  label: "Full name",
  value: "",
  options: [
    { id: 1, name: "<PERSON>" },
    { id: 2, name: "A" },
  ],
  labelCss: {},
  readOnly: false,
  handleChange: function () {},
  handleBlur: function () {},
  setFieldValue: function () {},
};

describe("<CheckboxField />", () => {
  test("it should mount", () => {
    render(<CheckboxField {...data} />);

    const checkboxField = screen.getByTestId("CheckboxField");

    expect(checkboxField).toBeInTheDocument();
  });
});
