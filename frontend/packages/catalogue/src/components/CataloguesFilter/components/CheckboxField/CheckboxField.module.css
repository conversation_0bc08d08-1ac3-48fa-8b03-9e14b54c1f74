.errorText {
  color: red;
  font-size: 12px;
  padding-top: 0.3rem;
}

.formCheckLabel {
  font-weight: 400;
  padding: 5px 0;
  width: calc(80%);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.formCheckLabel input[type="checkbox"] {
  cursor: pointer;
}
.formCheckLabel input[type="checkbox"]:checked {
  background-color: var(--bg-secondary, #2a5e6c);
  cursor: pointer;
}
.checkboxContainer {
  max-height: 200px;
  overflow-y: hidden;
}
.checkboxContainer:hover {
  overflow-y: auto;
}

/*  */
