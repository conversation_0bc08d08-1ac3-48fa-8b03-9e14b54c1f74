import React, { useState } from "react";
import styles from "./CheckboxField.module.css";
export interface CheckboxFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The selectable options
   */
  options: any[];
  /**
   * The value object
   */
  value?: string | number | readonly string[];
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The field is readonly
   */
  handleChange: React.ChangeEventHandler<HTMLInputElement>;

  dataObject: any;
}

export const CheckboxField: React.FC<CheckboxFieldProps> = ({
  label,
  name,
  options,
  labelCss,
  handleChange,
  dataObject,
  ...props
}) => {
  const [checkedItems, setCheckedItems] = useState(new Map());
  // to avoid conflict with key name in handle click item from event
  const fieldName = name;
  const handleCheckItem = (event: any) => {
    const { name, value } = event.target;
    let items = new Map(checkedItems);
    if (checkedItems.has(name)) {
      items.delete(name);
    } else {
      items.set(name, JSON.parse(value));
    }
    let newValue = [];
    setCheckedItems(items);
    for (const value of items.values()) {
      newValue.push(value.key);
    }
    let filterObj = {
      [fieldName]: [...newValue],
    };
    handleChange(filterObj as any);
  };
  return (
    <div
      className={`form-group my-2 border-bottom p-2 ${styles.borderBottomHide}`}
    >
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ""
      )}
      <div className={`form-check row d-flex ${styles.checkboxContainer}`}>
        {options.map((opt, index) => {
          return (
            <div className={"checkbox col-12"} key={index}>
              <label
                className={`form-check-label ${styles.formCheckLabel}`}
                key={index}
                htmlFor={`${fieldName}-${index}`}
              >
                <input
                  type="checkbox"
                  name={`${fieldName}-${index}`}
                  className={"form-check-input"}
                  value={JSON.stringify(opt)}
                  checked={checkedItems.get(`${fieldName}-${index}`) || false}
                  onChange={handleCheckItem}
                />
                {opt.displayLabel}
              </label>
            </div>
          );
        })}
      </div>
    </div>
  );
};
