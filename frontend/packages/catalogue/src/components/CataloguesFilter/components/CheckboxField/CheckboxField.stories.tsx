/* eslint-disable */
import React from "react";
import { <PERSON><PERSON>, <PERSON> } from "@storybook/react";
import { CheckboxField, CheckboxFieldProps } from "./CheckboxField";

export default {
  title: "Components/FormGenerator/CheckboxField",
  component: CheckboxField,
} as Meta;

const Template: Story<CheckboxFieldProps> = (args) => (
  <CheckboxField {...args} />
);

export const CheckboxForm = Template.bind({});
CheckboxForm.args = {
  name: "name",
  label: "Full name",
  value: "",
  options: [
    { id: 1, name: "K" },
    { id: 2, name: "A" },
    { id: 3, name: "V" },
    { id: 4, name: "I" },
    { id: 5, name: "S" },
    { id: 6, name: "H" },
  ],
};
