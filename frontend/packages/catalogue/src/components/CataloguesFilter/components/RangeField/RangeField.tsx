import React, {
  ChangeEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
// import './RadioField.module.css';
import classnames from "classnames";
import "./RangeField.css";
export interface RangeFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The selectable options
   */
  options: any[];
  /**
   * The value object
   */
  value?: string | number | readonly string[];
  /**
   * The label css
   */
  labelCss: Record<string, string>;

  min: number;
  max: number;
  /**
   * The function to be called on change
   */
  handleChange: Function;
}

export const RangeField: React.FC<RangeFieldProps> = ({
  label,
  name,
  options,
  labelCss,
  min,
  max,
  handleChange,
  ...props
}) => {
  const propsData = props as any;
  const [minVal, setMinVal] = useState(min);
  const [maxVal, setMaxVal] = useState(max);
  const minValRef = useRef<HTMLInputElement>(null);
  const maxValRef = useRef<HTMLInputElement>(null);
  const range = useRef<HTMLDivElement>(null);
  const fieldName = name;
  // const [onChange, setOnChange] = useState<boolean>(false);

  const getPercent = useCallback(
    (value: number) => Math.round(((value - min) / (max - min)) * 100),
    [min, max]
  );

  useEffect(() => {
    if (maxValRef.current) {
      const minPercent = getPercent(minVal);
      const maxPercent = getPercent(+maxValRef.current.value);

      if (range.current) {
        range.current.style.left = `${minPercent}%`;
        range.current.style.width = `${maxPercent - minPercent}%`;
      }
    }
  }, [minVal, getPercent]);

  useEffect(() => {
    if (minValRef.current) {
      const minPercent = getPercent(+minValRef.current.value);
      const maxPercent = getPercent(maxVal);

      if (range.current) {
        range.current.style.width = `${maxPercent - minPercent}%`;
      }
    }
  }, [maxVal, getPercent]);

  useEffect(() => {
    let filterObj = {};
    if (fieldName == "customData.product.vintage") {
      filterObj = {
        [`${fieldName}>`]: [`'${minVal}'`],
        [`${fieldName}<`]: [`'${maxVal}'`],
      };
    } else {
      filterObj = {
        [`${fieldName}>`]: [minVal],
        [`${fieldName}<`]: [maxVal],
      };
    }
    handleChange(filterObj);
  }, [minVal, maxVal]);

  return (
    <div className={"form-group my-2 border-bottom p-2"}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ""
      )}
      <div className={`row mt-2 mr-2 ml-2`}>
        <div className={"col-12 p-0"}>
          <input
            type="range"
            min={min}
            max={max}
            value={minVal}
            ref={minValRef}
            onChange={(event: ChangeEvent<HTMLInputElement>) => {
              const value = Math.min(+event.target.value, maxVal - 1);
              setMinVal(value);
              event.target.value = value.toString();
            }}
            className={classnames("thumb thumb--zindex-3", {
              "thumb--zindex-5": minVal > max - 100,
            })}
          />
          <input
            type="range"
            min={min}
            max={max}
            value={maxVal}
            ref={maxValRef}
            onChange={(event: ChangeEvent<HTMLInputElement>) => {
              const value = Math.max(+event.target.value, minVal + 1);
              setMaxVal(value);
              event.target.value = value.toString();
            }}
            className="thumb thumb--zindex-4"
          />
          <div className="slider">
            <div className="slider__track"></div>
            <div
              ref={range}
              className="slider__range"
              style={{ backgroundColor: propsData?.dataObject?.sliderBgColor }}
            ></div>
            <div
              className="slider__left-value"
              style={{ color: propsData?.dataObject?.color }}
            >
              {propsData?.dataObject?.currency}
              {minVal}
            </div>
            <div
              className="slider__right-value"
              style={{ color: propsData?.dataObject?.color }}
            >
              {propsData?.dataObject?.currency}
              {maxVal}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
