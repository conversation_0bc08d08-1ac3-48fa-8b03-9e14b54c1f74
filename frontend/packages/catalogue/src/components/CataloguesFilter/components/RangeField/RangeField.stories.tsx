/* eslint-disable */
import React from "react";
import { <PERSON>a, Story } from "@storybook/react";
import { RangeField, RangeFieldProps } from "./RangeField";

export default {
  title: "Components/FormGenerator/RangeField",
  component: RangeField,
} as Meta;

const Template: Story<RangeFieldProps> = (args) => <RangeField {...args} />;

export const RangeForm = Template.bind({});
RangeForm.args = {
  name: "name",
  label: "Full name",
  value: "",
  options: [
    { id: 1, name: "K" },
    { id: 2, name: "A" },
    { id: 3, name: "V" },
    { id: 4, name: "I" },
    { id: 5, name: "S" },
    { id: 6, name: "H" },
  ],
};
