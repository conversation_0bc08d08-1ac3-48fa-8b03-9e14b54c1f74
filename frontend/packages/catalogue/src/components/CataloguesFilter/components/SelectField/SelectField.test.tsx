import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { SelectField } from "./SelectField";

const data = {
  name: "name",
  label: "Full name",
  value: "",
  options: [
    { id: 1, name: "<PERSON>" },
    { id: 2, name: "A" },
  ],
  labelCss: {},
  handleBlur: function () {},
  handleChange: function () {},
};

describe("<SelectField />", () => {
  test("it should mount", () => {
    render(<SelectField {...data} />);

    const selectField = screen.getByTestId("SelectField");

    expect(selectField).toBeInTheDocument();
  });
});
