import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { TextField } from "./TextField";

const data = {
  name: "name",
  label: "Full name",
  placeholder: "Enter Full Name",
  subType: "text",
  value: "",
  labelCss: {},
  handleBlur: function () {},
  handleChange: function () {},
};

describe("<TextField />", () => {
  test("it should mount", () => {
    render(<TextField {...data} />);

    const textField = screen.getByTestId("TextField");

    expect(textField).toBeInTheDocument();
  });
});
