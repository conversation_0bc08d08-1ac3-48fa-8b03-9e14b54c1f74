import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RadioField } from "./RadioField";

const data = {
  name: "name",
  label: "Full name",
  value: "",
  options: [
    { id: 1, name: "<PERSON>" },
    { id: 2, name: "A" },
  ],
  labelCss: {},
  readOnly: false,
  handleChange: function () {},
  handleBlur: function () {},
  setFieldValue: function () {},
};

describe("<RadioField />", () => {
  test("it should mount", () => {
    render(<RadioField {...data} />);

    const radioField = screen.getByTestId("RadioField");

    expect(radioField).toBeInTheDocument();
  });
});
