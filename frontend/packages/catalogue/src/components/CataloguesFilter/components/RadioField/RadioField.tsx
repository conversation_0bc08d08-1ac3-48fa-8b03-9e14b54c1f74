import React, { useState } from "react";
// import './RadioField.module.css';
import styles from "./RadioField.module.css";
export interface RadioFieldProps {
  /**
   * The label for the field
   */
  label?: string;
  /**
   * The name field
   */
  name: string;
  /**
   * The selectable options
   */
  options: any[];
  /**
   * The value object
   */
  value?: string | number | readonly string[];
  /**
   * The label css
   */
  labelCss: Record<string, string>;
  /**
   * The function to be called on change
   */
  handleChange: React.ChangeEventHandler<HTMLInputElement>;
}

export const RadioField: React.FC<RadioFieldProps> = ({
  label,
  name,
  options,
  labelCss,
  handleChange,
  ...props
}) => {
  const [selectedItem, setSelectedItem] = useState("");
  const fieldName = name;

  const handleSelectItem = (event: any) => {
    const { name, value } = event.target;
    const parsedValue = JSON.parse(value);
    setSelectedItem(name);
    let filterObj = {
      [fieldName]: [parsedValue.key],
    };
    handleChange(filterObj as any);
  };

  return (
    <div className={"form-group my-2 border-bottom p-2"}>
      {label ? (
        <label htmlFor={name} style={labelCss} className="mb-1 mt-2">
          {label}
        </label>
      ) : (
        ""
      )}
      <div className={`form-radio row ${styles.radioContainer}`}>
        {options.map((opt, index) => {
          return (
            <div className={"radiobox col-12"}>
              <label
                className={`form-radio-label ${styles.formRadioLabel}`}
                key={index}
                htmlFor={`${fieldName}-${index}`}
              >
                <input
                  type="radio"
                  name={`${fieldName}-${index}`}
                  className={"form-select-input"}
                  value={JSON.stringify(opt)}
                  checked={selectedItem === `${fieldName}-${index}` || false}
                  onChange={handleSelectItem}
                />
                {opt.displayLabel}
              </label>
            </div>
          );
        })}
      </div>
    </div>
  );
};
