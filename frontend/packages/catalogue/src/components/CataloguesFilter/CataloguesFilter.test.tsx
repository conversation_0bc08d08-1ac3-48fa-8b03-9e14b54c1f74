import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import "bootstrap/dist/css/bootstrap.min.css";
import { CataloguesFilter } from "./CataloguesFilter";

export const data = [
  {
    id: "name",
    label: "Full name",
    placeholder: "Enter full name",
    type: "text",
    validationType: "string",
    value: "",
    validations: [
      {
        type: "required",
        params: ["name is required"],
      },
      {
        type: "min",
        params: [5, "Nannot be less than 5 characters"],
      },
      {
        type: "max",
        params: [10, "Name cannot be more than 10 characters"],
      },
    ],
  },
];

describe("<FormGenerator />", () => {
  test("it should mount", () => {
    render(<CataloguesFilter fields={data} formikProps={{}} />);

    const formGenerator = screen.getByTestId("FormGenerator");

    expect(formGenerator).toBeInTheDocument();
  });
});
