import React, { useState } from "react";
import TextField from "./components/TextField/TextField.lazy";
import SelectField from "./components/SelectField/SelectField.lazy";
import CheckboxField from "./components/CheckboxField/CheckboxField.lazy";
import RadioField from "./components/RadioField/RadioField.lazy";
import styles from "./CataloguesFilter.module.css";
import RangeField from "./components/RangeField/RangeField.lazy";
export interface CataloguesFilterProps {
  /**
   * The form generator fields
   */
  fields: Record<string, any>;
  /**
   * The label css
   */
  labelColor: Record<string, string>;
  /**
   * The background css
   */
  backgroundColor: Record<string, string>;
  /**
   * The button css
   */
  buttonColor: Record<string, string>;
  /**
   * The formik props generator fields
   */
  formikProps?: Record<string, any>;
  /**
   * The api configurations
   */
  apiConfigurations?: Record<string, string>;

  onChangeFilter?: (query: string) => void;

  onApplyFilter?: (query: string) => void;
}

const fieldMap: any = {
  checkbox: CheckboxField,
  select: SelectField,
  radio: RadioField,
  text: TextField,
  range: RangeField,
};

export const CataloguesFilter: React.FC<CataloguesFilterProps> = ({
  fields,
  labelColor,
  backgroundColor,
  buttonColor,
  formikProps,
  apiConfigurations,
  ...props
}) => {
  const { errors, touched, values, handleBlur, setFieldValue } =
    formikProps || {};
  const [seletedFilter, setSeletedFilter] = useState<any>({});
  const FilterGenerator = (item: any, index: number) => {
    const onChangeHandler = (data: any) => {
      setSeletedFilter({ ...seletedFilter, ...data });
      const filter = { ...seletedFilter, ...data };
      Object.keys(filter).forEach((key) => {
        filter[key].length === 0 ? delete filter[key] : filter[key];
      });
      props.onChangeFilter && props.onChangeFilter(buildURLQuery(filter));
    };

    const Component = fieldMap[item.type];
    const error = errors && errors.hasOwnProperty(item.id) && errors[item.id];
    if (!item.type || !Component) {
      return null;
    }
    return (
      <Component
        key={index}
        label={item.title}
        name={item.key}
        type={item.type}
        subType={item.subType}
        placeholder={item.placeholder}
        value={values ? values[item.id] : ""}
        options={item.options}
        touched={touched}
        error={error}
        readOnly={item.readOnly || false}
        override={item.override || false}
        labelCss={labelColor}
        backgroundCss={backgroundColor}
        buttonCss={buttonColor}
        maxAllowedDays={item.maxAllowedDays}
        minAllowedDays={item.minAllowedDays}
        showTimeSelect={item.showTimeSelect}
        showMonthSelect={item.showMonthSelect}
        showYearSelect={item.showYearSelect}
        use12Hours={item.use12Hours}
        displayFormat={item.displayFormat}
        handleBlur={handleBlur}
        handleChange={onChangeHandler}
        setFieldValue={setFieldValue}
        apiConfigurations={apiConfigurations || {}}
        apiKey={item.apiKey}
        searchable={item.searchable}
        timeIntervals={item.timeIntervals}
        configurations={item.configurations}
        dataObject={item}
        min={item.min}
        max={item.max}
      />
    );
  };

  // const onApplyFilter = () => {
  //   props.onApplyFilter && props.onApplyFilter(buildURLQuery(seletedFilter));
  // };

  const buildURLQuery = (obj: any) =>
    Object.entries(obj)
      .map((pair) => pair.map((value: any) => value.toString()).join("="))
      .join("&");

  return (
    <div className="filter__container border">
      <div className="col-12 d-flex justify-content-between filter__heading">
        <div className="filter__heading__text">Filter</div>
        {/* <div className="filter__heading__btn" onClick={onApplyFilter}>
          Apply
        </div> */}
      </div>
      <div
        className={`col-12 p-0 filter__fields__container ${styles.borderBottomHide}`}
      >
        {fields.map((item: any, index: number) => {
          return FilterGenerator(item, index);
        })}
      </div>
    </div>
  );
};
