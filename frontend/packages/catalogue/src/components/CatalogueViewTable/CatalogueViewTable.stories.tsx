/* eslint-disable */
import React from "react";
import { <PERSON>a, Story } from "@storybook/react";
import {
  CatalogueViewTable,
  CatalogueViewTableProps,
} from "./CatalogueViewTable";

export default {
  title: "Components/CatalogueViewTable",
  component: CatalogueViewTable,
} as Meta;

const Template: Story<CatalogueViewTableProps> = (args) => (
  <CatalogueViewTable {...args} />
);

export const CatalogueViewTableComponent = Template.bind({});
CatalogueViewTableComponent.args = {
  catalogues: [],
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#FFFFFF",
    primary: "#3B8396",
  },
  moduleConfigurations: {
    defaultQueryParams: "type=PRODUCT",
    actions: [
      {
        type: "edit",
        label: "Edit",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
  },
};
