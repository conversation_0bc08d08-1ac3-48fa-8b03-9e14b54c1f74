import React, { useState } from "react";
import { useTable, useFilters, useGlobalFilter, useSortBy } from "react-table";
import styles from "./CataloguesAccordian.module.css";
import { ThemeColors } from "..";
import { Accordion, Button, Card, Table } from "react-bootstrap";
import { size } from "lodash";
export interface CataloguesAccordianProps {
  /**
   * The catalogues data
   */
  data: any;
  /**
   * The colors to be utilized for the component
   */
  themeColors?: ThemeColors;
  /**
   * The configurations required for module
   */
  moduleConfigurations?: Record<string, any>;
  /**
   * The columns data
   */
  columns: any;
  /**
   * The edit callback call
   */
  callback?: any;
}

export const CataloguesAccordian: React.FC<CataloguesAccordianProps> = ({
  data,
  columns,
  moduleConfigurations,
  themeColors,
  callback,
  ...props
}) => {
  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } =
    useTable(
      {
        columns,
        data,
      },
      useFilters,
      useGlobalFilter,
      useSortBy
    );
  const actions = moduleConfigurations?.actions || [];
  const [idx, setIdx] = useState<any>([]);
  return (
    <div className={`${styles.accordianParent}`} data-testid="CatalogueTable">
      <Accordion>
        {rows?.map((row: any, index: number) => {
          prepareRow(row);
          return (
            <Card className={`${styles.accordianCard}`} key={index}>
              <Card.Header className={`${styles.accordianCardHeader}`}>
                <Accordion.Toggle
                  as={Button}
                  variant="link"
                  eventKey={`${index}`}
                  className={`${styles.accordianToggleBtn}`}
                  bsPrefix=""
                  onClick={() => {
                    if (idx.includes(index))
                      setIdx(idx.filter((i: any) => i !== index));
                    else setIdx([index]);
                  }}
                >
                  <span>{row?.original?.name}</span>
                  <img src={idx.includes(index) ? "" : ""} alt="" />
                </Accordion.Toggle>
              </Card.Header>
              <Accordion.Collapse eventKey={`${index}`}>
                <Card.Body className={`${styles.accordianCardBody}`}>
                  <Table hover size="sm" {...getTableProps()}>
                    <tbody
                      className={`${styles.accordianTableBody}`}
                      {...getTableBodyProps()}
                    >
                      <div className={styles.main}>
                        <div>
                          <div className={styles.headers}>
                            {headerGroups.map((headerGroup: any) => (
                              <div {...headerGroup.getHeaderGroupProps()}>
                                {headerGroup.headers.map(
                                  (column: any, key: any) => (
                                    <div>
                                      <td
                                        {...column.getHeaderProps(
                                          column.getSortByToggleProps()
                                        )}
                                      >
                                        {column.render("Header")}
                                      </td>
                                    </div>
                                  )
                                )}
                              </div>
                            ))}
                          </div>
                          <td>Actions</td>
                        </div>
                        <div>
                          <div>
                            {row.cells.map((cell: any) => (
                              <div>
                                <td {...cell.getCellProps()}>
                                  {cell.render("Cell")}
                                </td>
                              </div>
                            ))}
                          </div>
                          <div>
                            <td>
                              {actions.map((action: any, index: number) => {
                                return (action.field &&
                                  row.original[action.field] ===
                                    action.defaultValue) ||
                                  (typeof row.original[action.field] ===
                                    "object" &&
                                    Object.keys(row.original[action.field])
                                      .length > 0) ? (
                                  <button
                                    className={`${styles.accordianEditBtn}`}
                                    key={`${index}`}
                                    onClick={() =>
                                      callback(row, action.antiType, action)
                                    }
                                  >
                                    {action.antiLabel.toLowerCase()}
                                  </button>
                                ) : (
                                  <button
                                    className={`${styles.accordianEditBtn}`}
                                    key={`${index}`}
                                    onClick={() =>
                                      callback(row, action.type, action)
                                    }
                                  >
                                    {action.label.toLowerCase()}
                                  </button>
                                );
                              })}
                            </td>
                          </div>
                        </div>
                      </div>
                    </tbody>
                  </Table>
                </Card.Body>
              </Accordion.Collapse>
            </Card>
          );
        })}
      </Accordion>
    </div>
  );
};
