/* eslint-disable */
import React from "react";
import { <PERSON><PERSON>, Story } from "@storybook/react";
import {
  CataloguesAccordian,
  CataloguesAccordianProps,
} from "./CataloguesAccordian";

export default {
  title: "Components/CataloguesAccordian",
  component: CataloguesAccordian,
} as Meta;

const Template: Story<CataloguesAccordianProps> = (args) => (
  <CataloguesAccordian {...args} />
);

export const CataloguesAccordianComponent = Template.bind({});
CataloguesAccordianComponent.args = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#FFFFFF",
    primary: "#3B8396",
  },
  moduleConfigurations: {
    defaultQueryParams: "matchUsers=true&type[$ne]=employee",
    actions: [
      {
        type: "edit",
        label: "EDIT",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
  },
};
