import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { Catalogues } from "./Catalogues";

const data = {
  title: "Products",
  helpText: "This is where you can view and manage your products",
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#FFFFFF",
    primary: "#3B8396",
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: "type=PRODUCT",
    actions: [
      {
        type: "edit",
        label: "Edit",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
  },
  apiConfigurations: {
    baseUrl: "http://localhost:3104",
    userId: "607e6bd098b6013bdf13d854",
    token: "",
  },
};

describe("<Catalogues />", () => {
  test("it should mount", () => {
    render(<Catalogues {...data} />);

    const catalogues = screen.getByTestId("Catalogues");

    expect(catalogues).toBeInTheDocument();
  });
});
