import React, { useEffect, useState } from "react";
import {
  Icon,
  InfoBox,
  PageHeader,
  ProgressBar,
  Search,
  ErrorHandler,
} from "@frontend/core";
import "@frontend/core/lib/index.css"
import { Button, Col, Row } from "react-bootstrap";
import axios, { AxiosRequestConfig } from "axios";
import { size } from "lodash";
import Pagination from "react-js-pagination";
import Dot from "dot-object";
import Swal from "sweetalert2";
import "bootstrap/dist/css/bootstrap.min.css";
import "react-datepicker/dist/react-datepicker.css";
import { ActionForm } from "../ActionForm/ActionForm.lazy";
import { CatalogueViewTable } from "../CatalogueViewTable/CatalogueViewTable.lazy";
import { ApiConfigurations, ThemeColors } from "../SharedExports";
import styles from "./Catalogues.module.css";
import { CataloguesAccordian } from "../CataloguesAccordian/CataloguesAccordian.lazy";
import { Form } from "react-bootstrap";

export interface CataloguesProps {
  /**
   * The colors to be utilized for the component
   */
  themeColors: ThemeColors;
  /**
   * The help text of the component
   */
  helpText: string;
  /**
   * The title of the header
   */
  title: string;
  /**
   * The type of loader to be used
   */
  loaderType?: any;
  /**
   * The configurations required for API call
   */
  apiConfigurations: ApiConfigurations;
  /**
   * The configurations required for module
   */
  moduleConfigurations: Record<string, any>;
}

const staticColumns = [
  {
    id: "createdAt",
    filter: "date",
    accessor: (d: any) => {
      return new Date(d.createdAt).toLocaleDateString();
    },
  },
  {
    id: "paymentOptions-credit",
    accessor: (d: any) => {
      if (d.paymentOptions?.methods) {
        const method = d.paymentOptions?.methods.find(
          (item: any) => item.type === "CREDIT"
        );
        return method?.basePrice || "-";
      }
      return "-";
    },
  },
];
const dotHyphen = new Dot("-");
const dot = new Dot(".");

export const Catalogues: React.FC<CataloguesProps> = ({
  themeColors,
  helpText,
  title,
  loaderType,
  moduleConfigurations,
  apiConfigurations,
  ...props
}) => {
  let skip = 0;
  const limit = 10;
  const isSearchEnable = moduleConfigurations.isSearchEnable || false;
  const isFilterEnable = moduleConfigurations.isFilterEnable || false;
  const isImportEnable = moduleConfigurations.isImportEnable || false;
  const isAddEnable = moduleConfigurations.isAddEnable || false;
  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  loaderType = loaderType || "Audio";

  const [catalogues, setCatalogues] = useState();
  const [selectedCatalogue, setSelectedCatalogue] = useState();
  const [catalogueFetched, setCatalogueFetched] = useState(false);
  const [apiResponse, setApiResponse] = useState<any>({});
  const [defaultValue, setDefaultValue] = useState<any>({});
  const [activePage, setActivePage] = useState(1);
  const [dataColumns, setDataColumns] = useState([]);
  const [isColumnsSet, setIsColumnsSet] = useState(false);
  const [queryParams, setQueryParams] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [
    showStateWiseAdvancePricingModal,
    setShowStateWiseAdvancePricingModal,
  ] = useState(false);
  const [showEditPricing, setShowEditPricing] = useState(false);
  const [modalConfig, setModalConfig] = useState();
  const [formType, setFormType] = useState("");
  const [editId, setEditId] = useState("");
  const [inputFile, setInputFile] = useState<HTMLInputElement | null>(null);

  useEffect(() => {
    combinedDataColumns(moduleConfigurations.dataColumns, staticColumns);
    callGetCataloguesService(skip, "");
    getOnLoadDefaultValues(moduleConfigurations.onLoadConfig);
    setInputFile(document.getElementById("upload-file") as HTMLInputElement);
  }, []);

  function callGetCataloguesService(skipValue: number, queryString: string) {
    setCatalogueFetched(false);
    let defaultQuery = `?offset=${skipValue}&limit=${limit}`;
    if (size(moduleConfigurations?.defaultQueryParams) > 0) {
      defaultQuery += `&${moduleConfigurations.defaultQueryParams}`;
    }
    if (size(queryString) > 0) {
      defaultQuery += `&${queryString}`;
    }
    const url = `${apiConfigurations.baseUrl}/catalogue${defaultQuery}`;
    let requestConfig: AxiosRequestConfig = {
      method: "GET",
      url,
      headers: {
        "content-type": "application/json",
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        "x-sworks-timezone": userTimeZone,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        processDataOnGet(res);
      })
      .catch((err: any) => {
        processErrorDataOnGet(err);
      });
  }

  function callGetCataloguesServiceWithoutSkip(
    configuration: Record<string, any>
  ) {
    const limit = configuration.limit || 20;
    let defaultQuery = `?limit=${limit}`;
    if (size(configuration?.defaultQueryParams) > 0) {
      defaultQuery += `&${configuration.defaultQueryParams}`;
    }
    const url = `${apiConfigurations.baseUrl}/catalogue${defaultQuery}`;
    let requestConfig: AxiosRequestConfig = {
      method: "GET",
      url,
      headers: {
        "content-type": "application/json",
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        "x-sworks-timezone": userTimeZone,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        setDefaultValue({
          ...defaultValue,
          [configuration.key]: {
            data: res.data?.result?.categories || [],
            config: configuration,
          },
        });
      })
      .catch((err: any) => {
        return [];
      });
  }

  function callDeleteCatalogueService(id: string, type: string) {
    setCatalogueFetched(false);
    const url = `${apiConfigurations.baseUrl}/catalogue/${id}`;
    let requestConfig: AxiosRequestConfig = {
      method: "DELETE",
      url,
      headers: {
        "content-type": "application/json",
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        "x-sworks-timezone": userTimeZone,
      },
      params: {
        type,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .delete(url, requestConfig)
      .then((res: any) => {
        processDataOnDelete(res);
      })
      .catch((err: any) => {
        processErrorDataOnDelete(err);
      });
  }

  function callEditCatalogueService(data: any, staticData: any) {
    setCatalogueFetched(false);
    staticData = staticData || {};
    const url = `${apiConfigurations.baseUrl}/catalogue/${editId}`;
    let requestConfig: AxiosRequestConfig = {
      method: "PATCH",
      url,
      headers: {
        "content-type": "application/json",
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        "x-sworks-timezone": userTimeZone,
      },
      data: { ...data, ...staticData },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .patch(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnEdit(res);
      })
      .catch((err: any) => {
        processErrorDataOnEdit(err);
      });
  }

  function callCreateCatalogueService(data: any, staticData: any) {
    staticData = staticData || {};
    setCatalogueFetched(false);
    const url = `${apiConfigurations.baseUrl}/catalogue`;
    let requestConfig: AxiosRequestConfig = {
      method: "POST",
      url,
      headers: {
        "content-type": "application/json",
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        "x-sworks-timezone": userTimeZone,
      },
      data: dot.object({
        ...data,
        ...staticData,
      }),
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .post(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnCreate(res);
      })
      .catch((err: any) => {
        processErrorDataOnCreate(err);
      });
  }

  function callCatalogueServiceForUploadCsv(formData: any) {
    setCatalogueFetched(false);
    let url = `${apiConfigurations.baseUrl}/catalogue/upload`;
    if (moduleConfigurations.uploadConfig?.type) {
      url += `?type=${moduleConfigurations.uploadConfig?.type}`;
    }
    let requestConfig: AxiosRequestConfig = {
      method: "POST",
      url,
      headers: {
        "content-type": "application/json",
        "x-sworks-timezone": userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data: formData,
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .post(url, formData, requestConfig)
      .then((res: any) => {
        processDataOnCsvUpload(res);
      })
      .catch((err: any) => {
        processErrorOnCsvUpload(err);
      });
  }

  function getOnLoadDefaultValues(onLoadConfig: Record<string, any>) {
    if (onLoadConfig?.selectableValue) {
      for (const item of onLoadConfig.selectableValue) {
        if (item.type === "default") {
          callGetCataloguesServiceWithoutSkip(item);
        }
      }
    }
  }

  function processDataOnGet(res: any) {
    if (res.data?.result) {
      setApiResponse(res.data.result);
      const catalogues = res.data.result?.categories || [];
      setCatalogues(catalogues);
      setCatalogueFetched(true);
    }
  }

  function processErrorDataOnGet(err: any) {}

  function processDataOnDelete(res: any) {
    if (res.data?.result) {
      callGetCataloguesService(skip, queryParams);
    }
  }

  function processErrorDataOnDelete(err: any) {}

  function processDataOnEdit(res: any) {
    if (res.data?.result) {
      dismissActionForm();
      const offsetValue = (activePage - 1) * limit;
      callGetCataloguesService(offsetValue, queryParams);
    }
  }

  function processErrorDataOnEdit(err: any) {}

  function processDataOnCreate(res: any) {
    if (res.data?.result) {
      dismissActionForm();
      callGetCataloguesService(skip, queryParams);
    }
  }

  function processErrorDataOnCreate(err: any) {}

  function processDataOnCsvUpload(res: any) {
    if (res.data.status === 1) {
      setCatalogueFetched(true);
      Swal.fire({
        icon: "info",
        title: "In-Progress...",
        text: `Your upload is being processed. Soon ${title.toLowerCase()} would be visible for usage.`,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    } else {
      processErrorOnCsvUpload(res);
    }
  }

  function processErrorOnCsvUpload(err: any) {
    setCatalogueFetched(true);
    const errorMessage = `Please sync with the system admin if you feel something is not right`;
    const message =
      err.data?.error && err.data?.error.errorCode !== 6000
        ? err.data.error?.errorMessage
        : errorMessage;
    Swal.fire({
      icon: "error",
      title: "Oops...",
      text: message,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  const handleUpload = () => {
    inputFile?.click();
  };

  const handleChange = (file: any) => {
    uploadCatalogueCsvFile(file.target.files[0]);
  };

  function uploadCatalogueCsvFile(data: any) {
    const formData = new FormData();
    formData.append("file", data, data.name);
    formData.append("type", moduleConfigurations.uploadConfig?.type);
    const size = data.size / Math.pow(10, 6);
    const allowedSize = moduleConfigurations.uploadConfig?.size || 10;
    const allowedFileType = ["text/csv", "application/vnd.ms-excel"];
    if (allowedFileType.indexOf(data.type) === -1) {
      Swal.fire({
        icon: "warning",
        title: "Forbidden",
        text: "Please upload a CSV file to proceed",
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    } else if (size >= allowedSize) {
      Swal.fire({
        icon: "warning",
        title: "Forbidden",
        text: `Please upload a file of size less than ${allowedSize}MB`,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    } else {
      callCatalogueServiceForUploadCsv(formData);
    }
  }

  function handlePageChange(pageNumber: number) {
    if (pageNumber > 1) {
      skip = (pageNumber - 1) * limit;
    } else {
      skip = 0;
    }
    setActivePage(pageNumber);
    callGetCataloguesService(skip, queryParams);
  }

  function callback(data: any, action: string) {
    switch (action) {
      case "edit":
        showEditView(data.original);
        break;
      case "reactivate":
        softDeleteRow(data.original, "reactivate");
        break;
      case "soft-delete":
        softDeleteRow(data.original, "remove");
        break;
      case "edit-state-pricing":
        showStateWisePriceEditView(data.original);
        break;
      case "edit-pricing":
        showEditPricingView(data.original);
        break;
      default:
        break;
    }
  }

  function softDeleteRow(data: any, type: string) {
    if (type === "remove") {
      Swal.fire({
        title: "Are you sure?",
        text: `User won't be able to see the ${title.toLowerCase()} and it's relevant data!`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: themeColors.backgroundPrimary,
        cancelButtonColor: themeColors.backgroundSecondary,
        confirmButtonText: "Yes, deactivate it!",
      }).then((result) => {
        if (result.isConfirmed) {
          callDeleteCatalogueService(data._id, type);
        }
      });
    } else {
      Swal.fire({
        title: "Are you sure?",
        text: `User will be able to see the ${title.toLowerCase()} and it's relevant data!`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: themeColors.backgroundPrimary,
        cancelButtonColor: themeColors.backgroundSecondary,
        confirmButtonText: "Yes, enable it!",
      }).then((result) => {
        if (result.isConfirmed) {
          callDeleteCatalogueService(data._id, type);
        }
      });
    }
  }

  function combinedDataColumns(dataColumns: any, staticColumns: any) {
    if (!isColumnsSet) {
      for (let item of dataColumns) {
        Object.assign(
          item,
          staticColumns.find((y: any) => y?.id === item?.id)
        );
        if (typeof item.accessor === "object" && item.accessor.function) {
          item.accessor = new Function(
            item.accessor.function.arguments,
            item.accessor.function.body
          );
        }
      }
      setIsColumnsSet(true);
      setDataColumns(dataColumns);
    }
  }

  function filterCatalogues(queryString: string) {
    skip = 0;
    setActivePage(1);
    setQueryParams(queryString);
    callGetCataloguesService(skip, queryString);
  }

  function onCancelClick() {
    dismissActionForm();
  }

  function dismissActionForm() {
    setShowModal(false);
    setShowStateWiseAdvancePricingModal(false);
    setShowEditPricing(false);
    setModalConfig(undefined);
    setFormType("");
    setEditId("");
    setSelectedCatalogue(undefined);
  }

  function showCreateView() {
    setFormType("create");
    const createConfig = moduleConfigurations.formConfig?.createCatalogue;
    if (createConfig) {
      setModalConfig(createConfig);
      for (let item of createConfig.config) {
        if (item.type === "select" && item.options?.length === 0) {
          if (defaultValue[item.id]) {
            item.options =
              defaultValue[item.id].data.map((item: any) => {
                return { id: item._id, name: item.title };
              }) || [];
          }
        }
      }
      setShowModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: "error",
        title: "Oops...",
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  function showEditView(data: any) {
    setEditId(data._id);
    setFormType("edit");
    const editConfig = JSON.parse(
      JSON.stringify(moduleConfigurations.formConfig?.editCatalogue)
    );
    if (editConfig) {
      data = dot.dot(data);
      for (let item of editConfig.config) {
        let id = item.id;
        id = id.replace(/-/g, ".");
        let value = data[id] || item.value;
        item.value = value;
        if (item.type === "select" && item.options?.length === 0) {
          if (defaultValue[item.id]) {
            item.options =
              defaultValue[item.id].data.map((item: any) => {
                return { id: item._id, name: item.title };
              }) || [];
          }
        }
        if (item.id.includes("paymentOptions")) {
          const type = id.split(".");
          data = dot.object(data);
          if (data.paymentOptions?.methods) {
            const method = data.paymentOptions?.methods.find(
              (item: any) => item.type === type[type.length - 1].toUpperCase()
            );
            item.value = method.basePrice || "";
          }
          data = dot.dot(data);
        }
      }
      setModalConfig(editConfig);
      setShowModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: "error",
        title: "Oops...",
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  function showStateWisePriceEditView(data: any) {
    setEditId(data._id);
    setFormType("edit-state-pricing");
    const editAdvancePricingConfig = JSON.parse(
      JSON.stringify(
        moduleConfigurations.formConfig?.editAdvanceCataloguePricing
      )
    );
    if (editAdvancePricingConfig) {
      setSelectedCatalogue(data);
      setModalConfig(editAdvancePricingConfig);
      setShowStateWiseAdvancePricingModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: "error",
        title: "Oops...",
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  const showEditPricingView = (data: any) => {
    setEditId(data._id);
    setFormType("edit-pricing");
    const editPricingConfig = JSON.parse(
      JSON.stringify(moduleConfigurations.formConfig?.editPricing)
    );
    if (editPricingConfig) {
      setSelectedCatalogue(data);
      setModalConfig(editPricingConfig);
      setShowEditPricing(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: "error",
        title: "Oops...",
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  };

  function onSubmitClick(data: any) {
    dotHyphen.object(data);
    if (formType === "edit-pricing") {
      const formConfig = moduleConfigurations.formConfig;
      callEditCatalogueService(data, formConfig.editCatalogue?.staticData);
    } else if (formType !== "edit-state-pricing") {
      data = dot.dot(data);
      const formConfig = moduleConfigurations.formConfig;
      data = derivedCatalogueData(data);
      if (formType === "create") {
        callCreateCatalogueService(
          data,
          formConfig.createCatalogue?.staticData
        );
      } else if (formType === "edit") {
        callEditCatalogueService(data, formConfig.editCatalogue?.staticData);
      }
    } else {
      const formConfig = moduleConfigurations.formConfig;
      callEditCatalogueService(data, formConfig.editCatalogue?.staticData);
    }
  }

  function derivedCatalogueData(catalogue: Record<string, any>) {
    const keys = Object.keys(defaultValue);
    for (const key of keys) {
      const { config, data } = defaultValue[key];
      if (catalogue[key]) {
        const derivedKeys = config.customMappedValues
          ? Object.keys(config.customMappedValues)
          : [];
        if (derivedKeys.length > 0) {
          const selectedItem = data.find(
            (item: any) => catalogue[key] === item._id.toString()
          );
          for (const item of derivedKeys) {
            catalogue[item] = selectedItem[config.customMappedValues[item]];
          }
        }
      }
    }
    if (catalogue["paymentOptions.credit"]) {
      catalogue = {
        ...catalogue,
        paymentOptions: {
          methods: [
            {
              type: "CREDIT",
              label: "Credit Points",
              basePrice: Number(catalogue["paymentOptions.credit"]),
              isEnable: true,
            },
          ],
        },
      };
      if (!catalogue.price) {
        catalogue.price = Number(catalogue["paymentOptions.credit"]);
      }
      delete catalogue["paymentOptions.credit"];
    }
    return catalogue;
  }

  return (
    <>
      {window.innerWidth > 450 ? (
        <div data-testid="Catalogues">
          <PageHeader title={title} fontColor={themeColors?.textSecondary} />
          <InfoBox
            text={helpText}
            noGutter={false}
            iconName={"FaLightbulb"}
            background={themeColors?.backgroundSecondary}
            iconColor={themeColors?.textSecondary}
            fontColor={themeColors?.textAlternate}
          />
          <Row>
            {isSearchEnable && dataColumns?.length > 0 ? (
              <Col>
                <Search
                  dataColumns={dataColumns}
                  key={"search-component"}
                  handleSearchChangeFor={(queryParams: string) =>
                    filterCatalogues(queryParams)
                  }
                />
              </Col>
            ) : (
              ""
            )}
            {isFilterEnable ? (
              <Col
                lg={2}
                xl={2}
                className={`d-flex justify-content-end mb-auto mx-auto`}
              >
                <Icon
                  iconName={"FaFilter"}
                  iconColor={themeColors.textSecondary}
                  label={"Filters"}
                  fontColor={themeColors.textSecondary}
                />
              </Col>
            ) : (
              ""
            )}
            {isImportEnable ? (
              <Col
                lg={2}
                xl={2}
                className={`d-flex justify-content-end mb-auto mx-auto`}
              >
                <label htmlFor={"upload"} className={`d-content`}>
                  <Button
                    size="sm"
                    block
                    id="uploadNewClick"
                    type="button"
                    className={`${styles.addNewButton} p-2`}
                    onClick={handleUpload}
                  >
                    {`Upload ${title}`}
                  </Button>
                </label>
                <input
                  type="file"
                  accept="text/csv"
                  className={`d-none`}
                  id={"upload-file"}
                  name={"upload"}
                  onChange={handleChange}
                />
              </Col>
            ) : (
              ""
            )}
            {isAddEnable ? (
              <Col
                lg={2}
                xl={2}
                className={`d-flex justify-content-end mb-auto mx-auto`}
              >
                <Button
                  size="sm"
                  id="addNewClick"
                  className={styles.addNewButton}
                  type="button"
                  onClick={() => showCreateView()}
                >
                  {`Add ${title}`}
                </Button>
              </Col>
            ) : (
              ""
            )}
          </Row>
          <Row className={styles.showDisplay}>
            {catalogueFetched ? (
              <div>
                {size(catalogues) > 0 ? (
                  <>
                    <Col className={`d-flex`} xs={12}>
                      <CatalogueViewTable
                        catalogues={catalogues}
                        dataColumns={dataColumns}
                        themeColors={themeColors}
                        moduleConfigurations={moduleConfigurations}
                        callback={callback}
                      />
                    </Col>
                    <Col className={`d-flex justify-content-center`}>
                      {/* @ts-ignore */}
                      <Pagination
                        activePage={activePage}
                        itemsCountPerPage={limit}
                        totalItemsCount={apiResponse?.total}
                        pageRangeDisplayed={5}
                        prevPageText={"Prev"}
                        nextPageText={"Next"}
                        onChange={handlePageChange}
                        activeLinkClass={styles.paginationLinkActive}
                        itemClass={`page-item`}
                        linkClass={`page-link ${styles.paginationLink}`}
                      />
                    </Col>
                  </>
                ) : (
                  <ErrorHandler
                    errorCode={"404"}
                    showTryAgainButton={false}
                    errorMsg={`We are unable to find any matching ${title.toLowerCase()} for your search`}
                    occupyFullScreen={false}
                  />
                )}
              </div>
            ) : (
              <ProgressBar
                type={loaderType}
                background={`#0000002F`}
                color={themeColors.backgroundSecondary}
              />
            )}
          </Row>
        </div>
      ) : (
        //Mobile view starts here
        <section>
          <div className={`${styles.topNav}`}>
            <h1>{title}</h1>
            {isAddEnable ? (
              <button
                className={`${styles.addNewBtn}`}
                onClick={() => showCreateView()}
              >{`+ ${title}`}</button>
            ) : null}
          </div>

          <p className={`${styles.tagLine}`}>
            <img src="" alt="" /> {helpText}
          </p>
          <div>
            <Form>
              <Row className="mt-4">
                {isSearchEnable && dataColumns?.length > 0 ? (
                  <Col>
                    <Search
                      dataColumns={dataColumns}
                      key={"search-component"}
                      handleSearchChangeFor={(queryParams: string) =>
                        filterCatalogues(queryParams)
                      }
                    />
                  </Col>
                ) : (
                  ""
                )}
                {isFilterEnable ? (
                  <Col>
                    <Icon
                      iconName={"FaFilter"}
                      iconColor={themeColors.textSecondary}
                      label={"Filters"}
                      fontColor={themeColors.textSecondary}
                    />
                  </Col>
                ) : (
                  ""
                )}
                {isImportEnable ? (
                  <Col>
                    <Button
                      size="sm"
                      id="addNewClick"
                      type="submit"
                      className={styles.addNewButton}
                    >
                      {`Upload ${title}`}
                    </Button>
                  </Col>
                ) : (
                  ""
                )}
              </Row>
            </Form>
          </div>
          {catalogueFetched ? (
            <div>
              {size(catalogues) > 0 ? (
                <>
                  <CataloguesAccordian
                    callback={callback}
                    themeColors={themeColors}
                    moduleConfigurations={moduleConfigurations}
                    columns={dataColumns}
                    data={catalogues}
                    />
                  {/* @ts-ignore */}
                  <Pagination
                    activePage={activePage}
                    itemsCountPerPage={limit}
                    totalItemsCount={apiResponse?.total}
                    pageRangeDisplayed={5}
                    prevPageText={"Prev"}
                    nextPageText={"Next"}
                    onChange={handlePageChange}
                    activeLinkClass={styles.paginationLinkActive}
                    itemClass={`page-item`}
                    linkClass={`page-link ${styles.paginationLink}`}
                  />
                </>
              ) : (
                <ErrorHandler
                  errorCode={"404"}
                  showTryAgainButton={false}
                  errorMsg={`We are unable to find any matching ${title.toLowerCase()} for your search`}
                  occupyFullScreen={false}
                />
              )}
            </div>
          ) : (
            <ProgressBar
              type={loaderType}
              background={`#0000002F`}
              color={themeColors.backgroundSecondary}
            />
          )}
        </section>
        //Mobile view ends here)
      )}
      {showModal ? (
        <ActionForm
          setModal={showModal}
          modalConfig={modalConfig}
          themeColors={themeColors}
          onCancelClick={() => onCancelClick()}
          apiConfigurations={apiConfigurations}
          onSubmitClick={(data: any) => onSubmitClick(data)}
        />
      ) : (
        ""
      )}
    </>
  );
};
