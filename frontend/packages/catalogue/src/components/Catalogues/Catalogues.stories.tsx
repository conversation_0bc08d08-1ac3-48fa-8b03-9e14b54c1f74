/* eslint-disable */
import React from "react";
import { <PERSON>a, Story } from "@storybook/react";
import { Catalogues, CataloguesProps } from "./Catalogues";

export default {
  title: "Components/Catalogues",
  component: Catalogues,
} as Meta;

const Template: Story<CataloguesProps> = (args) => <Catalogues {...args} />;

export const CatalogueComponent = Template.bind({});
CatalogueComponent.args = {
  title: "Facility",
  helpText: "This is where you can view and manage your facility",
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#3B8396",
    primary: "#FFFFFF",
  },
  moduleConfigurations: {
    defaultQueryParams: "type=SERVICE&sort=-_id",
    isSearchEnable: true,
    isAddEnable: true,
    isImportEnable: true,
    uploadConfig: {
      size: 1,
      type: "product",
    },
    dataColumns: [
      {
        id: "customData.parentName",
        Header: "Vehicle Type",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.customData?.parentName ? d.customData.parentName : '-';",
          },
        },
      },
      {
        id: "subTitle",
        Header: "Registration Number",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.subTitle ? d.subTitle : '-';",
          },
        },
      },
      {
        id: "customData.vehicle.brand",
        Header: "Vehicle",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.customData?.vehicle?.brand ? d.customData.vehicle.brand : '-';",
          },
        },
      },
      {
        id: "customData.vehicle.color",
        Header: "Vehicle Color",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.customData?.vehicle?.color ? d.customData.vehicle.color : '-';",
          },
        },
      },
      {
        id: "createdAt",
        Header: "Created At",
      },
    ],
    formConfig: {
      editCatalogue: {
        title: "Edit Vehicle",
        submitLabel: "UPDATE",
        config: [
          {
            id: "iconUrl",
            label: "Vehicle Image",
            placeholder: "",
            type: "upload",
            validationType: "mixed",
            validations: [
              {
                type: "required",
                params: ["Vehicle image is required"],
              },
            ],
          },
          {
            id: "subTitle",
            label: "Vehicle Registration Number",
            placeholder: "Enter Vehicle Registration Number",
            type: "text",
            subType: "text",
            value: "",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Vehicle registration number is required"],
              },
              {
                type: "max",
                params: [
                  20,
                  "Vehicle registration number cannot be more than 20 characters",
                ],
              },
            ],
          },
          {
            id: "customData-vehicle-brand",
            label: "Vehicle Brand",
            placeholder: "Enter Vehicle Brand",
            type: "text",
            subType: "text",
            value: "",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Vehicle brand is required"],
              },
              {
                type: "max",
                params: [20, "Vehicle brand cannot be more than 20 characters"],
              },
            ],
          },
          {
            id: "customData-vehicle-color",
            label: "Vehicle Color",
            placeholder: "Enter Vehicle Color",
            type: "text",
            subType: "text",
            value: "",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Vehicle color is required"],
              },
              {
                type: "max",
                params: [20, "Vehicle color cannot be more than 20 characters"],
              },
            ],
          },
          {
            id: "parentId",
            label: "Service",
            placeholder: "Select Service",
            type: "select",
            validationType: "string",
            value: "",
            options: [],
            validations: [
              {
                type: "required",
                params: ["Service is required"],
              },
            ],
          },
        ],
      },
      createCatalogue: {
        title: "Add Vehicle",
        submitLabel: "CREATE",
        staticData: {
          type: "SERVICE",
          customData: {
            level: 1,
          },
          price: 0,
        },
        config: [
          {
            id: "iconUrl",
            label: "Vehicle Image",
            placeholder: "",
            type: "upload",
            validationType: "mixed",
            validations: [
              {
                type: "required",
                params: ["Vehicle image is required"],
              },
            ],
          },
          {
            id: "subTitle",
            label: "Vehicle Registration Number",
            placeholder: "Enter Vehicle Registration Number",
            type: "text",
            subType: "text",
            value: "",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Vehicle registration number is required"],
              },
              {
                type: "max",
                params: [
                  20,
                  "Vehicle registration number cannot be more than 20 characters",
                ],
              },
            ],
          },
          {
            id: "customData-vehicle-brand",
            label: "Vehicle Brand",
            placeholder: "Enter Vehicle Brand",
            type: "text",
            subType: "text",
            value: "",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Vehicle brand is required"],
              },
              {
                type: "max",
                params: [20, "Vehicle brand cannot be more than 20 characters"],
              },
            ],
          },
          {
            id: "customData-vehicle-color",
            label: "Vehicle Color",
            placeholder: "Enter Vehicle Color",
            type: "text",
            subType: "text",
            value: "",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Vehicle color is required"],
              },
              {
                type: "max",
                params: [20, "Vehicle color cannot be more than 20 characters"],
              },
            ],
          },
          {
            id: "parentId",
            label: "Service",
            placeholder: "Select Service",
            type: "select",
            validationType: "string",
            value: "",
            options: [],
            validations: [
              {
                type: "required",
                params: ["Service is required"],
              },
            ],
          },
        ],
      },
    },
    onLoadConfig: {
      selectableValue: [
        {
          key: "parentId",
          type: "default",
          defaultQueryParams:
            "type=SERVICE_CATEGORY&customData.level=0&isActive=true&sort=title",
          customMappedValues: {
            "customData.parentName": "title",
          },
        },
      ],
    },
    actions: [
      {
        type: "edit",
        label: "EDIT",
      },
      {
        type: "soft-delete",
        label: "DEACTIVATE",
        antiLabel: "ACTIVATE",
        antiType: "reactivate",
        field: "isActive",
        defaultValue: false,
      },
    ],
  },
  apiConfigurations: {
    baseUrl: "http://localhost:3104",
    userId: "62bc17e3ae9651c940f16dda",
    token: "",
  },
};
