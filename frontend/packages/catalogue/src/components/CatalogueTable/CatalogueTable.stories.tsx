/* eslint-disable */
import React from "react";
import { Meta, Story } from "@storybook/react";
import { CatalogueTable, CatalogueTableProps } from "./CatalogueTable";

export default {
  title: "Components/CatalogueTable",
  component: CatalogueTable,
} as Meta;

const Template: Story<CatalogueTableProps> = (args) => (
  <CatalogueTable {...args} />
);

export const CatalogueTableComponent = Template.bind({});
CatalogueTableComponent.args = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#FFFFFF",
    primary: "#3B8396",
  },
  moduleConfigurations: {
    defaultQueryParams: "type=PRODUCT",
    actions: [
      {
        type: "edit",
        label: "EDIT",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
  },
};
