import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { CatalogueTable } from "./CatalogueTable";

const data = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#FFFFFF",
    primary: "#3B8396",
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: "type=PRODUCT",
    actions: [
      {
        type: "edit",
        label: "EDIT",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
  },
};

describe("<CatalogueTable />", () => {
  test("it should mount", () => {
    render(<CatalogueTable {...data} />);

    const catalogueTable = screen.getByTestId("CatalogueTable");

    expect(catalogueTable).toBeInTheDocument();
  });
});
