import React from "react";
import { useTable, useFilters, useGlobalFilter, useSortBy } from "react-table";
import { size } from "lodash";
import styles from "./CatalogueTable.module.css";
import { ThemeColors } from "..";
export interface CatalogueTableProps {
  /**
   * The catalogues data
   */
  data: any;
  /**
   * The colors to be utilized for the component
   */
  themeColors?: ThemeColors;
  /**
   * The configurations required for module
   */
  moduleConfigurations?: Record<string, any>;
  /**
   * The columns data
   */
  columns: any;
  /**
   * The edit callback call
   */
  callback?: any;
}

export const CatalogueTable: React.FC<CatalogueTableProps> = ({
  data,
  columns,
  moduleConfigurations,
  themeColors,
  callback,
  ...props
}) => {
  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } =
    useTable(
      {
        columns,
        data,
      },
      useFilters,
      useGlobalFilter,
      useSortBy
    );
  const actions = moduleConfigurations?.actions || [];
  const hasImage = rows.find((row: any, i: number) => {
    return size(row.original.iconUrl) > 0;
  });

  return (
    <div className={styles.CatalogueTable} data-testid="CatalogueTable">
      <table {...getTableProps()}>
        <thead>
          {headerGroups.map((headerGroup: any) => (
            <tr {...headerGroup.getHeaderGroupProps()}>
              {hasImage ? <th>Image</th> : null}
              {headerGroup.headers.map((column: any) => (
                <th {...column.getHeaderProps(column.getSortByToggleProps())}>
                  {column.render("Header")}
                </th>
              ))}
              {actions?.length > 0 ? <th>Action</th> : ""}
            </tr>
          ))}
        </thead>
        <tbody {...getTableBodyProps()}>
          {rows.map((row: any, i: number) => {
            prepareRow(row);
            return (
              <tr {...row.getRowProps()}>
                {hasImage ? (
                  <td className={styles.tableAttribute} key={`product-img${i}`}>
                    <img
                      src={
                        size(row.original.iconUrl) > 0
                          ? row.original.iconUrl
                          : ""
                      }
                      className={`${styles.componentImage}`}
                    />
                  </td>
                ) : (
                  ""
                )}
                {row.cells.map((cell: any) => {
                  return (
                    <td {...cell.getCellProps()}>{cell.render("Cell")}</td>
                  );
                })}

                {actions?.length > 0 ? (
                  <td>
                    {actions.map((action: any, index: number) => {
                      return action.field &&
                        row.original[action.field] === action.defaultValue ? (
                        <span
                          key={`${index}`}
                          className={styles.actionLabel}
                          onClick={() => callback(row, action.antiType)}
                        >
                          {action.antiLabel}
                        </span>
                      ) : (
                        <span
                          key={`${index}`}
                          className={styles.actionLabel}
                          onClick={() => callback(row, action.type)}
                        >
                          {action.label}
                        </span>
                      );
                    })}
                  </td>
                ) : (
                  ""
                )}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};
