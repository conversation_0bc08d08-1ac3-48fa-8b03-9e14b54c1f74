import React from "react";
import { <PERSON><PERSON>, <PERSON> } from "@storybook/react";
import { ActionForm, ActionFormProps } from "./ActionForm";

export default {
  title: "Components/ActionForm",
  component: ActionForm,
} as Meta;

const Template: Story<ActionFormProps> = (args) => <ActionForm {...args} />;

export const ActionFormComponent = Template.bind({});
ActionFormComponent.args = {
  setModal: true,
  modalConfig: [],
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#3B8396",
    primary: "#FFFFFF",
  },
  apiConfigurations: {
    baseUrl: "http://localhost:3104",
    userId: "607e6bd098b6013bdf13d854",
    token: "",
  },
};
