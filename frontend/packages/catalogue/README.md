# Cleveland Web Catalogue

A React component library for catalogue management dashboard built with TypeScript, Storybook, and modern development tools.

## 📋 Overview

This project provides a comprehensive set of React components for managing catalogues in web applications. It includes components for displaying, filtering, creating, editing, and deleting catalogue data with a responsive design that works across desktop and mobile devices.

## 🚀 Quick Start

### Prerequisites

- Node.js (version 14 or higher)
- Yarn package manager
- `NODE_AUTH_TOKEN` environment variable set with appropriate GitHub registry access

### Installation

```bash
# Install dependencies
yarn bootstrap

# Start Storybook development server
yarn start
```

Open [http://localhost:6006](http://localhost:6006) to view the component library in Storybook.

## 📦 Installation as Package

```bash
# Install from GitHub registry
yarn add @sworksio/cleveland-web-catalogue
```

## 🏗️ Components

### Core Components

- **`Catalogues`** - Main component that orchestrates catalogue management functionality
- **`CatalogueTable`** - Displays catalogue data in a sortable table format
- **`CatalogueViewTable`** - Wrapper component for table styling and layout
- **`CataloguesAccordian`** - Mobile-friendly accordion view for catalogue data
- **`ActionForm`** - Dynamic form component for creating and editing catalogues
- **`CataloguesFilter`** - Advanced filtering interface with multiple field types

### Filter Components

- **`TextField`** - Text input filtering
- **`SelectField`** - Dropdown selection filtering
- **`CheckboxField`** - Multi-select checkbox filtering
- **`RadioField`** - Single-select radio button filtering
- **`RangeField`** - Numeric range filtering

## 🛠️ Development

### Available Scripts

| Script | Description |
|--------|-------------|
| `yarn start` | Start Storybook development server on port 6006 |
| `yarn test` | Run test suite with Jest |
| `yarn build` | Build the library for production |
| `yarn bootstrap` | Install project dependencies |
| `yarn clean` | Remove build artifacts |
| `yarn format` | Format code with Prettier |
| `yarn verify` | Clean, install, and build (used in CI) |
| `yarn analyze` | Analyze bundle size with source-map-explorer |
| `yarn build-storybook` | Build static Storybook site |

### Development Workflow

1. **Component Development**: Use Storybook for isolated component development
2. **Testing**: Write tests using Jest and React Testing Library
3. **Code Generation**: Use `generate-react-cli` for consistent component scaffolding
4. **Formatting**: Prettier ensures consistent code style

### Creating New Components

```bash
# Generate a new component with all boilerplate
npx generate-react-cli component ComponentName
```

This creates:
- Component file with TypeScript
- CSS module for styling
- Test file with React Testing Library
- Storybook story
- Lazy-loaded version
- Index file for exports

## 🏛️ Architecture

### Tech Stack

- **React 18** - UI library
- **TypeScript** - Type safety
- **Styled Components** - CSS-in-JS styling
- **React Bootstrap** - UI components
- **Storybook** - Component development environment
- **Rollup** - Module bundler
- **Jest & React Testing Library** - Testing framework

### Dependencies

#### Core Dependencies
- `@sworksio/dashboard-core` - Internal dashboard utilities
- `react-table` - Table functionality
- `react-router-dom` - Routing
- `axios` - HTTP client
- `lodash` - Utility functions
- `sweetalert2` - User notifications

#### Development Dependencies
- `@storybook/*` - Component development
- `@testing-library/*` - Testing utilities
- `@rollup/*` - Build tools
- `typescript` - Type checking

## 📱 Features

- **Responsive Design** - Works on desktop and mobile devices
- **Advanced Filtering** - Multiple filter types with real-time search
- **CRUD Operations** - Create, read, update, delete catalogue entries
- **Pagination** - Handle large datasets efficiently
- **CSV Upload** - Bulk import catalogue data
- **Error Handling** - Comprehensive error boundaries and user feedback
- **Accessibility** - Built with accessibility best practices
- **Type Safety** - Full TypeScript support

## 🔧 Configuration

### Environment Variables

```bash
NODE_AUTH_TOKEN=your_github_token_here
```

### Build Configuration

The project uses Rollup for building with the following outputs:
- **CommonJS** (`lib/index.js`) - For Node.js environments
- **ES Modules** (`lib/index.esm.js`) - For modern bundlers
- **TypeScript Declarations** (`lib/index.d.ts`) - For type support

## 🧪 Testing

```bash
# Run tests
yarn test

# Run tests in watch mode
yarn test --watch
```

Tests are written using Jest and React Testing Library, focusing on:
- Component rendering
- User interactions
- Props validation
- Error states

## 📚 Storybook

Storybook provides an interactive development environment where you can:
- View components in isolation
- Test different props and states
- Document component APIs
- Share components with stakeholders

## 🚀 Deployment

### Building for Production

```bash
yarn build
```

### Publishing

The package is automatically published to GitHub Package Registry via GitHub Actions when a new release is created.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run `yarn verify` to ensure everything builds
6. Submit a pull request

## 📄 License

ISC License

## 🔗 Related Projects

- `@sworksio/dashboard-core` - Core dashboard utilities and components

---

For more information, visit the [Storybook documentation](http://localhost:6006) when running the development server.
