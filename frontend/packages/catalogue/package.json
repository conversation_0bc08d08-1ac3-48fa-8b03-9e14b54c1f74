{"name": "@frontend/catalogue", "version": "0.0.0", "description": "This is front end dashboard code base for catalogue library", "main": "lib/index.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "scripts": {"analyze": "source-map-explorer 'lib/*'", "bootstrap": "yarn install", "build": "rollup -c", "clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write \"src/**/*\"", "start": "start-storybook -p 6006", "test": "yarn test", "verify": "npm-run-all clean bootstrap build", "preversion": "yarn verify", "build-storybook": "build-storybook"}, "keywords": ["dashboard", "catalogue", "react"], "author": "", "license": "ISC", "dependencies": {"@frontend/core": "*", "@types/dot-object": "^2.1.2", "axios": "^0.26.1", "bootstrap": "^5.2.3", "dot-object": "^2.1.4", "lodash": "^4.17.21", "react-bootstrap": "1.5.2", "react-error-boundary": "^3.1.4", "react-js-pagination": "^3.0.3", "react-router-dom": "5.3.4", "react-table": "^7.7.0", "styled-components": "^5.3.5", "sweetalert2": "^11.4.8"}, "devDependencies": {"@babel/core": "^7.17.9", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^21.0.3", "@rollup/plugin-node-resolve": "^13.2.0", "@storybook/addon-actions": "^6.4.21", "@storybook/addon-essentials": "^6.4.21", "@storybook/addon-links": "^6.4.21", "@storybook/addon-postcss": "^2.0.0", "@storybook/react": "^6.4.21", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.0.1", "@types/jest": "^27.4.1", "@types/react": "^17.0.2", "@types/react-js-pagination": "^3.0.4", "@types/react-table": "^7.7.10", "@types/styled-components": "^5.1.25", "babel-loader": "^8.2.4", "generate-react-cli": "^7.1.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.12", "prettier": "^2.6.2", "react": "^17.0.2", "rimraf": "^3.0.2", "rollup": "^2.70.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "source-map-explorer": "^2.5.2", "storybook-css-modules-preset": "^1.1.1", "terser": "^5.12.1", "typescript": "^4.6.3"}, "files": ["/lib"], "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}