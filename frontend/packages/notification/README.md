# Cleveland Web Notification Dashboard

A React component library for managing notifications through a dashboard interface. This project provides a complete notification management system with CRUD operations, pagination, filtering, and a responsive UI built with <PERSON><PERSON> and <PERSON>tra<PERSON>.

## 🚀 Features

- **Notification Management**: Create, read, update, and delete notifications
- **Interactive Table**: Sortable and filterable notification display using react-table
- **Pagination**: Built-in pagination for large datasets
- **Modal Forms**: Dynamic form generation for creating and editing notifications
- **Responsive Design**: Bootstrap-based responsive UI components
- **TypeScript Support**: Full TypeScript implementation with type definitions
- **Storybook Integration**: Component documentation and development environment
- **Testing Suite**: Comprehensive test coverage with Jest and React Testing Library

## 📦 Installation

### Prerequisites

- Node.js (version 14 or higher)
- Yarn package manager
- `NODE_AUTH_TOKEN` environment variable set with appropriate GitHub package registry permissions

### Install Dependencies

```bash
yarn bootstrap
```

This command installs all project dependencies. Ensure your `NODE_AUTH_TOKEN` is configured globally for accessing private GitHub packages.

## 🛠 Development

### Start Development Server

```bash
yarn start
```

Launches Storybook development server at [http://localhost:6006](http://localhost:6006). The page will reload automatically when you make changes.

### Run Tests

```bash
yarn test
```

Runs the test suite in interactive watch mode using Jest and React Testing Library.

### Build Library

```bash
yarn build
```

Builds the library for production using Rollup, generating both CommonJS and ES modules in the `lib/` directory.

### Format Code

```bash
yarn format
```

Formats all source code using Prettier.

### Analyze Bundle

```bash
yarn analyze
```

Analyzes the built bundle size using source-map-explorer.

## 📁 Project Structure

```
src/
├── components/
│   ├── Notifications/          # Main container component
│   ├── NotificationTable/      # Table display component
│   ├── NotificationViewTable/  # Table wrapper component
│   ├── ActionForm/            # Modal form component
│   └── SharedExports.tsx      # Common interfaces and types
├── global.d.ts               # Global type definitions
└── index.ts                  # Library entry point
```

## 🧩 Components

### Notifications
The main container component that orchestrates the entire notification management system.

**Features:**
- API integration for CRUD operations
- State management for notifications
- Pagination handling
- Form modal management
- Error handling and user feedback

### NotificationTable
A data table component built with react-table for displaying notifications.

**Features:**
- Sortable columns
- Row actions (edit, delete)
- Configurable column display
- Responsive design

### ActionForm
A modal component for creating and editing notifications using dynamic forms.

**Features:**
- Dynamic form generation
- Validation support
- Modal dialog interface
- Form submission handling

## 🔧 Configuration

### API Configuration
Configure API endpoints through the `ApiConfigurations` interface in `SharedExports.tsx`:

```typescript
interface ApiConfigurations {
  getNotifications: string;
  deleteNotification: string;
  editNotification: string;
  createNotification: string;
}
```

### Theme Configuration
Customize colors through the `ThemeColors` interface:

```typescript
interface ThemeColors {
  primary: string;
  secondary: string;
  // ... other color properties
}
```

## 🧪 Testing

The project uses Jest and React Testing Library for testing. Test files are located alongside their corresponding components with the `.test.tsx` extension.

Run tests with:
```bash
yarn test
```

## 📚 Storybook

Component documentation and development environment is available through Storybook. Stories are defined in `.stories.tsx` files alongside components.

Access Storybook at [http://localhost:6006](http://localhost:6006) when running the development server.

## 🏗 Build Process

The project uses Rollup for building the library with the following features:

- **TypeScript compilation** with type definitions
- **CSS modules** with PostCSS processing
- **Code minification** with Terser
- **Peer dependency externalization**
- **Source map generation**

Output formats:
- CommonJS (`lib/index.js`)
- ES Modules (`lib/index.esm.js`)
- Type definitions (`lib/index.d.ts`)

## 📋 Dependencies

### Runtime Dependencies
- `@sworksio/dashboard-core` - Core dashboard components
- `react-table` - Table functionality
- `react-bootstrap` - UI components
- `axios` - HTTP client
- `sweetalert2` - Alert dialogs
- `lodash` - Utility functions

### Development Dependencies
- `typescript` - Type checking
- `rollup` - Module bundler
- `storybook` - Component development
- `jest` - Testing framework
- `prettier` - Code formatting

## 🚀 Publishing

The package is configured to publish to GitHub Package Registry. The build process includes:

1. Clean previous builds
2. Install dependencies
3. Build library
4. Run tests
5. Generate type definitions

## 📄 License

ISC License

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📞 Support

For issues and questions, please use the GitHub issue tracker.
