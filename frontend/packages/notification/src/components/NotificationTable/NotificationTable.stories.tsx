/* eslint-disable */
import React from "react";
import { Meta, Story } from "@storybook/react";
import { NotificationTable, NotificationTableProps } from "./NotificationTable";

export default {
  title: "Components/NotificationTable",
  component: NotificationTable,
} as Meta;

const Template: Story<NotificationTableProps> = (args) => (
  <NotificationTable {...args} />
);

export const NotificationTableComponent = Template.bind({});
NotificationTableComponent.args = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#FFFFFF",
    primary: "#3B8396",
  },
  moduleConfigurations: {
    defaultQueryParams: "sort=_id",
    actions: [
      {
        type: "edit",
        label: "EDIT",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
  },
};
