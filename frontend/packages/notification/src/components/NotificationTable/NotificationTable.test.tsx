import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { NotificationTable } from "./NotificationTable";

const data = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#FFFFFF",
    primary: "#3B8396",
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: "sort=_id",
    actions: [
      {
        type: "edit",
        label: "EDIT",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
  },
};

describe("<NotificationTable />", () => {
  test("it should mount", () => {
    render(<NotificationTable {...data} />);

    const notificationTable = screen.getByTestId("NotificationTable");

    expect(notificationTable).toBeInTheDocument();
  });
});
