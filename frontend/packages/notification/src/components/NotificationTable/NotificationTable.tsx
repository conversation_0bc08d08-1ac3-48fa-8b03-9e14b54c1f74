import React from "react";
import { useTable, useFilters, useGlobalFilter, useSortBy } from "react-table";
import { ThemeColors } from "../SharedExports";
import styles from "./NotificationTable.module.css";
export interface NotificationTableProps {
  /**
   * The notifications data
   */
  data: any;
  /**
   * The colors to be utilized for the component
   */
  themeColors?: ThemeColors;
  /**
   * The configurations required for module
   */
  moduleConfigurations?: Record<string, any>;
  /**
   * The columns data
   */
  columns: any;
  /**
   * The edit callback call
   */
  callback?: any;
}

export const NotificationTable: React.FC<NotificationTableProps> = ({
  data,
  columns,
  moduleConfigurations,
  themeColors,
  callback,
  ...props
}) => {
  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } =
    useTable(
      {
        columns,
        data,
      },
      useFilters,
      useGlobalFilter,
      useSortBy
    );
  const actions = moduleConfigurations?.actions || [];

  return (
    <div className={styles.Table} data-testid="Table">
      <table {...getTableProps()}>
        <thead>
          {headerGroups.map((headerGroup: any) => (
            <tr {...headerGroup.getHeaderGroupProps()}>
              {headerGroup.headers.map((column: any) => (
                <th {...column.getHeaderProps(column.getSortByToggleProps())}>
                  {column.render("Header")}
                </th>
              ))}
              {actions?.length > 0 ? <th>Action</th> : ""}
            </tr>
          ))}
        </thead>
        <tbody {...getTableBodyProps()}>
          {rows.map((row: any, i: number) => {
            prepareRow(row);
            return (
              <tr {...row.getRowProps()}>
                {row.cells.map((cell: any) => {
                  return (
                    <td {...cell.getCellProps()}>{cell.render("Cell")}</td>
                  );
                })}

                {actions?.length > 0 ? (
                  <td>
                    {actions.map((action: any, index: number) => {
                      return action.field &&
                        row.original[action.field] === action.defaultValue ? (
                        <span
                          key={`${index}`}
                          className={styles.actionLabel}
                          onClick={() => callback(row, action.antiType)}
                        >
                          {action.antiLabel}
                        </span>
                      ) : (
                        <span
                          key={`${index}`}
                          className={styles.actionLabel}
                          onClick={() => callback(row, action.type)}
                        >
                          {action.label}
                        </span>
                      );
                    })}
                  </td>
                ) : (
                  ""
                )}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};
