import React from "react";
import { <PERSON><PERSON>, <PERSON> } from "@storybook/react";
import { ActionForm, ActionFormProps } from "./ActionForm";

export default {
  title: "Components/ActionForm",
  component: ActionForm,
} as Meta;

const Template: Story<ActionFormProps> = (args) => <ActionForm {...args} />;

export const ActionFormComponent = Template.bind({});
ActionFormComponent.args = {
  setModal: true,
  modalConfig: [],
};
