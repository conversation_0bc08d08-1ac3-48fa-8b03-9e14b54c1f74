/* eslint-disable */
import React from "react";
import { <PERSON>a, <PERSON> } from "@storybook/react";
import {
  NotificationViewTable,
  NotificationViewTableProps,
} from "./NotificationViewTable";

export default {
  title: "Components/NotificationViewTable",
  component: NotificationViewTable,
} as Meta;

const Template: Story<NotificationViewTableProps> = (args) => (
  <NotificationViewTable {...args} />
);

export const NotificationViewTableComponent = Template.bind({});
NotificationViewTableComponent.args = {
  notifications: [],
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#FFFFFF",
    primary: "#3B8396",
  },
  moduleConfigurations: {
    defaultQueryParams: "type=PRODUCT",
    actions: [
      {
        type: "edit",
        label: "Edit",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
  },
};
