import React from "react";
import styled from "styled-components";
import { NotificationTable } from "../NotificationTable/NotificationTable.lazy";
import { ThemeColors } from "../SharedExports";
const Styles = styled.div`
  // padding: 1rem;
  padding-top: 0rem;

  //Most important below 4 lines to avoid full screen scrolling
  ${
    "" /* These styles are suggested for the table fill all available space in its containing element */
  }
  display: block;
  ${
    "" /* These styles are required for a horizontaly scrollable table overflow */
  }
  overflow: auto;

  table {
    border-spacing: 0;
    border: 1px solid #f2f2f2;
    font-size: 0.875rem;
    tr {
      border-bottom: 1px solid #f2f2f2;
      background-color: var(--primary, #ffffff);
      :last-child {
        td {
          border-bottom: 0;
        }
      }
    }
    th {
      padding: 0.75rem;
      border-bottom: 1px solid #f2f2f2;
      text-transform: uppercase;
      background-color: #fafafa;
    }
    ,
    td {
      margin: 0rem;
      padding: 0.5rem;
      min-width: 11.8rem;
      text-transform: none;
      text-align: left;
      background-color: var(--primary, #ffffff);
      :last-child {
        border-right: 0;
      }
    }
  }
`;

export interface NotificationViewTableProps {
  /**
   * The notifications data
   */
  notifications: any;
  /**
   * The data mapping columns
   */
  dataColumns: { [key: string]: any }[];
  /**
   * The colors to be utilized for the component
   */
  themeColors?: ThemeColors;
  /**
   * The configurations required for module
   */
  moduleConfigurations?: Record<string, any>;
  /**
   * The edit api callback call
   */
  callback?: any;
}

export const NotificationViewTable: React.FC<NotificationViewTableProps> = ({
  notifications,
  dataColumns,
  themeColors,
  moduleConfigurations,
  callback,
  ...props
}) => {
  const data = React.useMemo(() => notifications, [notifications]);
  const columns = React.useMemo(() => dataColumns, []);

  return (
    <Styles>
      <NotificationTable
        callback={callback}
        themeColors={themeColors}
        moduleConfigurations={moduleConfigurations}
        columns={columns}
        data={data}
      />
    </Styles>
  );
};
