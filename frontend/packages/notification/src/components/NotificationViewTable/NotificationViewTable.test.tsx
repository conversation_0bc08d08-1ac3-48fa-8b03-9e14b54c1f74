import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { NotificationViewTable } from "./NotificationViewTable";

const data = {
  notifications: [],
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#FFFFFF",
    primary: "#3B8396",
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: "type=PRODUCT",
    actions: [
      {
        type: "edit",
        label: "Edit",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
  },
};

describe("<NotificationViewTable />", () => {
  test("it should mount", () => {
    render(<NotificationViewTable {...data} />);

    const notificationViewTable = screen.getByTestId("NotificationViewTable");

    expect(notificationViewTable).toBeInTheDocument();
  });
});
