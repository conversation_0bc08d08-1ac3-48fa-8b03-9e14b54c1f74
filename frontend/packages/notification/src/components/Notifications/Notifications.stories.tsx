/* eslint-disable */
import React from "react";
import { <PERSON><PERSON>, <PERSON> } from "@storybook/react";
import { Notifications, NotificationsProps } from "./Notifications";

export default {
  title: "Components/Notifications",
  component: Notifications,
} as Meta;

const Template: Story<NotificationsProps> = (args) => (
  <Notifications {...args} />
);

export const NotificationComponent = Template.bind({});
NotificationComponent.args = {
  title: "News & Offers",
  helpText: "This is where you can set promotions and send to your users.",
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#3B8396",
    primary: "#FFFFFF",
  },
  moduleConfigurations: {
    defaultQueryParams: "isActive=true&sort=-_id",
    isFilterEnable: false,
    isImportEnable: false,
    isAddEnable: true,
    isSearchEnable: true,
    actions: [
      {
        type: "edit",
        label: "EDIT",
      },
      {
        type: "soft-delete",
        label: "DEACTIVATE",
        antiLabel: "ACTIVATE",
        antiType: "reactivate",
        field: "isActive",
        defaultValue: false,
      },
    ],
    dataColumns: [
      {
        id: "type",
        Header: "Type",
        filter: "fuzzyText",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.type ? d.type.join().replace('_', ' ') : '-';",
          },
        },
      },
      {
        id: "title",
        Header: "Title",
        filter: "fuzzyText",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.title ? d.title : '-';",
          },
        },
      },
      {
        id: "message",
        Header: "Message",
        filter: "fuzzyText",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.message ? d.message : '-';",
          },
        },
      },
      {
        id: "completedOnTime",
        Header: "Completed At",
      },
    ],
    formConfig: {
      editNotification: {
        title: "Edit News & Offers",
        submitLabel: "UPDATE",
        config: [
          {
            id: "title",
            label: "Title",
            placeholder: "Enter Title",
            type: "text",
            subType: "text",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Title is required"],
              },
              {
                type: "max",
                params: [20, "Title cannot be more than 20 characters"],
              },
            ],
          },
          {
            id: "message",
            label: "Message",
            placeholder: "Enter Message",
            type: "textArea",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Message is required"],
              },
              {
                type: "max",
                params: [100, "Message cannot be more than 100 characters"],
              },
            ],
          },
        ],
      },
      createNotification: {
        title: "Add News & Offers",
        submitLabel: "SUBMIT",
        staticData: {
          "variables.isBackgroundSupported": true,
          "variables.displayScreen": "NOTIFICATION",
          "variables.criteria.type": "user",
        },
        config: [
          {
            id: "title",
            label: "Title",
            placeholder: "Enter Title",
            type: "text",
            subType: "text",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Title is required"],
              },
              {
                type: "max",
                params: [20, "Title cannot be more than 20 characters"],
              },
            ],
          },
          {
            id: "message",
            label: "Message",
            placeholder: "Enter Message",
            type: "textArea",
            validationType: "string",
            validations: [
              {
                type: "required",
                params: ["Message is required"],
              },
              {
                type: "max",
                params: [100, "Message cannot be more than 100 characters"],
              },
            ],
          },
          {
            id: "type",
            label: "Type",
            placeholder: "Select Type",
            type: "select",
            validationType: "string",
            value: "",
            options: [
              { id: "IN_APP", name: "IN APP" },
              { id: "PUSH", name: "PUSH" },
            ],
            validations: [
              {
                type: "required",
                params: ["Type is required"],
              },
            ],
          },
          {
            id: "variables-criteria",
            label: "Segment",
            placeholder: "Select Segment",
            type: "select",
            validationType: "string",
            value: "",
            options: [
              {
                id: '{ "query": "type=rider", "segment": "Riders" }',
                name: "Riders",
              },
              {
                id: '{ "query": "type=driver", "segment": "Drivers" }',
                name: "Drivers",
              },
            ],
            validations: [
              {
                type: "required",
                params: ["Segment is required"],
              },
            ],
          },
        ],
      },
    },
  },
  apiConfigurations: {
    baseUrl: "http://localhost:3102",
    userId: "607e6bd098b6013bdf13d854",
    token: "",
  },
};
