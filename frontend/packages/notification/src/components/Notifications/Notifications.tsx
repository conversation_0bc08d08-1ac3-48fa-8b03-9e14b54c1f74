import React, { useEffect, useState } from "react";
import {
  Icon,
  InfoBox,
  PageHeader,
  ProgressBar,
  Search,
  ErrorHandler,
} from "@frontend/core";
import "@frontend/core/lib/index.css";
import { <PERSON><PERSON>, Col, Row } from "react-bootstrap";
import axios, { AxiosRequestConfig } from "axios";
import { size } from "lodash";
import Pagination from "react-js-pagination";
import Dot from "dot-object";
import Swal from "sweetalert2";
import "bootstrap/dist/css/bootstrap.min.css";
import "react-datepicker/dist/react-datepicker.css";
import { NotificationViewTable } from "../NotificationViewTable/NotificationViewTable.lazy";
import { ActionForm } from "../ActionForm/ActionForm.lazy";
import { ApiConfigurations, ThemeColors } from "../SharedExports";
import styles from "./Notifications.module.css";

export interface NotificationsProps {
  /**
   * The colors to be utilized for the component
   */
  themeColors: ThemeColors;
  /**
   * The help text of the component
   */
  helpText: string;
  /**
   * The title of the header
   */
  title: string;
  /**
   * The type of loader to be used
   */
  loaderType?: any;
  /**
   * The configurations required for API call
   */
  apiConfigurations: ApiConfigurations;
  /**
   * The configurations required for module
   */
  moduleConfigurations: Record<string, any>;
}

const staticColumns = [
  {
    id: "createdAt",
    filter: "date",
    accessor: (d: any) => {
      return new Date(d.createdAt).toLocaleString();
    },
  },
  {
    id: "scheduledTime",
    filter: "date",
    accessor: (d: any) => {
      return new Date(d.scheduledTime).toLocaleString() || "-";
    },
  },
  {
    id: "completedOnTime",
    filter: "date",
    accessor: (d: any) => {
      return new Date(d.completedOnTime).toLocaleString() !== "Invalid Date"
        ? new Date(d.completedOnTime).toLocaleString()
        : "-";
    },
  },
];
const dotHyphen = new Dot("-");
const dot = new Dot(".");

export const Notifications: React.FC<NotificationsProps> = ({
  themeColors,
  helpText,
  title,
  loaderType,
  moduleConfigurations,
  apiConfigurations,
  ...props
}) => {
  let skip = 0;
  const limit = 10;
  const isSearchEnable = moduleConfigurations.isSearchEnable || false;
  const isFilterEnable = moduleConfigurations.isFilterEnable || false;
  const isImportEnable = moduleConfigurations.isImportEnable || false;
  const isAddEnable = moduleConfigurations.isAddEnable || false;
  loaderType = loaderType || "Audio";

  const [notifications, setNotifications] = useState();
  const [notificationFetched, setNotificationFetched] = useState(false);
  const [apiResponse, setApiResponse] = useState<any>({});
  const [activePage, setActivePage] = useState(1);
  const [dataColumns, setDataColumns] = useState([]);
  const [isColumnsSet, setIsColumnsSet] = useState(false);
  const [queryParams, setQueryParams] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState();
  const [formType, setFormType] = useState("");
  const [editId, setEditId] = useState("");

  useEffect(() => {
    combinedDataColumns(moduleConfigurations.dataColumns, staticColumns);
    callGetNotificationsService(skip, "");
  }, []);

  function getTimezone() {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return timezone;
  }

  function callGetNotificationsService(skipValue: number, queryString: string) {
    setNotificationFetched(false);
    let defaultQuery = `?offset=${skipValue}&limit=${limit}`;
    if (size(moduleConfigurations?.defaultQueryParams) > 0) {
      defaultQuery += `&${moduleConfigurations.defaultQueryParams}`;
    }
    if (size(queryString) > 0) {
      defaultQuery += `&${queryString}`;
    }
    const url = `${apiConfigurations.baseUrl}/notifications/schedule${defaultQuery}`;
    let requestConfig: AxiosRequestConfig = {
      method: "GET",
      url,
      headers: {
        "content-type": "application/json",
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        "x-sworks-timezone": getTimezone(),
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        processDataOnGet(res);
      })
      .catch((err: any) => {
        processErrorDataOnGet(err);
      });
  }

  function callDeleteNotificationService(id: string, type: string) {
    setNotificationFetched(false);
    const url = `${apiConfigurations.baseUrl}/notifications/schedule/${id}`;
    let requestConfig: AxiosRequestConfig = {
      method: "DELETE",
      url,
      headers: {
        "content-type": "application/json",
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        "x-sworks-timezone": getTimezone(),
      },
      params: {
        type,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .delete(url, requestConfig)
      .then((res: any) => {
        processDataOnDelete(res);
      })
      .catch((err: any) => {
        processErrorDataOnDelete(err);
      });
  }

  function callEditNotificationService(data: any, staticData: any) {
    setNotificationFetched(false);
    staticData = staticData || {};
    const url = `${apiConfigurations.baseUrl}/notifications/schedule/${editId}`;
    let requestConfig: AxiosRequestConfig = {
      method: "PATCH",
      url,
      headers: {
        "content-type": "application/json",
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        "x-sworks-timezone": getTimezone(),
      },
      data: { ...data, ...staticData },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .patch(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnEdit(res);
      })
      .catch((err: any) => {
        processErrorDataOnEdit(err);
      });
  }

  function callCreateNotificationService(data: any, staticData: any) {
    staticData = staticData || {};
    setNotificationFetched(false);
    const url = `${apiConfigurations.baseUrl}/notifications/schedule`;
    let requestConfig: AxiosRequestConfig = {
      method: "POST",
      url,
      headers: {
        "content-type": "application/json",
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        "x-sworks-timezone": getTimezone(),
      },
      data: dot.object({
        ...data,
        ...staticData,
      }),
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .post(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnCreate(res);
      })
      .catch((err: any) => {
        processErrorDataOnCreate(err);
      });
  }

  function processDataOnGet(res: any) {
    if (res.data?.result) {
      setApiResponse(res.data.result);
      const notifications = res.data.result?.notifications || [];
      setNotifications(notifications);
      setNotificationFetched(true);
    }
  }

  function processErrorDataOnGet(err: any) {}

  function processDataOnDelete(res: any) {
    if (res.data?.result) {
      callGetNotificationsService(skip, queryParams);
    }
  }

  function processErrorDataOnDelete(err: any) {}

  function processDataOnEdit(res: any) {
    if (res.data?.result) {
      dismissActionForm();
      callGetNotificationsService(skip, queryParams);
    }
  }

  function processErrorDataOnEdit(err: any) {}

  function processDataOnCreate(res: any) {
    if (res.data?.result) {
      dismissActionForm();
      callGetNotificationsService(skip, queryParams);
    } else {
      processErrorDataOnCreate(res);
    }
  }

  function processErrorDataOnCreate(err: any) {
    setNotificationFetched(true);
    const errorMessage = `Please sync with the system admin if you feel something is not right`;
    Swal.fire({
      icon: "error",
      title: "Oops...",
      text:
        err.data?.error && err.data?.error.errorCode !== 6000
          ? err.data.error?.errorMessage
          : errorMessage,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function handlePageChange(pageNumber: number) {
    if (pageNumber > 1) {
      skip = (pageNumber - 1) * limit;
    } else {
      skip = 0;
    }
    setActivePage(pageNumber);
    callGetNotificationsService(skip, queryParams);
  }

  function callback(data: any, action: string) {
    switch (action) {
      case "edit":
        showEditView(data.original);
        break;
      case "delete":
        break;
      case "soft-delete":
        softDeleteRow(data.original, "remove");
        break;
      case "reactivate":
        softDeleteRow(data.original, "reactivate");
        break;
      default:
        break;
    }
  }

  function softDeleteRow(data: any, type: string) {
    if (type === "remove") {
      Swal.fire({
        title: "Are you sure?",
        text: `User won't be able to see the ${title.toLowerCase()} and it's relevant data!`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: themeColors.backgroundPrimary,
        cancelButtonColor: themeColors.backgroundSecondary,
        confirmButtonText: "Yes, delete it!",
      }).then((result) => {
        if (result.isConfirmed) {
          callDeleteNotificationService(data.id, type);
        }
      });
    } else {
      Swal.fire({
        title: "Are you sure?",
        text: `User will be able to see the ${title.toLowerCase()} and it's relevant data!`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: themeColors.backgroundPrimary,
        cancelButtonColor: themeColors.backgroundSecondary,
        confirmButtonText: "Yes, enable it!",
      }).then((result) => {
        if (result.isConfirmed) {
          callDeleteNotificationService(data.id, type);
        }
      });
    }
  }

  function combinedDataColumns(dataColumns: any, staticColumns: any) {
    if (!isColumnsSet) {
      for (let item of dataColumns) {
        Object.assign(
          item,
          staticColumns.find((y: any) => y?.id === item?.id)
        );
        if (typeof item.accessor === "object" && item.accessor.function) {
          item.accessor = new Function(
            item.accessor.function.arguments,
            item.accessor.function.body
          );
        }
      }
      setIsColumnsSet(true);
      setDataColumns(dataColumns);
    }
  }

  function filterNotifications(queryString: string) {
    skip = 0;
    setActivePage(1);
    setQueryParams(queryString);
    callGetNotificationsService(skip, queryString);
  }

  function onCancelClick() {
    dismissActionForm();
  }

  function dismissActionForm() {
    setShowModal(false);
    setModalConfig(undefined);
    setFormType("");
    setEditId("");
  }

  function showCreateView() {
    setFormType("create");
    const createConfig = moduleConfigurations.formConfig?.createNotification;
    setModalConfig(createConfig);
    setShowModal(true);
  }

  function showEditView(data: any) {
    setEditId(data.id);
    setFormType("edit");
    const editConfig = JSON.parse(
      JSON.stringify(moduleConfigurations.formConfig?.editNotification)
    );
    data = dot.dot(data);
    for (let item of editConfig.config) {
      let id = item.id;
      id = id.replace(/-/g, ".");
      let value = data[id] || item.value;
      item.value = value;
    }
    setModalConfig(editConfig);
    setShowModal(true);
  }

  function IsJsonString(input: any) {
    if (typeof input !== "string") return false;
    try {
      const result = JSON.parse(input);
      const type = Object.prototype.toString.call(result);
      return type === "[object Object]" || type === "[object Array]";
    } catch (err) {
      return false;
    }
  }

  function onSubmitClick(data: any) {
    dotHyphen.object(data);
    if (data.variables?.criteria) {
      const isJson = IsJsonString(data.variables.criteria);
      if (isJson) {
        data.variables.criteria = JSON.parse(data.variables.criteria);
      }
    }
    data = dot.dot(data);
    const formConfig = moduleConfigurations.formConfig;
    if (formType === "create") {
      callCreateNotificationService(
        data,
        formConfig.createNotification?.staticData
      );
    } else if (formType === "edit") {
      callEditNotificationService(
        data,
        formConfig.editNotification?.staticData
      );
    }
  }

  return (
    <div data-testid="Notifications">
      <PageHeader title={title} fontColor={themeColors?.textSecondary} />
      <InfoBox
        text={helpText}
        noGutter={false}
        iconName={"FaLightbulb"}
        background={themeColors?.backgroundSecondary}
        iconColor={themeColors?.textSecondary}
        fontColor={themeColors?.textAlternate}
      />
      <Row>
        {isSearchEnable && dataColumns?.length > 0 ? (
          <Col>
            <Search
              dataColumns={dataColumns}
              key={"search-component"}
              handleSearchChangeFor={(queryParams: string) =>
                filterNotifications(queryParams)
              }
            />
          </Col>
        ) : (
          ""
        )}
        {isFilterEnable ? (
          <Col
            lg={2}
            xl={2}
            className={`d-flex justify-content-end mb-auto mx-auto`}
          >
            <Icon
              iconName={"FaFilter"}
              iconColor={themeColors.textSecondary}
              label={"Filters"}
              fontColor={themeColors.textSecondary}
            />
          </Col>
        ) : (
          ""
        )}
        {isImportEnable ? (
          <Col
            lg={2}
            xl={2}
            className={`d-flex justify-content-end mb-auto mx-auto`}
          >
            <Button
              size="sm"
              block
              id="addNewClick"
              type="submit"
              className={styles.addNewButton}
            >
              {`Upload ${title}`}
            </Button>
          </Col>
        ) : (
          ""
        )}

        {isAddEnable ? (
          <Col
            lg={2}
            xl={2}
            className={`d-flex justify-content-end mb-auto mx-auto`}
          >
            <Button
              size="sm"
              block
              id="addNewClick"
              type="button"
              onClick={() => showCreateView()}
              className={styles.addNewButton}
            >
              {`Add ${title}`}
            </Button>
          </Col>
        ) : (
          ""
        )}
      </Row>
      <Row className={`d-block`}>
        {notificationFetched ? (
          <div>
            {" "}
            {size(notifications) > 0 ? (
              <>
                <Col className={`d-flex justify-content-center`} xs={12}>
                  <NotificationViewTable
                    notifications={notifications}
                    dataColumns={dataColumns}
                    themeColors={themeColors}
                    moduleConfigurations={moduleConfigurations}
                    callback={callback}
                  />
                </Col>
                <Col className={`d-flex justify-content-center`}>
                  {/* @ts-ignore */}
                  <Pagination
                    activePage={activePage}
                    itemsCountPerPage={limit}
                    totalItemsCount={apiResponse?.total || 1}
                    pageRangeDisplayed={5}
                    prevPageText={"Prev"}
                    nextPageText={"Next"}
                    onChange={handlePageChange}
                    activeLinkClass={styles.paginationLinkActive}
                    itemClass={`page-item`}
                    linkClass={`page-link ${styles.paginationLink}`}
                  />
                </Col>
              </>
            ) : (
              <ErrorHandler
                errorCode={"404"}
                showTryAgainButton={false}
                errorMsg={`We are unable to find any matching ${title.toLowerCase()} for your search`}
              />
            )}
          </div>
        ) : (
          <ProgressBar
            type={loaderType}
            background={`#0000002F`}
            color={themeColors.backgroundSecondary}
          />
        )}
      </Row>
      {showModal ? (
        <ActionForm
          setModal={showModal}
          modalConfig={modalConfig}
          themeColors={themeColors}
          apiConfigurations={apiConfigurations}
          onCancelClick={() => onCancelClick()}
          onSubmitClick={(data: any) => onSubmitClick(data)}
        />
      ) : (
        ""
      )}
    </div>
  );
};
