import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { Notifications } from "./Notifications";

const data = {
  title: "Loyalty Levels",
  helpText: "This is where you can manage your loyalty levels",
  themeColors: {
    textAlternate: "#000000",
    textSecondary: "#3B8396",
    textPrimary: "#FFFFFF",
    backgroundSecondary: "#2A5E6C",
    backgroundPrimary: "#3B8396",
    primary: "#FFFFFF",
  },
  moduleConfigurations: {
    defaultQueryParams: "sort=_id",
    isFilterEnable: true,
    isImportEnable: true,
    isAddEnable: true,
    actions: [
      {
        type: "edit",
        label: "EDIT",
      },
      {
        type: "delete",
        label: "DELETE",
      },
    ],
    dataColumns: [
      {
        id: "title",
        Header: "Title",
        filter: "fuzzyText",
        accessor: {
          function: { arguments: "d", body: "return d.title ? d.title : '-';" },
        },
      },
      {
        id: "description",
        Header: "Description",
        filter: "fuzzyText",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.description ? d.description : '-';",
          },
        },
      },
      {
        id: "range.minimum",
        Header: "Notifications from",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.range?.minimum ? d.range?.minimum : '0';",
          },
        },
      },
      {
        id: "range.maximum",
        Header: "Notifications To",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.range?.maximum ? d.range?.maximum : '-';",
          },
        },
      },
      {
        id: "spendingMultiplier",
        Header: "Spending logic ($1 = X Notifications)",
        accessor: {
          function: {
            arguments: "d",
            body: "return d.spendingMultiplier ? d.spendingMultiplier : '-';",
          },
        },
      },
    ],
  },
  apiConfigurations: {
    baseUrl: "http://localhost:3102",
    userId: "607e6bd098b6013bdf13d854",
    token: "",
  },
};

describe("<Notifications />", () => {
  test("it should mount", () => {
    render(<Notifications {...data} />);

    const notifications = screen.getByTestId("Notifications");

    expect(notifications).toBeInTheDocument();
  });
});
