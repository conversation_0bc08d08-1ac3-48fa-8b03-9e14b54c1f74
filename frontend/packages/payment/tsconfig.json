{"compilerOptions": {"target": "es6", "outDir": "lib", "lib": ["dom", "dom.iterable", "esnext"], "declaration": true, "declarationDir": "lib", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react"}, "include": ["src"], "exclude": ["node_modules", "lib"]}