import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';
import React from 'react';
// import { StripePaymentElementOptions } from '@stripe/stripe-js';
import { useEffect, useState } from 'react';
import styles from './Checkout.module.css';
interface CardCheckout {
  stripeDetails: StripeDetails;
  txnId: string;
  amount: number;
  currency?: string;
  onSucces: (object: any) => void;
  redirectionUrl?: string;
}

interface StripeDetails {
  hostUrl: string;
  userId: string;
  userToken: string;
  stripeKey: string;
  amount?: number;
  refundable?: boolean;
}
export const CheckoutForm = (props: CardCheckout) => {
  const stripe = useStripe();
  const elements = useElements();
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(false);
  useEffect(() => {
    if (!stripe) {
      return;
    }
    const clientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');
    const tx_id = new URLSearchParams(window.location.search).get('tx_id');
    if (!clientSecret) {
      return;
    }

    stripe.retrievePaymentIntent(clientSecret).then(async ({ paymentIntent }) => {
      switch (paymentIntent?.status) {
        case 'succeeded':
          if (paymentIntent?.payment_method) {
            const paymentDetails = { txnId: paymentIntent?.id, paymentMethod: paymentIntent?.payment_method };
            props.onSucces(paymentDetails);
            setErrorMessage(true);
            setMessage('Thank you! Your transaction was successful!');
          }
          break;
        case 'processing':
          setMessage('Your transaction is being processed.');
          break;
        case 'requires_payment_method':
          setErrorMessage(false);
          setMessage('We were not able to complete your transaction. Please try again.');
          break;
        default:
          setErrorMessage(false);
          setMessage(
            'Oops! Something went wrong! Don`t worry no charges would be deducted from your account. Please try again later.',
          );
          break;
      }
    });
  }, [stripe]);

  const retrievePaymentIntentForIframe = (client_secret: string) => {
    stripe &&
      stripe.retrievePaymentIntent(client_secret).then(async ({ paymentIntent }) => {
        switch (paymentIntent?.status) {
          case 'succeeded':
            if (paymentIntent?.payment_method) {
              const paymentDetails = { txnId: paymentIntent?.id, paymentMethod: paymentIntent?.payment_method };
              props.onSucces(paymentDetails);
              setErrorMessage(true);
              setMessage('Thank you! Your transaction was successful!');
            }
            break;
          case 'processing':
            setMessage('Your transaction is being processed.');
            break;
          case 'requires_payment_method':
            setErrorMessage(false);
            setMessage('We were not able to complete your transaction. Please try again.');
            break;
          default:
            setErrorMessage(false);
            setMessage(
              'Oops! Something went wrong! Don`t worry no charges would be deducted from your account. Please try again later.',
            );
            break;
        }
      });
  };
  const handleSubmit = async (event: React.MouseEvent) => {
    event.preventDefault();
    if (!stripe || !elements) {
      return;
    }
    const redirectionUrl = props?.redirectionUrl ? props.redirectionUrl : window.location;

    setIsLoading(true);

    if (window !== window.parent) {
      stripe
        .confirmPayment({
          elements,
          confirmParams: {
            return_url: 'https://example.com',
          },
          redirect: 'if_required', //
        })
        .then(function (result) {
          if (result.paymentIntent) {
            retrievePaymentIntentForIframe(result.paymentIntent?.client_secret!);
          }
          if (result.error) {
            if (
              (result.error && result.error.type === 'card_error') ||
              (result.error && result.error.type === 'validation_error')
            ) {
              setErrorMessage(false);
              setMessage(result.error.message!);
            } else {
              setErrorMessage(false);
              setMessage('An unexpected error occured.');
            }
            setIsLoading(false);
          }
        });
    } else {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${redirectionUrl}?tx_id=${props.txnId}`,
        },
      });
      if ((error && error.type === 'card_error') || (error && error.type === 'validation_error')) {
        setErrorMessage(false);
        setMessage(error.message!);
      } else {
        setErrorMessage(false);
        setMessage('An unexpected error occured.');
      }
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.paymentDiv}>
      <form id="payment-form">
        <div className={styles.paymentForm}>
          <PaymentElement id="payment-element" />
        </div>
        <button disabled={isLoading || !stripe || !elements} id="submit" onClick={handleSubmit}>
          <span id="button-text">{isLoading ? `Please wait...` : `$${props.amount / 100}.00 Pay`}</span>
          {isLoading ? '' : <i className="fa fa-lock"></i>}
        </button>
        {props.stripeDetails.refundable === false ? (
          ''
        ) : (
          <div className={styles.infoText}>
            <b>Note: </b>
            {`Refundable amount of $${
              props.amount / 100
            }.00 will be charged to verify the credit card. Your ride payment will be automatically deducted after ride completion.`}
          </div>
        )}
        {message && (
          <div id="payment-message" style={{ marginTop: '16px', color: errorMessage ? 'green' : 'red' }}>
            {message}
          </div>
        )}
      </form>
    </div>
  );
};
