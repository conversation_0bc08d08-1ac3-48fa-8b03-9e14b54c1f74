/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { Payment, PaymentDetailsProps } from './Payment';

export default {
  title: 'Components/Payment',
  component: Payment,
} as Meta;

const Template: Story<PaymentDetailsProps> = (args) => <Payment {...args} />;
const data = {
  hostUrl: 'http://localhost',
  userId: '6257f678698aed34566cc483',
  userToken: '',
  stripeKey: '',
  amount: 100,
  refundable: true,
};
export const PaymentsBox = Template.bind({});
PaymentsBox.args = {
  stripeDetails: data,
};
