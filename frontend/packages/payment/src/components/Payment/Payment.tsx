import { Elements } from '@stripe/react-stripe-js';
import { Appearance, loadStripe } from '@stripe/stripe-js';
import React from 'react';
import { useEffect, useState } from 'react';
import { CheckoutForm } from '../Checkout/Checkout';
import styles from './Payment.module.css';
import { PaymentServices } from './Services';

export interface PaymentDetailsProps {
  cardTheme?: CardTheme;
  stripeDetails: StripeDetails;
  onSucess: (response: any) => void;
  onFail: (error: any) => void;
}

export interface CardTheme {
  theme?: string;
  backgroundColor?: string;
}

export interface StripeDetails {
  hostUrl: string;
  userId: string;
  userToken: string;
  stripeKey: string;
  amount?: number;
  refundable?: boolean;
  redirectionUrl?: string;
}
export const Payment: React.FC<PaymentDetailsProps> = ({ ...props }) => {
  const stripePromise = loadStripe(props.stripeDetails.stripeKey);
  const [clientSecret, setClientSecret] = useState('');
  const [txnId, setTxnId] = useState('');
  const [amount, setAmount] = useState(0);
  const service = new PaymentServices(
    props.stripeDetails.hostUrl,
    props.stripeDetails.userId,
    props.stripeDetails.userToken,
  );
  const redirectionUrl = props.stripeDetails.redirectionUrl;

  useEffect(() => {
    getStripeId();
  }, []);
  const getStripeId = async () => {
    const stripeData = await service.cardInitialize(props.stripeDetails.amount, props.stripeDetails.refundable);
    if (stripeData.status == 1) {
      const clientSecret = stripeData.result?.stripe_data?.client_secret;
      setClientSecret(clientSecret);
      setTxnId(stripeData.result?.txnId);
      setAmount(stripeData.result?.amount);
    } else {
      props.onFail(stripeData.error.errorMessage);
    }
  };
  const appearance: Appearance = {
    theme: 'flat',
    variables: {
      colorBackground: props.cardTheme?.backgroundColor || '#CCC',
    },
  };

  const confirmPayment = (object: any) => {
    if (object.txnId) {
      getStatus(object);
    }
  };

  const getStatus = async (object: any) => {
    const card = await service.cardComplete(object.txnId);
    if (card.status === 1 && card.result.customer) {
      props.onSucess({ ...object, customerId: card.result.customer });
    } else if (card.error) {
      props.onFail(card);
    }
  };

  return (
    <div className={styles.cardDetails}>
      <div>
        {clientSecret && (
          <Elements options={{ clientSecret, appearance }} stripe={stripePromise}>
            <CheckoutForm
              txnId={txnId}
              amount={amount}
              redirectionUrl={redirectionUrl}
              onSucces={(data) => confirmPayment(data)}
              stripeDetails={props.stripeDetails}
            />
          </Elements>
        )}
      </div>
    </div>
  );
};
