import axios, { AxiosRequestConfig } from 'axios';

export class PaymentServices {
  hostUrl: string;
  userId: string;
  userToken: string;
  constructor(hostUrl: string, userId: string, userToken: string) {
    this.hostUrl = hostUrl;
    this.userId = userId;
    this.userToken = userToken;
  }

  public async cardInitialize(amount: number = 100, refundable: boolean = true): Promise<any> {
    try {
      const url = this.hostUrl + '/user/' + this.userId + '/add-card/initialize';
      const data = {
        amount,
        refundable,
      };
      const requestConfig: AxiosRequestConfig = {
        method: 'POST',
        url,
        headers: {
          authorization: this.userToken,
          userId: this.userId,
          'response-version': `v2`,
        },
      };
      const result = await axios.post(url, data, requestConfig);
      return result.data;
    } catch (error) {
      throw error;
    }
  }

  public async cardComplete(txnId: string): Promise<any> {
    try {
      const url = this.hostUrl + '/user/' + this.userId + '/add-card/complete';
      const requestConfig: AxiosRequestConfig = {
        method: 'POST',
        url,
        headers: {
          authorization: this.userToken,
          userId: this.userId,
          'response-version': `v2`,
        },
        data: {
          txnId: txnId,
        },
      };
      const result = await axios.post(url, requestConfig.data, requestConfig);
      return result.data;
    } catch (error) {
      throw error;
    }
  }
}
