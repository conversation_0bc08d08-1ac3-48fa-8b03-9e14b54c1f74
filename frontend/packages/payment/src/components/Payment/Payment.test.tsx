import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { Payment } from './Payment';

const stripeDetails = {
  hostUrl: 'http://localhost',
  userId: '61d51a7d9bf746d92153564c',
  userToken: '',
  stripeKey: '',
};

describe('<Payment />', () => {
  test('it should mount', () => {
    render(
      <Payment
        stripeDetails={stripeDetails}
        onSucess={function (response: any): void {
          throw new Error('Function not implemented.');
        }}
        onFail={function (error: any): void {
          throw new Error('Function not implemented.');
        }}
      />,
    );

    const PaymentScreen = screen.getByTestId('Payment');

    expect(PaymentScreen).toBeInTheDocument();
  });
});
