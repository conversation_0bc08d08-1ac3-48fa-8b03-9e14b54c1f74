{"name": "@frontend/payment", "version": "1.0.0", "description": "This is stripe payment library project", "main": "lib/index.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "scripts": {"analyze": "source-map-explorer 'lib/*'", "bootstrap": "yarn install", "clean": "<PERSON><PERSON><PERSON> dist", "start": "start-storybook -p 6006", "build": "rollup -c", "format": "prettier --write \"src/**/*\"", "test": "yarn test", "verify": "npm-run-all clean bootstrap build", "preversion": "yarn verify", "build-storybook": "build-storybook"}, "keywords": ["web", "payment", "react"], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.17.9", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.2.1", "@storybook/addon-actions": "^6.4.22", "@storybook/addon-essentials": "^6.4.22", "@storybook/addon-links": "^6.4.22", "@storybook/addon-postcss": "^2.0.0", "@storybook/react": "^6.4.3", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.1.1", "@testing-library/user-event": "^14.1.1", "@types/react": "^17.0.2", "@types/testing-library__jest-dom": "^5.14.3", "babel-loader": "^8.2.5", "bootstrap": "^5.1.3", "generate-react-cli": "^7.1.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.12", "postcss-cli": "^9.1.0", "postcss-import": "^14.1.0", "postcss-nested": "^5.0.5", "postcss-preset-env": "^7.4.4", "prettier": "^2.6.2", "react": "^18.3.1", "react-dom": "^18.1.0", "rimraf": "^3.0.2", "rollup": "^2.70.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "source-map-explorer": "^2.5.2", "storybook-css-modules-preset": "^1.0.8", "typescript": "^4.6.3"}, "dependencies": {"@stripe/react-stripe-js": "^1.7.1", "@stripe/stripe-js": "^1.27.0", "axios": "^0.27.2", "react-router-dom": "5.3.4"}, "files": ["/lib"], "browser": {"fs": false, "path": false, "os": false}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}