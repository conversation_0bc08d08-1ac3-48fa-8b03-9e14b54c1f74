# Cleveland Web Payment

A React component library for Stripe payment integration, providing secure and customizable payment processing components.

## Overview

This library provides React components for handling Stripe payments with a backend service integration. It includes components for payment initialization, card input, and transaction completion with support for both regular and refundable payments.

## Features

- 🔒 Secure Stripe payment processing
- 💳 Customizable payment forms
- 🎨 Themeable card input components
- 🔄 Refundable payment support
- 📱 Responsive design
- 🖼️ iframe support for embedded payments
- ✅ TypeScript support

## Installation

```bash
npm install @sworks-platform/cleveland-web-payment
```

or

```bash
yarn add @sworks-platform/cleveland-web-payment
```

## Usage

### Basic Payment Component

```tsx
import { Payment } from '@sworks-platform/cleveland-web-payment';

const MyPaymentPage = () => {
  const stripeDetails = {
    hostUrl: 'https://your-backend-api.com',
    userId: 'user123',
    userToken: 'your-auth-token',
    stripeKey: 'pk_test_your_stripe_publishable_key',
    amount: 2000, // Amount in cents ($20.00)
    refundable: true,
    redirectionUrl: 'https://your-app.com/payment-success'
  };

  const handleSuccess = (response) => {
    console.log('Payment successful:', response);
    // Handle successful payment
  };

  const handleFailure = (error) => {
    console.error('Payment failed:', error);
    // Handle payment failure
  };

  return (
    <Payment
      stripeDetails={stripeDetails}
      onSucess={handleSuccess}
      onFail={handleFailure}
      cardTheme={{
        backgroundColor: '#f8f9fa'
      }}
    />
  );
};
```

## API Reference

### Payment Component

The main component that handles the complete payment flow.

#### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `stripeDetails` | `StripeDetails` | Yes | Configuration for Stripe and backend integration |
| `onSucess` | `(response: any) => void` | Yes | Callback for successful payments |
| `onFail` | `(error: any) => void` | Yes | Callback for failed payments |
| `cardTheme` | `CardTheme` | No | Styling options for the payment form |

#### StripeDetails Interface

```tsx
interface StripeDetails {
  hostUrl: string;           // Backend API URL
  userId: string;            // User identifier
  userToken: string;         // Authentication token
  stripeKey: string;         // Stripe publishable key
  amount?: number;           // Payment amount in cents
  refundable?: boolean;      // Whether payment is refundable
  redirectionUrl?: string;   // Success redirect URL
}
```

#### CardTheme Interface

```tsx
interface CardTheme {
  theme?: string;            // Stripe theme ('flat', 'stripe', etc.)
  backgroundColor?: string;  // Background color for payment form
}
```

### CheckoutForm Component

The form component that renders Stripe's PaymentElement.

#### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `stripeDetails` | `StripeDetails` | Yes | Stripe configuration |
| `txnId` | `string` | Yes | Transaction ID |
| `amount` | `number` | Yes | Payment amount in cents |
| `onSucces` | `(object: any) => void` | Yes | Success callback |
| `redirectionUrl` | `string` | No | Redirect URL after payment |

## Backend Integration

This library requires a backend service that implements the following endpoints:

### Initialize Payment
```
POST /user/{userId}/add-card/initialize
Headers:
  - authorization: {userToken}
  - userId: {userId}
  - response-version: v2
Body:
  - amount: number
  - refundable: boolean
```

### Complete Payment
```
POST /user/{userId}/add-card/complete
Headers:
  - authorization: {userToken}
  - userId: {userId}
  - response-version: v2
Body:
  - txnId: string
```

## Development

### Prerequisites

- Node.js 14+
- Yarn package manager

### Setup

```bash
# Install dependencies
yarn install

# Start Storybook for development
yarn start

# Build the library
yarn build

# Run tests
yarn test
```

### Scripts

- `yarn start` - Start Storybook development server
- `yarn build` - Build the library for production
- `yarn test` - Run tests
- `yarn format` - Format code with Prettier
- `yarn analyze` - Analyze bundle size

## Storybook

This project uses Storybook for component development and documentation. Run `yarn start` to launch the Storybook interface at `http://localhost:6006`.

## Technologies

- **React 18** - UI framework
- **TypeScript** - Type safety
- **Stripe** - Payment processing
- **Axios** - HTTP client
- **Rollup** - Module bundler
- **PostCSS** - CSS processing
- **Storybook** - Component development
- **Jest** - Testing framework

## Browser Support

This library supports all modern browsers:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

ISC

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions, please create an issue in the repository.
