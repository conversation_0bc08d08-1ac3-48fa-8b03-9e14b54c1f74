{"name": "@frontend/order", "version": "0.0.0", "description": "This is front end dashboard code base for order library", "main": "lib/index.js", "module": "lib/index.esm.js", "types": "lib/index.d.ts", "scripts": {"analyze": "source-map-explorer 'lib/*'", "bootstrap": "yarn install", "build": "rollup -c", "clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write \"src/**/*\"", "start": "start-storybook -p 6006", "test": "yarn test", "verify": "npm-run-all clean bootstrap build", "preversion": "yarn verify", "build-storybook": "build-storybook"}, "keywords": ["dashboard", "order", "orders", "react"], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.21.0", "@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-node-resolve": "^15.0.1", "@storybook/addon-actions": "^6.5.16", "@storybook/addon-essentials": "^6.5.16", "@storybook/addon-links": "^6.5.16", "@storybook/addon-postcss": "^2.0.0", "@storybook/react": "^6.5.16", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/dot-object": "^2.1.2", "@types/jest": "^29.2.3", "@types/lodash": "^4.14.191", "@types/react": "^17.0.2", "@types/react-js-pagination": "^3.0.4", "@types/react-router-dom": "5.3.3", "@types/react-table": "^7.7.14", "@types/styled-components": "^5.1.26", "babel-loader": "^9.1.2", "generate-react-cli": "^8.2.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.19", "prettier": "^2.8.4", "react": "^18.3.1", "rimraf": "^4.1.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.34.1", "source-map-explorer": "^2.5.3", "storybook-css-modules-preset": "^1.1.1", "terser": "^5.16.5", "typescript": "^4.9.5"}, "dependencies": {"@frontend/core": "*", "@frontend/user": "*", "axios": "0.26.1", "bootstrap": "^5.2.3", "dot-object": "^2.1.4", "lodash": "^4.17.21", "moment": "^2.29.4", "react-bootstrap": "^1.5.2", "react-js-pagination": "^3.0.3", "react-router-dom": "5.3.4", "react-table": "^7.8.0", "rollup": "2.79.1", "styled-components": "^5.3.9", "sweetalert2": "^11.7.3"}, "files": ["/lib"], "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}