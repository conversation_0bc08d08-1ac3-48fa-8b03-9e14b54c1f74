# Cleveland Web Order

A React component library for building dashboard features related to order management. This library provides a comprehensive set of components for displaying, creating, editing, and managing orders in web applications.

## Features

- 📋 **Order Management**: Complete CRUD operations for orders
- 👥 **Agent Assignment**: Assign and manage agents for orders
- 📊 **Order Tables**: Sortable and filterable order displays
- 🔍 **Order Details**: Detailed order view with line items
- 📝 **Dynamic Forms**: Configurable forms for order creation and editing
- 🏷️ **Order Tabs**: Filter orders by service type
- 🎨 **Styled Components**: Bootstrap-based responsive design

## Components

### Core Components

- **`Orders`** - Main order management component with full CRUD functionality
- **`OrderTable`** - Tabular display of orders with actions
- **`OrderDetailView`** - Modal component for viewing detailed order information
- **`CreateOrderForm`** - Form component for creating new orders
- **`AssignAgent`** - Component for assigning agents to orders
- **`ActionForm`** - Generic dynamic form component
- **`OrderTabs`** - Tabbed interface for filtering orders
- **`OrderViewTable`** - Alternative table view for orders
- **`OrdersAccordian`** - Accordion-style order display

## Installation

### Prerequisites

- Node.js (version 14 or higher)
- Yarn package manager
- `NODE_AUTH_TOKEN` environment variable set with GitHub package registry access

### Install Dependencies

```bash
yarn bootstrap
```

This command installs all project dependencies. Ensure your `NODE_AUTH_TOKEN` is configured for accessing private GitHub packages.

## Development

### Start Development Server

```bash
yarn start
```

Runs Storybook development server at [http://localhost:6006](http://localhost:6006). The page will reload automatically when you make changes.

### Run Tests

```bash
yarn test
```

Launches the test runner in interactive watch mode using Jest and React Testing Library.

### Build Library

```bash
yarn build
```

Builds the library for production using Rollup. Output files are generated in the `lib/` directory.

### Format Code

```bash
yarn format
```

Formats all source files using Prettier.

### Analyze Bundle

```bash
yarn analyze
```

Analyzes the built bundle size using source-map-explorer.

## Usage

```jsx
import { Orders, OrderTable, CreateOrderForm } from '@sworksio/cleveland-web-order';

// Basic order management
function OrderDashboard() {
  return (
    <Orders
      apiConfigurations={apiConfig}
      themeColors={themeConfig}
      onCancelClick={handleCancel}
    />
  );
}

// Standalone order table
function OrderList() {
  return (
    <OrderTable
      orders={orderData}
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  );
}
```

## Configuration

The components require configuration objects for API endpoints and theming:

```jsx
const apiConfigurations = {
  // API endpoint configurations
};

const themeColors = {
  // Theme color configurations
};
```

## Dependencies

### Core Dependencies
- React 17+
- React-DOM 17+
- Bootstrap 5.2+
- Axios for API calls
- Lodash for utilities
- Moment.js for date handling
- React Router DOM for navigation
- React Table for data tables
- Styled Components for styling

### Development Dependencies
- TypeScript for type safety
- Rollup for bundling
- Storybook for component development
- Jest & React Testing Library for testing
- Prettier for code formatting

## Browser Support

- Chrome (last version)
- Firefox (last version)
- Safari (last version)
- Edge (last version)

## Contributing

1. Clone the repository
2. Install dependencies with `yarn bootstrap`
3. Start development server with `yarn start`
4. Make your changes
5. Run tests with `yarn test`
6. Format code with `yarn format`
7. Build with `yarn build`

## License

ISC

## Package Information

- **Name**: @sworksio/cleveland-web-order
- **Version**: 0.0.0
- **Registry**: GitHub Package Registry
- **Main**: lib/index.js
- **Module**: lib/index.esm.js
- **Types**: lib/index.d.ts
