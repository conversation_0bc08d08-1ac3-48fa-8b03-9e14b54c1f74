/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { Orders, OrdersProps } from './Orders';
import { MemoryRouter } from 'react-router-dom';

export default {
  title: 'Components/Orders',
  component: Orders,
  decorators: [
    (Story) => (
      // @ts-ignore
      <MemoryRouter>
        <Story />
      </MemoryRouter>
    ),
  ],
} as Meta;

const Template: Story<OrdersProps> = (args) => <Orders {...args} />;

export const OrderComponent = Template.bind({});

OrderComponent.args = {
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#3B8396',
    primary: '#FFFFFF',
  },
  title: 'Upcoming Rides',
  helpText: 'This is where you can see all upcoming rides of users',
  moduleConfigurations: {
    defaultQueryParams: 'status[]=CONFIRM&status[]=ACCEPT&isActive=true&sort=-_id',
    isSearchEnable: true,
    isAddEnable: true,
    isConfigUpdateEnable: true,
    dataColumns: [
      {
        id: 'startTime',
        Header: 'Start Time',
        filter: 'date',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.startTime ? d.startTime : '-';",
          },
        },
      },
      {
        id: 'endTime',
        Header: 'End Time',
        filter: 'date',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.endTime ? d.endTime : '-';",
          },
        },
      },
      {
        id: 'userData.name',
        Header: 'Rider Name',
        filter: 'fuzzyText',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.userData?.name ? d.userData?.name : '-';",
          },
        },
      },
      {
        id: 'tasks.$.name',
        Header: 'Driver Name',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.taskAcceptance ? d.taskAcceptance.name : '-';",
          },
        },
      },
      {
        id: 'address.pickupAddress.addressLine1',
        Header: 'Start Address',
        filter: 'fuzzyText',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.address?.pickupAddress?.addressLine1 ? d.address?.pickupAddress?.addressLine1 : '-';",
          },
        },
      },
      {
        id: 'address.dropAddress.addressLine1',
        Header: 'End Address',
        filter: 'fuzzyText',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.address?.dropAddress?.addressLine1 ? d.address?.dropAddress?.addressLine1 : '-';",
          },
        },
      },
      {
        id: 'grossAmount',
        Header: 'Estimate Price',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.grossAmount ? d.grossAmount : '-';",
          },
        },
      },
      {
        id: 'addOnCost',
        Header: 'Addon Price',
      },
      {
        id: 'amount',
        Header: 'Total Price',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.amount ? d.amount : '-';",
          },
        },
      },
      {
        id: 'description',
        Header: 'Notes',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.description ? d.description : '-';",
          },
        },
      },
      {
        id: 'items.title',
        Header: 'Service Type',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.items && d.items.length > 0 ? d.items[0].title : '-';",
          },
        },
      },
      {
        id: 'tasksAcceptedBy',
        Header: 'Driver Assigned?',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.tasksAcceptedBy?.length > 0 ? 'YES' : 'NO';",
          },
        },
      },
      {
        id: 'payment.payeeCardId',
        Header: 'Payment Mode',
        accessor: {
          function: {
            arguments: 'd',
            body: "return d.payment?.method || '-';",
          },
        },
      },
    ],
    onLoadConfig: {
      selectableValue: [
        {
          key: 'catalogueId',
          type: 'default',
          mappedKey: '_id',
          defaultQueryParams: 'type=SERVICE&isActive=true',
        },
        {
          key: 'parentId',
          type: 'default',
          mappedKey: '_id',
          defaultQueryParams: 'type=SERVICE_CATEGORY&customData.level=2&sort=-_id',
        },
      ],
    },
    formConfig: {
      viewOrderDetailConfig: {
        title: 'View Order Detail',
        dataColumns: [
          {
            id: 'name',
            Header: 'Order Items',
            accessor: 'name',
          },
          {
            id: 'quantity',
            Header: 'Quantity',
            accessor: 'quantity',
          },
          {
            id: 'amount',
            Header: 'Price',
            accessor: 'amount',
          },
        ],
        config: [
          {
            label: 'Order id',
            key: '_id',
          },
          {
            label: 'Order Date',
            key: 'startTime',
          },
          {
            label: 'Order Status',
            key: 'status',
          },
          {
            label: 'Total Price',
            key: 'totalDisplayAmount',
          },
          {
            label: 'Delivery Address',
            key: 'address.dropAddress.addressLine1',
          },
          {
            label: 'Payment Method',
            key: 'payment.method',
          },
          {
            label: 'Payment Status',
            key: 'payment.status',
          },
        ],
      },
      editOrder: {
        title: 'Edit Rides',
        submitLabel: 'Save',
        config: [
          {
            id: 'userData-name',
            label: 'Customer Name',
            placeholder: 'Please select',
            type: 'text',
            validationType: 'string',
            value: '',
            validations: [],
            readOnly: true,
          },
          {
            id: 'startTime',
            label: 'Start Date & Time',
            placeholder: 'Enter Scheduled Date',
            type: 'date',
            showTimeSelect: true,
            validationType: 'date',
            value: '',
            isUtc: true,
            displayFormat: 'ddd, MMM Do YYYY, h:mm a',
            maxAllowedDays: 30,
            timeIntervals: 10,
            validations: [],
            readOnly: false,
            configurations: {
              isEditMode: true,
            },
          },
          {
            id: 'endTime',
            label: 'End Date & Time',
            placeholder: 'Enter Scheduled Date',
            type: 'date',
            showTimeSelect: true,
            validationType: 'date',
            value: '',
            isUtc: true,
            displayFormat: 'ddd, MMM Do YYYY, h:mm a',
            maxAllowedDays: 30,
            timeIntervals: 10,
            validations: [],
            readOnly: false,
            configurations: {
              isEditMode: true,
            },
          },
          {
            id: 'address-pickupAddress',
            label: 'Trip Details',
            type: 'locationV2',
            validationType: 'mixed',
            value: {
              addressLine1: 'Texas, USA',
              country: 'US',
              latitude: 31.9685988,
              longitude: -99.9018131,
              state: 'TX',
            },
            validations: [
              {
                type: 'required',
                params: ['Pickup Address is required'],
              },
            ],
            configurations: {
              isEditMode: true,
            },
            apiKey: 'AIzaSyA2Ob5jpJOQNWl5xxvTeiLJ5CrhHhuCN7c',
            searchable: true,
          },
          {
            id: 'address-dropAddress',
            type: 'locationV2',
            validationType: 'mixed',
            value: '',
            validations: [
              {
                type: 'required',
                params: ['Drop Address is required'],
              },
            ],
            configurations: {
              isEditMode: true,
            },
            apiKey: 'AIzaSyA2Ob5jpJOQNWl5xxvTeiLJ5CrhHhuCN7c',
            searchable: true,
          },
          {
            id: 'description',
            label: 'Notes',
            placeholder: 'Enter Notes',
            type: 'text',
            subType: 'text',
            validationType: 'string',
            value: '',
            validations: [
              {
                type: 'max',
                params: [120, 'Estimate Price cannot be more than 120 characters'],
              },
            ],
          },
          {
            id: 'parentId',
            label: 'Service Type',
            placeholder: 'Select Service Type',
            type: 'select',
            options: [],
            value: '',
            validationType: 'string',
            configurations: {
              isEditMode: true,
            },
            validations: [
              {
                type: 'required',
                params: ['Service type is required'],
              },
            ],
          },
          {
            id: 'catalogueId',
            label: 'Vehicle Type',
            placeholder: 'Select Vehicle',
            type: 'select',
            options: [],
            value: '',
            validationType: 'string',
            configurations: {
              isEditMode: true,
            },
            validations: [
              {
                type: 'required',
                params: ['Vehicle type is required'],
              },
            ],
          },
          {
            id: 'quantity',
            label: 'Total Seats',
            placeholder: 'Enter Total Seats',
            type: 'text',
            subType: 'number',
            value: '',
            validationType: 'number',
            validations: [
              {
                type: 'required',
                params: ['Total seats count is required'],
              },
              {
                type: 'max',
                params: [20, 'Total seats cannot be more than 20'],
              },
            ],
            configurations: {
              isEditMode: true,
            },
          },
          {
            id: 'customData-paymentStatus',
            label: 'Payment Type',
            placeholder: 'Select Payment Type',
            type: 'select',
            validationType: 'string',
            value: '',
            options: [
              {
                id: 'paid',
                name: 'Paid',
              },
              {
                id: 'unpaid',
                name: 'Unpaid',
              },
            ],
            validations: [
              {
                type: 'required',
                params: ['Payment Type is required'],
              },
            ],
            configurations: {
              isEditMode: true,
            },
          },
        ],
      },
      assignAgent: {
        title: 'Assign Driver',
        fields: 'name,familyName,givenName,email,contactNumber,profilePicture,presenceStatus,address',
        defaultQueryParams: 'type=driver&isActive=true&sort=-_id',
        defaultAccept: false,
        buttonLabel: 'Assign Task',
        statusConfigurations: {
          ONLINE: {
            key: 'ONLINE_SERVING',
            label: 'Online',
            selectionDisabled: false,
            class: 'online',
          },
          ONLINE_READY_FOR_SERVICE: {
            key: 'ONLINE_READY_FOR_SERVICE',
            label: 'Online ready for service',
            selectionDisabled: false,
            class: 'readyToServe',
          },
          OFFLINE: {
            key: 'OFFLINE',
            label: 'Offline',
            selectionDisabled: false,
            class: 'offline',
          },
          UNKNOWN: {
            key: 'UNKNOWN',
            selectionDisabled: false,
            label: 'Recently Joined',
            class: 'unknown',
          },
        },
      },
      createOrder: {
        title: 'Create New Trip',
        submitLabel: 'CREATE TRIP',
        addUserFormEventKey: '/users/riders',
        addUserFormSubEventKey: '/users/riders',
        addOrderFormEventKey: '/orders',
        addOrderFormSubEventKey: '/orders/upcoming',
        staticData: {
          type: 'PACKAGE_RIDE',
        },
        config: [
          {
            id: 'startTime',
            label: 'Start Time',
            placeholder: 'Enter Scheduled Date',
            type: 'date',
            showTimeSelect: true,
            validationType: 'date',
            value: '',
            isUtc: true,
            maxAllowedDays: 30,
            timeIntervals: 15,
            validations: [
              {
                type: 'required',
                params: ['Start time is required'],
              },
            ],
          },
          {
            id: 'endTime',
            label: 'End Time',
            placeholder: 'Enter Scheduled Date',
            type: 'date',
            showTimeSelect: true,
            validationType: 'date',
            value: '',
            isUtc: true,
            maxAllowedDays: 30,
            timeIntervals: 15,
            validations: [],
          },
          {
            id: 'pickupAddress',
            label: 'Pickup Address',
            name: 'Pickup-Address',
            placeholder: 'Enter Pickup Address',
            type: 'location',
            validationType: 'mixed',
            validations: [
              {
                type: 'required',
                params: ['Pickup Address is required'],
              },
            ],
            apiKey: 'AIzaSyA2Ob5jpJOQNWl5xxvTeiLJ5CrhHhuCN7c',
            searchable: true,
          },
          {
            id: 'dropAddress',
            label: 'Drop Address',
            name: 'Drop-Address',
            placeholder: 'Enter Drop Address',
            type: 'location',
            validationType: 'mixed',
            configurations: {
              componentRestrictions: {
                country: [],
              },
            },
            validations: [
              {
                type: 'required',
                params: ['Drop Address is required'],
              },
            ],
            notFoundError: 'No matching location found',
            apiKey: 'AIzaSyA2Ob5jpJOQNWl5xxvTeiLJ5CrhHhuCN7c',
            searchable: true,
          },
          {
            id: 'parentId',
            label: 'Service Type',
            placeholder: 'Select Service Type',
            type: 'select',
            validationType: 'string',
            value: '',
            options: [],
            validations: [
              {
                type: 'required',
                params: ['Service type is required'],
              },
            ],
          },
          {
            id: 'catalogueId',
            label: 'Vehicle Type',
            placeholder: 'Select Vehicle',
            type: 'select',
            validationType: 'string',
            value: '',
            options: [],
            validations: [
              {
                type: 'required',
                params: ['Vehicle type is required'],
              },
            ],
          },
          {
            id: 'quantity',
            label: 'Total Seats',
            placeholder: 'Enter Total Seats',
            type: 'text',
            subType: 'number',
            value: '',
            validationType: 'number',
            validations: [
              {
                type: 'required',
                params: ['Total seats count is required'],
              },
              {
                type: 'max',
                params: [20, 'Total seats cannot be more than 20'],
              },
            ],
          },
          {
            id: 'customData-paymentStatus',
            label: 'Payment Type',
            placeholder: 'Select Payment Type',
            type: 'select',
            validationType: 'string',
            value: '',
            options: [
              {
                id: 'paid',
                name: 'Paid',
              },
              {
                id: 'unpaid',
                name: 'Unpaid',
              },
            ],
            validations: [
              {
                type: 'required',
                params: ['Payment Type is required'],
              },
            ],
          },
        ],
        userSelectConfig: {
          name: 'name',
          label: 'Choose Customer',
          notFoundError: 'No Customer Found',
        },
      },
    },
    actions: [
      {
        type: 'assign-agent',
        label: 'ASSIGN DRIVER',
      },
      {
        type: 'edit',
        label: 'EDIT RIDE',
      },
      {
        label: 'CANCEL RIDE',
        type: 'admin-cancel-order',
      },
    ],
  },
  tabViewConfigurations: {
    isEnable: false,
    type: 'catalogueId',
    filterKey: 'items.0._id',
  },
  apiConfigurations: {
    baseUrl: 'http://localhost:3104',
    userId: '622b2efdb6b36a3781c30cce',
    token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyIsImV4cGlyZXNJbiI6IjE4MjUgZGF5cyJ9.eyJpZCI6IjYyMmIyZWZkYjZiMzZhMzc4MWMzMGNjZSIsInN1YiI6IjYyMmIyZWZkYjZiMzZhMzc4MWMzMGNjZSIsInRlbmFudElkIjoiYnVsbGNpdHkwMDEiLCJyb2xlcyI6WyJhZG1pbiJdLCJpYXQiOjE2ODEzNzAxMTIsImV4cCI6MTgzOTA1MDExMiwiYXVkIjoiaHR0cHM6Ly95b3VyZG9tYWluLmNvbSIsImlzcyI6ImZlYXRoZXJzIiwianRpIjoiNjJhYjM5MTUtYTYwNC00NmU3LWE4ZTgtMzhmNjA2MjhjNWEzIn0.rz-iOoXu2Qe41G6DnOxGtO5EbCs3uKpoE00y4lA_Lzk',
  },
};
