import React, { useEffect, useState } from 'react';
import { Icon, InfoBox, PageHeader, ProgressBar, Search, ErrorHandler } from '@frontend/core';
// import { linkTo } from '@storybook/addon-links';
import "@frontend/core/lib/index.css"
import { But<PERSON>, Col, Row } from 'react-bootstrap';
import axios, { AxiosRequestConfig } from 'axios';
import { size } from 'lodash';
import { useLocation } from 'react-router-dom';
import Pagination from 'react-js-pagination';
import Dot from 'dot-object';
import 'bootstrap/dist/css/bootstrap.css';
import 'react-datepicker/dist/react-datepicker.css';
import Swal from 'sweetalert2';
import moment from 'moment';
import { OrderViewTable } from '../OrderViewTable/OrderViewTable.lazy';
import { ActionForm } from '../ActionForm/ActionForm.lazy';
import { CreateOrderForm } from '../CreateOrderForm';
import { ApiConfigurations, ThemeColors } from '../SharedExports';
import styles from './Orders.module.css';
import { AssignAgent } from '../AssignAgent/AssignAgent.lazy';
import { OrderDetailView } from '../OrderDetailView/OrderDetailView.lazy';
import { OrdersAccordian } from '../OrdersAccordian/OrdersAccordian.lazy';
import { Form } from 'react-bootstrap';
import { OrderTabs } from '../OrderTabs';

export interface OrdersProps {
  /**
   * The colors to be utilized for the component
   */
  themeColors: ThemeColors;
  /**
   * The help text of the component
   */
  helpText: string;
  /**
   * The title of the header
   */
  title: string;
  /**
   * The type of loader to be used
   */
  loaderType?: any;

  /**
   * The configurations required for API call
   */
  apiConfigurations: ApiConfigurations;
  /**
   * The configurations required for module
   */
  moduleConfigurations: Record<string, any>;

  /**
   *  The configurations required for tab view
   */
  tabViewConfigurations?: Record<string, any>;
}

const staticColumns = [
  {
    id: 'createdAt',
    filter: 'date',
    accessor: (d: any) => {
      return moment(d.createdAt).format('ddd, MMM Do YYYY, h:mm a');
    },
  },
  {
    id: 'startTime',
    filter: 'date',
    accessor: (d: any) => {
      return moment(d.startTime).format('ddd, MMM Do YYYY, h:mm a');
    },
  },
  {
    id: 'endTime',
    filter: 'date',
    accessor: (d: any) => {
      return moment(d.endTime).format('ddd, MMM Do YYYY, h:mm a');
    },
  },
  {
    id: 'addOnCost',
    accessor: (d: any) => {
      const items = d.items || [];
      const result: string[] = [];
      for (const item of items) {
        item.addonCost?.map((data: any) => {
          result.push(`${data.displayKey}: $${data.amount}`);
        });
      }
      return result.length > 0 ? result.join('\r\n') : '-';
    },
  },
];
const dotHyphen = new Dot('-');
const dot = new Dot('.');

export const Orders: React.FC<OrdersProps> = ({
  themeColors,
  helpText,
  title,
  loaderType,
  moduleConfigurations,
  apiConfigurations,
  tabViewConfigurations,
  ...props
}) => {
  let skip = 0;
  const limit = 10;
  const isSearchEnable = moduleConfigurations.isSearchEnable || false;
  const isFilterEnable = moduleConfigurations.isFilterEnable || false;
  const isAddEnable = moduleConfigurations.isAddEnable || false;
  loaderType = loaderType || 'Audio';
  let location: any = useLocation();
  let defaultValue: any = {};

  const [orders, setOrders] = useState();
  const [order, setOrder] = useState<any>();
  const [state, setState] = useState<any>();
  const [orderFetched, setOrderFetched] = useState(false);
  const [apiResponse, setApiResponse] = useState<any>({});
  const [activePage, setActivePage] = useState(1);
  const [dataColumns, setDataColumns] = useState([]);
  const [isColumnsSet, setIsColumnsSet] = useState(false);
  const [queryParams, setQueryParams] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState();
  const [showAssignAgentModal, setAssignAgentModal] = useState(false);
  const [showCreateOrderModal, setShowCreateOrderModal] = useState(false);
  const [formType, setFormType] = useState('');
  const [inputFile, setInputFile] = useState<HTMLInputElement | null>(null);
  const [showOrderDetailModal, setOrderDetailModal] = useState(false);
  const [activeData, setActiveData] = useState<any>({});
  const [serviceType, setServiceType] = useState<any[]>();
  const [activeTab, setActiveTab] = useState();
  const screenWidth = window.innerWidth;
  const [orderConfig, setOrderConfig] = useState<any[]>();

  useEffect(() => {
    combinedDataColumns(moduleConfigurations.dataColumns, staticColumns);
    getOnLoadDefaultValues(moduleConfigurations.onLoadConfig);
    setInputFile(document.getElementById('upload-file') as HTMLInputElement);
  }, []);

  useEffect(() => {
    if (!tabViewConfigurations?.isEnable || !serviceType) {
      return;
    }
    const defaultServiceType = serviceType ? serviceType[0] : null;
    setActiveTab(defaultServiceType);
    if (defaultServiceType != null) {
      const query = `&${tabViewConfigurations?.filterKey}=${defaultServiceType._id}`;
      callGetOrdersService(skip, query);
      setQueryParams(query);
    }
  }, [serviceType]);

  useEffect(() => {
    const isTabViewEnabled = tabViewConfigurations?.isEnable ?? false;
    if (!isTabViewEnabled) {
      callGetOrdersService(skip, '');
    }
  }, [tabViewConfigurations]);

  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  function callGetOrdersService(skipValue: number, queryString: string) {
    setOrderFetched(false);
    let defaultQuery = `?offset=${skipValue}&limit=${limit}`;
    if (size(moduleConfigurations?.defaultQueryParams) > 0) {
      defaultQuery += `&${moduleConfigurations.defaultQueryParams}`;
    }
    if (size(queryString) > 0) {
      defaultQuery += `&${queryString}`;
    }
    const url = `${apiConfigurations.baseUrl}/order/filter${defaultQuery}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'GET',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        processDataOnGet(res);
      })
      .catch((err: any) => {
        processErrorDataOnGet(err);
      });
  }

  function callDeleteOrderService(id: string, type: string) {
    setOrderFetched(false);
    const url = `${apiConfigurations.baseUrl}/order/${id}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'DELETE',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      params: {
        type,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .delete(url, requestConfig)
      .then((res: any) => {
        processDataOnDelete(res);
      })
      .catch((err: any) => {
        processErrorDataOnDelete(err);
      });
  }

  const handleExportOrder = () => {
    // const limit = 1000;
    let defaultQuery = `?limit=${moduleConfigurations.downloadConfig.limit}`;
    if (size(moduleConfigurations?.defaultQueryParams) > 0) {
      defaultQuery += `&${moduleConfigurations.defaultQueryParams}`;
    }
    const url = `${apiConfigurations.baseUrl}/order/download${defaultQuery}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'GET',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        setTimeout(() => {
          Swal.fire('Good job!', `${moduleConfigurations.downloadConfig.exportMsg}`, 'success');
        }, 2000);
      })
      .catch((err: any) => {
        Swal.fire({
          icon: 'error',
          title: 'Oops...',
          text: `${err}`,
        });
      });
  };

  function callEditOrderService(data: any, staticData: any) {
    setOrderFetched(false);
    staticData = staticData || {};
    const url = `${apiConfigurations.baseUrl}/order/${order?._id}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'PATCH',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data: { ...data, ...staticData },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .patch(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnEdit(res);
      })
      .catch((err: any) => {
        processErrorDataOnEdit(err);
      });
  }

  function callCreateOrderService(data: any, staticData: any) {
    staticData = staticData || {};
    setOrderFetched(false);
    const url = `${apiConfigurations.baseUrl}/order`;
    let requestConfig: AxiosRequestConfig = {
      method: 'POST',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${data.customer.id}`,
      },
      data: {
        status: 'open',
        payment: {
          method: data.paymentType ? data.paymentType : 'CASH',
        },
        customData: {
          source: 'admin-dashboard',
        },
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .post(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnCreate(res, data, staticData);
      })
      .catch((err: any) => {
        processErrorDataOnCreate(err);
      });
  }

  function callAddLineItemToOrderService(res: any, data: any, staticData: any) {
    staticData = staticData || {};
    setOrderFetched(false);
    const url = `${apiConfigurations.baseUrl}/order/${res._id}/item`;
    let requestConfig: AxiosRequestConfig = {
      method: 'PATCH',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data: {
        catalogueId: data.catalogueId,
        parentId: data.parentId,
        quantity: data.quantity || 1,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .patch(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnLineItemAdd(res, data, staticData);
      })
      .catch((err: any) => {
        processErrorDataOnLineItemAdd(err);
      });
  }

  function callConfirmOrderService(res: any, data: any, staticData: any) {
    staticData = staticData || {};
    setOrderFetched(false);
    const url = `${apiConfigurations.baseUrl}/order/${res._id}/confirm`;
    let body: any = { ...staticData };
    let address: any = {};
    if (data.pickupAddress) {
      address.pickupAddress = data.pickupAddress;
    }
    if (data.dropAddress) {
      address.dropAddress = data.dropAddress;
    }
    if (data.pickupAddress || data.dropAddress) {
      body = {
        ...body,
        address,
      };
    }
    body = {
      ...body,
      ...data,
    };
    let requestConfig: AxiosRequestConfig = {
      method: 'PATCH',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data: {
        ...body,
        ...staticData,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .patch(url, requestConfig.data, requestConfig)
      .then((res: any) => {
        processDataOnConfirm(res, data, staticData);
      })
      .catch((err: any) => {
        processErrorDataOnConfirm(err);
      });
  }

  function callUpdateOrderStatusService(id: string, status: string) {
    const url = `${apiConfigurations.baseUrl}/order/${id}/status`;
    const data = {
      status,
    };
    let requestConfig: AxiosRequestConfig = {
      method: 'PATCH',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data,
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .patch(url, data, requestConfig)
      .then((res: any) => {
        processUpdateOrderStatus(res);
      })
      .catch((err: any) => {
        processErrorOnUpdateOrderStatus(err);
      });
  }

  function callGetCatalogueServiceWithoutSkip(configuration: Record<string, any>) {
    const limit = configuration.limit || 20;
    let defaultQuery = `?limit=${limit}`;
    if (size(configuration?.defaultQueryParams) > 0) {
      defaultQuery += `&${configuration.defaultQueryParams}`;
    }

    const url = `${apiConfigurations.baseUrl}/catalogue${defaultQuery}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'GET',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        defaultValue = {
          ...defaultValue,
          [configuration.key]: {
            data: res.data?.result?.categories || [],
            config: configuration,
          },
        };
        setState(defaultValue);
        getStateStatus(defaultValue);
        if (tabViewConfigurations && tabViewConfigurations.isEnable) {
          setServiceType(defaultValue[tabViewConfigurations.type]?.data || []);
        }
      })
      .catch((err: any) => {
        return [];
      });
  }

  function getOnLoadDefaultValues(onLoadConfig: Record<string, any>) {
    if (onLoadConfig?.selectableValue) {
      for (const item of onLoadConfig.selectableValue) {
        if (item.type === 'default' && size(defaultValue) === 0) {
          callGetCatalogueServiceWithoutSkip(item);
        }
      }
    }
  }

  function getStateStatus(defaultValue: Record<string, any>) {
    if (location.state?.user) {
      showCreateView(defaultValue);
    }
  }

  function processDataOnGet(res: any) {
    if (res.data?.result?.orders) {
      setApiResponse(res.data.result);
      const orders = res.data?.result?.orders || [];
      setOrders(orders);
      setOrderFetched(true);
    } else {
      setOrderFetched(true);
      processErrorDataOnGet(res);
    }
  }

  function processErrorDataOnGet(err: any) {
    const errorMessage = `There seems to be some issue with getting the ${title.toLowerCase()}! Please try again after some time!`;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processDataOnDelete(res: any) {
    if (res.data?.result) {
      callGetOrdersService(skip, queryParams);
    } else {
      processErrorDataOnDelete(res);
    }
  }

  function processErrorDataOnDelete(err: any) {
    const errorMessage = `There seems to be some issue with deleting the ${title.toLowerCase()}! Please try again after some time!`;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processDataOnEdit(res: any) {
    if (res.data?.result) {
      dismissActionForm();
      callGetOrdersService(skip, queryParams);
    } else {
      processErrorDataOnEdit(res);
    }
  }

  function processErrorDataOnEdit(err: any) {
    setOrderFetched(true);
    const errorMessage = `There seems to be some issue with changing the ${title.toLowerCase()}! Please try again after some time!`;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processDataOnCreate(res: any, data: any, staticData: any) {
    if (res.data?.result) {
      setOrder(res.data?.result);
      callAddLineItemToOrderService(res.data?.result, data, staticData);
    } else {
      processErrorDataOnCreate(res);
    }
  }

  function processDataOnLineItemAdd(res: any, data: any, staticData: any) {
    if (res.data?.result) {
      setOrder(res.data?.result);
      callConfirmOrderService(res.data?.result, data, staticData);
    } else {
      processErrorDataOnLineItemAdd(res);
    }
  }

  function processDataOnConfirm(res: any, data: any, staticData: any) {
    if (res.data?.result) {
      setOrder(res.data?.result);
      setOrderFetched(true);
      dismissCreateOrderView();
      Swal.fire({
        title: `Request has been confirmed`,
        icon: 'success',
        confirmButtonColor: themeColors.backgroundPrimary,
      });
      if (location.state?.user?.result?.user) {
        location.state.user.result.user = null;
      }
      callGetOrdersService(skip, queryParams);
    } else {
      processErrorDataOnConfirm(res);
    }
  }

  function processErrorDataOnCreate(err: any) {
    setOrderFetched(true);
    const errorMessage = `There seems to be some issue with creating the ${title.toLowerCase()}! Please try again after some time!`;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processErrorDataOnLineItemAdd(err: any) {
    setOrderFetched(true);
    const errorMessage = `There seems to be some issue with booking the ${title.toLowerCase()}! Please try again after some time!`;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processErrorDataOnConfirm(err: any) {
    setOrderFetched(true);
    const errorMessage = `There seems to be some issue with confirming the ${title.toLowerCase()}! Please try again after some time!`;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function processUpdateOrderStatus(res: any) {
    if (res.data?.result) {
      dismissActionForm();
      callGetOrdersService(skip, queryParams);
    } else {
      processErrorOnUpdateOrderStatus(res);
    }
  }

  function processErrorOnUpdateOrderStatus(err: any) {
    const errorMessage = `There seems to be some issue with changing the status of ${title.toLowerCase()}! Please try again after some time!`;
    Swal.fire({
      icon: 'error',
      title: 'Oops...',
      text: err.data?.error && err.data?.error.errorCode !== 6000 ? err.data.error?.errorMessage : errorMessage,
      confirmButtonColor: themeColors.backgroundPrimary,
    });
  }

  function handlePageChange(pageNumber: number) {
    if (pageNumber > 1) {
      skip = (pageNumber - 1) * limit;
    } else {
      skip = 0;
    }
    setActivePage(pageNumber);
    callGetOrdersService(skip, queryParams);
  }

  function callback(data: any, actionType: string, action: Record<string, any>) {
    switch (actionType) {
      case 'edit':
        showEditView(data.original);
        break;
      case 'delete':
        hardDeleteOrder(data.original, 'hard-delete');
        break;
      case 'soft-delete':
        softDeleteOrder(data.original, 'remove');
        break;
      case 'reactivate':
        softDeleteOrder(data.original, 'reactivate');
        break;
      case 'assign-agent':
        showAssignAgentView(data.original);
        break;
      case 'admin-cancel-order':
        adminCancelOrderStatus(data.original, action);
        break;
      case 'confirm-order':
        confirmOrderStatus(data.original, action);
        break;
      case 'in-progress-order':
        inProgressOrderStatus(data.original, action);
        break;
      case 'complete-order':
        completeOrderStatus(data.original, action);
        break;
      case 'view-order':
        showOrderDetailView(data.original);
        break;
      default:
        break;
    }
  }

  function showOrderDetailView(data: any) {
    setActiveData(data);
    const orderDetailConfig = JSON.parse(JSON.stringify(moduleConfigurations.formConfig.viewOrderDetailConfig));
    if (orderDetailConfig) {
      setModalConfig(orderDetailConfig);
      // setDataColumns(orderDetailConfig.dataColumns);
      setOrderDetailModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  function onViewOrderDetailCancelClick() {
    dismissOrderDetailView();
  }

  function dismissOrderDetailView() {
    setOrderDetailModal(false);
    setModalConfig(undefined);
  }

  function hardDeleteOrder(data: any, type: string) {
    Swal.fire({
      title: 'Are you sure?',
      text: `This is an irreversible action! On deleting the ${title.toLowerCase()}, user might not be able to see the ${title.toLowerCase()} permanently!  Do it, only if you are sure!`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: themeColors.backgroundPrimary,
      cancelButtonColor: themeColors.backgroundSecondary,
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        callDeleteOrderService(data._id, type);
      }
    });
  }

  function softDeleteOrder(data: any, type: string) {
    if (type === 'remove') {
      Swal.fire({
        title: 'Are you sure?',
        text: `On deleting the ${title.toLowerCase()}, user might not be able to see the ${title.toLowerCase()}!`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: themeColors.backgroundPrimary,
        cancelButtonColor: themeColors.backgroundSecondary,
        confirmButtonText: 'Yes, delete it!',
      }).then((result) => {
        if (result.isConfirmed) {
          callDeleteOrderService(data._id, type);
        }
      });
    } else {
      Swal.fire({
        title: 'Are you sure?',
        text: `On activating the ${title.toLowerCase()}, user can see the ${title.toLowerCase()} again!`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: themeColors.backgroundPrimary,
        cancelButtonColor: themeColors.backgroundSecondary,
        confirmButtonText: 'Yes, activate it!',
      }).then((result) => {
        if (result.isConfirmed) {
          callDeleteOrderService(data._id, type);
        }
      });
    }
  }

  function adminCancelOrderStatus(data: any, action: any) {
    const infoText =
      action.displayText?.infoText ||
      `On cancelling the ${title.toLowerCase()}, user might not be able to see the ${title.toLowerCase()}!`;
    Swal.fire({
      title: 'Are you sure?',
      text: infoText,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: themeColors.backgroundPrimary,
      cancelButtonColor: themeColors.backgroundSecondary,
      confirmButtonText: 'Yes, cancel it!',
    }).then((result) => {
      if (result.isConfirmed) {
        callUpdateOrderStatusService(data._id, 'ADMIN_CANCEL');
      }
    });
  }

  function inProgressOrderStatus(data: any, action: any) {
    const infoText =
      action.displayText?.infoText ||
      `On marking the ${title.toLowerCase()} in-progress, user would be notified for the ${title.toLowerCase()}!`;
    Swal.fire({
      title: 'Are you sure?',
      text: infoText,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: themeColors.backgroundPrimary,
      cancelButtonColor: themeColors.backgroundSecondary,
      confirmButtonText: 'Yes, Mark it in progress!',
    }).then((result) => {
      if (result.isConfirmed) {
        callUpdateOrderStatusService(data._id, 'ORDER_IN_PROGRESS');
      }
    });
  }

  function confirmOrderStatus(data: any, action: any) {
    const infoText =
      action.displayText?.infoText ||
      `On marking the ${title.toLowerCase()} approved, guard would be able to apply on the ${title.toLowerCase()}!`;
    Swal.fire({
      title: 'Are you sure?',
      text: infoText,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: themeColors.backgroundPrimary,
      cancelButtonColor: themeColors.backgroundSecondary,
      confirmButtonText: 'Yes, Approve the request!',
    }).then((result) => {
      if (result.isConfirmed) {
        callConfirmOrderService(
          data,
          {
            status: 'CONFIRM',
            description: data.description,
            startTime: data.startTime,
            endTime: data.endTime,
            orderExecutionTime: data.orderExecutionTime,
            customerContactNumber: data.customerContactNumber,
            title: data.title,
          },
          {},
        );
      }
    });
  }

  function completeOrderStatus(data: any, action: any) {
    const infoText =
      action.displayText?.infoText ||
      `On completing the ${title.toLowerCase()}, user would be charged for the ${title.toLowerCase()}! This action is irrevertible`;
    Swal.fire({
      title: 'Are you sure?',
      text: infoText,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: themeColors.backgroundPrimary,
      cancelButtonColor: themeColors.backgroundSecondary,
      confirmButtonText: 'Yes, complete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        callUpdateOrderStatusService(data._id, 'COMPLETED');
      }
    });
  }

  function combinedDataColumns(dataColumns: any, staticColumns: any) {
    if (!isColumnsSet) {
      for (let item of dataColumns) {
        Object.assign(
          item,
          staticColumns.find((y: any) => y?.id === item?.id),
        );
        if (typeof item.accessor === 'object' && item.accessor.function) {
          item.accessor = new Function(item.accessor.function.arguments, item.accessor.function.body);
        }
      }
      setIsColumnsSet(true);
      setDataColumns(dataColumns);
    }
  }

  function filterOrders(queryString: string) {
    if (tabViewConfigurations && tabViewConfigurations.isEnable && serviceType && activeTab) {
      const defaultServiceType = activeTab ? activeTab : serviceType[0];
      if (defaultServiceType != null) {
        const query = `&${tabViewConfigurations?.filterKey}=${defaultServiceType._id}`;
        queryString += query;
      }
    }
    skip = 0;
    setActivePage(1);
    setQueryParams(queryString);
    callGetOrdersService(skip, queryString);
  }

  function onCancelClick() {
    dismissActionForm();
  }

  function dismissActionForm() {
    setShowModal(false);
    setModalConfig(undefined);
    setFormType('');
    setOrderConfig(undefined);
  }

  function showCreateView(defaultValue?: any) {
    setFormType('create');
    const createConfig = moduleConfigurations.formConfig?.createOrder;
    if (createConfig) {
      const data: any = defaultValue || state || {};
      for (let item of createConfig.config) {
        if (item.type === 'select' && item.options?.length === 0) {
          if (data[item.id]) {
            item.options =
              data[item.id].data.map((item: any) => {
                return {
                  id: item._id,
                  name: item.description ? `${item.title} (${item.description})` : item.title,
                };
              }) || [];
          }
        }
      }
      setModalConfig(createConfig);
      setShowCreateOrderModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  function showEditView(data: any) {
    setActiveData(data);
    setFormType('edit');
    setShowModal(true);
    setOrder(data);
    const editConfig = JSON.parse(JSON.stringify(moduleConfigurations?.formConfig?.editOrder));
    const dotifyData = dot.dot(data);

    for (let item of editConfig?.config) {
      let id = item.id;
      id = id.replace(/-/g, '.');
      let value = dotifyData[id] || item.value;
      item.value = value;

      // Assign a value to the override price.
      if (item.id === 'overrideAmount') {
        item.value = dotifyData['items[0].overrideAmount'];
      }

      if (item.id === 'quantity') {
        item.value = dotifyData['items[0].quantity'];
      }

      if (item?.id === 'parentId') {
        item.value = dotifyData['items[0].serviceId'];
      }
      if (item?.id === 'catalogueId') {
        item.value = dotifyData['items[0]._id'];
      }

      if (item?.id === 'address-pickupAddress') {
        item.value = data.address.pickupAddress;
      }

      if (item?.id === 'address-dropAddress') {
        item.value = data.address.dropAddress;
      }

      if (item?.id === 'hourlyPricing') {
        const value = dotifyData['items[0].priceVariable.timeBasedPricing.slots[0].price.value'];
        item.value = value * 60;
      }

      if (item.type === 'radio' && item.options?.length === 0) {
        if (state[item?.id]) {
          item.options =
            state[item?.id]?.data?.map((types: any, index: number) => {
              return {
                id: types._id,
                name: types.description ? `${types.title} (${types.description})` : types.title,
                checked: dotifyData['items[0]._id'] === types?._id && `serviceId-${index}`,
              };
            }) || [];
        }
      }

      if (item.type === 'select' && item.options?.length === 0) {
        if (state[item?.id]) {
          item.options =
            state[item?.id]?.data?.map((types: any, index: number) => {
              return {
                id: types._id,
                name: types.description ? `${types.title} (${types.description})` : types.title,
              };
            }) || [];
        }
      }
    }

    if (editConfig) {
      setModalConfig(editConfig);
      setShowModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  function showAssignAgentView(data: any) {
    const assignAgentConfig = JSON.parse(JSON.stringify(moduleConfigurations.formConfig?.assignAgent));
    if (assignAgentConfig) {
      assignAgentConfig.orderId = data._id;
      assignAgentConfig.tasks = data.tasks || {};
      assignAgentConfig.selectedAgentsIds = [];
      if (data.address?.pickupAddress?.latitude && data.address?.pickupAddress?.longitude) {
        assignAgentConfig.orderLocation = `${data.address.pickupAddress.longitude},${data.address.pickupAddress.latitude}`;
        assignAgentConfig.pickupLocation = data.address?.pickupAddress;
      }
      if (data.tasks) {
        const tasks = ['ASSIGN', 'ACCEPT', 'OUT_FOR_DELIVERY'];
        const ids = [];
        for (const taskUserId in data.tasks) {
          if (tasks.indexOf(data.tasks[taskUserId].status) > -1) {
            ids.push(taskUserId);
          }
        }
        assignAgentConfig.selectedAgentsIds = ids;
      }
      setModalConfig(assignAgentConfig);
      setAssignAgentModal(true);
    } else {
      const errorMessage = `There seems to be some issue with configurations! Please try again after some time or logout and log back in once!`;
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: errorMessage,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    }
  }

  function onCreateOrderSubmitClick(data: any) {
    dotHyphen.object(data);
    const paymentType = data.paymentType ? data.paymentType : 'CASH';
    if (!data.customer || !data.customer.id) {
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: `Please select a customer before booking a cab`,
        confirmButtonColor: themeColors.backgroundPrimary,
      });
    } else {
      let displayString = `You are about to book a <b>${paymentType}</b> ride from <b>${data?.pickupAddress?.addressLine1}</b>`;
      if (data?.dropAddress?.addressLine1) {
        displayString += ` to <b>${data?.dropAddress?.addressLine1}</b>`;
      }
      if (data.paymentType == 'CARD' && data.customer.stripeCustomerId) {
        Swal.fire({
          title: 'Are you sure?',
          html: displayString,
          icon: 'info',
          showCancelButton: true,
          confirmButtonColor: themeColors.backgroundPrimary,
          cancelButtonColor: themeColors.backgroundSecondary,
          confirmButtonText: 'Yes, book it!',
        }).then((result) => {
          if (result.isConfirmed) {
            const formConfig = moduleConfigurations.formConfig;
            callCreateOrderService(data, formConfig.createOrder?.staticData);
          }
        });
      } else if (data.paymentType == 'CASH' || size(data.paymentType) == 0) {
        Swal.fire({
          title: 'Are you sure?',
          html: displayString,
          icon: 'info',
          showCancelButton: true,
          confirmButtonColor: themeColors.backgroundPrimary,
          cancelButtonColor: themeColors.backgroundSecondary,
          confirmButtonText: 'Yes, book it!',
        }).then((result) => {
          if (result.isConfirmed) {
            const formConfig = moduleConfigurations.formConfig;
            callCreateOrderService(data, formConfig.createOrder?.staticData);
          }
        });
      } else {
        Swal.fire({
          title: `Setup for card payment is not done for the user`,
          icon: 'error',
          confirmButtonColor: themeColors.backgroundPrimary,
        });
      }
    }
  }

  function onSubmitClick(data: any) {
    dotHyphen.object(data);
    const dotifyData = dot.dot(data);
    const formConfig = moduleConfigurations.formConfig;
    if (formType === 'edit') {
      // insert the overridePrice key and delete the amount key
      if (dotifyData && dotifyData.overrideAmount) {
        data['overridePrice'] = [{ catalogueId: order?.items[0]?._id, amount: dotifyData.overrideAmount }];
        delete dotifyData.overrideAmount;
      }
      if (dotifyData && dotifyData.catalogueId) {
        data.lineItems = {
          catalogueId: dotifyData.catalogueId,
          quantity: dotifyData.quantity,
          parentId: dotifyData.parentId,
        };
      }
      let address: any = {};

      if (data.address.pickupAddress) {
        address.pickupAddress = data.address.pickupAddress;
      }
      if (data.address.dropAddress) {
        address.dropAddress = data.address.dropAddress;
      }
      if (address.pickupAddress || address.dropAddress) {
        data = {
          ...data,
          address,
        };
      }
      callEditOrderService(data, formConfig.editOrder?.staticData);
    }
  }

  function onChangeTab(data: any) {
    setActiveTab(data);
    const query = `&${tabViewConfigurations?.filterKey}=${data._id}`;
    callGetOrdersService(skip, query);
    setQueryParams(query);
  }

  const handleUpload = () => {
    inputFile?.click();
  };

  function dismissAssignAgentView() {
    setAssignAgentModal(false);
    setModalConfig(undefined);
    callGetOrdersService(skip, queryParams);
  }

  function dismissCreateOrderView() {
    setShowCreateOrderModal(false);
    setModalConfig(undefined);
  }

  function onAssignAgentCancelClick() {
    dismissAssignAgentView();
  }

  function onCreateOrderCancelClick() {
    dismissCreateOrderView();
  }

  return (
    <>
      {screenWidth > 450 ? (
        <div data-testid="Orders">
          <PageHeader title={title} fontColor={themeColors?.textSecondary} />
          {tabViewConfigurations && tabViewConfigurations.isEnable && (
            <OrderTabs
              themeColors={themeColors}
              onChangeTab={(data: any) => onChangeTab(data)}
              serviceType={serviceType ? serviceType : []}
            ></OrderTabs>
          )}
          <InfoBox
            text={helpText}
            noGutter={false}
            iconName={'FaLightbulb'}
            background={themeColors?.backgroundSecondary}
            iconColor={themeColors?.textSecondary}
            fontColor={themeColors?.textAlternate}
          />
          <Row>
            {isSearchEnable && dataColumns?.length > 0 ? (
              <Col>
                <Search
                  dataColumns={dataColumns}
                  key={'search-component'}
                  handleSearchChangeFor={(queryParams: string) => filterOrders(queryParams)}
                />
              </Col>
            ) : (
              ''
            )}
            {isFilterEnable ? (
              <Col lg={2} xl={2} className={`d-flex justify-content-end mb-auto mx-auto`}>
                <Icon
                  iconName={'FaFilter'}
                  iconColor={themeColors.textSecondary}
                  label={'Filters'}
                  fontColor={themeColors.textSecondary}
                />
              </Col>
            ) : (
              ''
            )}

            {isAddEnable ? (
              <Col lg={2} xl={2} className={`d-flex justify-content-end mb-auto mx-auto`}>
                <Button
                  size="sm"
                  block
                  id="addNewClick"
                  type="button"
                  onClick={() => showCreateView()}
                  className={styles.addNewButton}
                >
                  {`Add ${title}`}
                </Button>
              </Col>
            ) : (
              ''
            )}
          </Row>
          <Row className={styles.showDisplay}>
            {orderFetched ? (
              <div>
                {' '}
                {size(orders) > 0 ? (
                  <>
                    <Col className={`d-flex`} xs={12}>
                      <OrderViewTable
                        orders={orders}
                        dataColumns={dataColumns}
                        themeColors={themeColors}
                        moduleConfigurations={moduleConfigurations}
                        callback={callback}
                        showOrderDetailView={showOrderDetailView}
                      />
                    </Col>
                    <Col className={`d-flex justify-content-center`}>
                      {/* @ts-ignore */}
                      <Pagination
                        activePage={activePage}
                        itemsCountPerPage={limit}
                        totalItemsCount={apiResponse?.total || 1}
                        pageRangeDisplayed={5}
                        prevPageText={'Prev'}
                        nextPageText={'Next'}
                        onChange={handlePageChange}
                        activeLinkClass={styles.paginationLinkActive}
                        itemClass={`page-item`}
                        linkClass={`page-link ${styles.paginationLink}`}
                      />
                    </Col>
                  </>
                ) : (
                  <ErrorHandler
                    errorCode={'404'}
                    showTryAgainButton={false}
                    errorMsg={`We are unable to find any matching ${title.toLowerCase()} for your search`}
                  />
                )}
              </div>
            ) : (
              <ProgressBar type={loaderType} background={`#0000002F`} color={themeColors.backgroundSecondary} />
            )}
          </Row>
          {showModal ? (
            <ActionForm
              setModal={showModal}
              modalConfig={modalConfig}
              themeColors={themeColors}
              onCancelClick={() => onCancelClick()}
              onSubmitClick={(data: any) => onSubmitClick(data)}
            />
          ) : (
            ''
          )}
        </div>
      ) : (
        //Mobile view starts here
        <section className={`${styles.ordersAccordianSection}`}>
          <div className={`${styles.topNav}`}>
            <h1>{title}</h1>
            <button className={`${styles.addNewBtn}`} onClick={() => showCreateView()}>{`+ New`}</button>
          </div>

          <p className={`${styles.tagLine}`}>
            <img src="" alt="" /> {helpText}
          </p>
          <div>
            <div>
              <Form>
                <Row className="mt-4">
                  {isSearchEnable && dataColumns?.length > 0 ? (
                    <Col>
                      <Search
                        dataColumns={dataColumns}
                        key={'search-component'}
                        handleSearchChangeFor={(queryParams: string) => filterOrders(queryParams)}
                      />
                    </Col>
                  ) : (
                    ''
                  )}
                  {isFilterEnable ? (
                    <Col>
                      <Icon
                        iconName={'FaFilter'}
                        iconColor={themeColors.textSecondary}
                        label={'Filters'}
                        fontColor={themeColors.textSecondary}
                      />
                    </Col>
                  ) : (
                    ''
                  )}
                </Row>
              </Form>
            </div>
            {orders && (
              <OrdersAccordian
                data={orders}
                columns={dataColumns}
                themeColors={themeColors}
                moduleConfigurations={moduleConfigurations}
                callback={callback}
                showOrderDetailView={showOrderDetailView}
              />
            )}
            </div>
            
            {/* @ts-ignore */}
          <Pagination
            activePage={activePage}
            itemsCountPerPage={limit}
            totalItemsCount={apiResponse?.total || 1}
            pageRangeDisplayed={5}
            prevPageText={'Prev'}
            nextPageText={'Next'}
            onChange={handlePageChange}
            activeLinkClass={styles.paginationLinkActive}
            itemClass={`page-item`}
            linkClass={`page-link ${styles.paginationLink}`}
          />
        </section>
        //Mobile view ends here
      )}

      {showCreateOrderModal && (
        <CreateOrderForm
          setModal={showModal}
          modalConfig={modalConfig}
          themeColors={themeColors}
          apiConfigurations={apiConfigurations}
          user={location.state?.user?.result?.user}
          onCancelClick={() => onCreateOrderCancelClick()}
          onSubmitClick={(data: any) => onCreateOrderSubmitClick(data)}
        />
      )}

      {showAssignAgentModal && (
        <AssignAgent
          setModal={showAssignAgentModal}
          modalConfig={modalConfig}
          themeColors={themeColors}
          apiConfigurations={apiConfigurations}
          onCancelClick={() => onAssignAgentCancelClick()}
        />
      )}

      {showOrderDetailModal && (
        <OrderDetailView
          setModal={showOrderDetailModal}
          modalConfig={modalConfig}
          themeColors={themeColors}
          data={activeData}
          onCancelClick={() => onViewOrderDetailCancelClick()}
        />
      )}
    </>
  );
};
