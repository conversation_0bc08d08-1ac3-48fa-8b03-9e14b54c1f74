import React, { useEffect, useState } from 'react';
import styles from './OrderDetailView.module.css';
import { Button, Col, Modal, Table } from 'react-bootstrap';
import Dot from 'dot-object';
import { OrderViewTable } from '../OrderViewTable';
const dot = new Dot('.');

interface viewOrderDetailConfigInterface {
  title: string;
  config: {
    label: string;
    key: string;
  }[];
}

export interface OrderDetailViewProps {
  setModal: boolean;
  modalConfig: any;
  data: any;
  themeColors: any;
  onCancelClick: () => void;
  callback?: any;
  dataColumns: { [key: string]: any }[];
  viewOrderDetailConfig: viewOrderDetailConfigInterface;
}

export const OrderDetailView: React.FC<OrderDetailViewProps> = ({
  themeColors,
  data,
  modalConfig,
  callback,
  dataColumns,
  ...props
}) => {
  const attachments = data.attachments || [];
  const items = data?.cells || [];
  const [orderItems, setOrderItems] = useState<[]>(data.items || []);
  const orderDetils = data;
  function onCancelClick(e: any) {
    props.onCancelClick();
  }

  data = dot.dot(data);

  const RenderOrderDetails = ({ item, ...props }: Record<string, any>) => {
    return (
      <div className="w-100 mb-3">
        <div className="d-flex justify-content-between">
          <div className="w-50">
            <h6 className={`mb-0 d-inline-block ${styles.labelText}`}>{item.label || 'N/A'}</h6>
          </div>
          <div className={`text-break ${styles.showdataitems}`}>
            <p className={`mb-0 ${styles.labelValue}`}>{data[item.key] || 'N/A'}</p>
          </div>
        </div>
      </div>
    );
  };

  const RenderItemsListTable = ({ item, ...props }: Record<string, any>) => {
    return (
      <div className="w-100 mb-3">
        <div className="d-flex justify-content-between">
          <div>
            <h6 className={`mb-0 d-inline-block ${styles.labelText}`}>{item.label || 'N/A'}</h6>
            <p className={`mb-0 mt-3`}>
              <OrderViewTable
                orders={items}
                dataColumns={dataColumns}
                themeColors={themeColors}
                moduleConfigurations={[]}
                callback={callback}
              />
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Modal
      show={true}
      dialogClassName={`${styles.modalDialog}`}
      aria-labelledby="modal-styling-title"
      className={`${styles.modalRight}`}
      contentClassName={`${styles.modalContent}`}
      scrollable={true}
      animation={true}
    >
      <Col sm={12} className={`${styles.container}`}>
        <div className="modal-header">
          <h4 className={`modal-title ${styles.labelText}`}>{data.subject || modalConfig?.title || 'Order Detail'}</h4>
          <Button variant="outline-secondary" data-dismiss="modal" aria-label="Close" onClick={(e) => onCancelClick(e)}>
            <span aria-hidden="true" style={{ fontSize: 18 }}>
              &times;
            </span>
          </Button>
        </div>
        <div className={`${styles.cardBody} mt-3`}>
          {modalConfig.config?.map((item: any, index: any) => {
            return (
              <>
                {item.key === 'items' ? (
                  <RenderItemsListTable item={item} key={index} />
                ) : (
                  <RenderOrderDetails item={item} key={index} />
                )}
              </>
            );
          })}
        </div>
      </Col>
    </Modal>
  );
};
