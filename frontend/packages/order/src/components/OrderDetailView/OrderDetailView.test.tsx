import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { OrderDetailView } from './OrderDetailView';

const data = {
  setModal: true,
  modalConfig: [],
  data: [],
  themeColors: {},
  dataColumns: [],
  onCancelClick: () => new Function(),
  viewOrderDetailConfig: {
    title: '',
    config: [],
  },
};

describe('<OrderDetailView />', () => {
  test('it should mount', () => {
    render(<OrderDetailView {...data} />);

    const orderDetailView = screen.getByTestId('OrderDetailView');

    expect(orderDetailView).toBeInTheDocument();
  });
});
