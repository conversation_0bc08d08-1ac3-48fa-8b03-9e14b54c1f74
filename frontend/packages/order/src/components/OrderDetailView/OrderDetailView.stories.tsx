/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { OrderDetailView, OrderDetailViewProps } from './OrderDetailView';

export default {
  title: 'Components/OrderDetailView',
  component: OrderDetailView,
} as Meta;

const Template: Story<OrderDetailViewProps> = (args) => <OrderDetailView {...args} />;

export const ViewOrderDetailComponent = Template.bind({});
ViewOrderDetailComponent.args = {
  setModal: true,
  viewOrderDetailConfig: {
    title: 'View Order Detail',
    config: [
      {
        label: 'Order id',
        key: '_id',
      },
      {
        label: 'Order Date',
        key: 'startTime',
      },
      {
        label: 'Order Status',
        key: 'status',
      },
      {
        label: 'Total Price',
        key: 'totalDisplayAmount',
      },
      {
        label: 'Delivery Address',
        key: 'address.dropAddress.addressLine1',
      },
      {
        label: 'Payment Method',
        key: 'payment.method',
      },
      {
        label: 'Payment Status',
        key: 'payment.status',
      },
    ],
  },
  themeColors: {
    textSecondary: 'red',
  },
  data: {
    discount: {
      subType: 'default',
      type: 'default',
      percentage: 0,
      amount: 0,
    },
    tasksAcceptedBy: [],
    isActive: true,
    status: 'CONFIRM',
    _id: '61efafe8fa83960fe081cc14',
    userId: '61d51a7d9bf746d92153564c',
    userData: {
      _id: '61d51a7d9bf746d92153564c',
      createdAt: '2022-01-05T04:11:41.337Z',
      email: '<EMAIL>',
      familyName: 'Doe',
      givenName: 'John',
      name: 'John Doe',
      profilePicture: '',
      contactNumber: '1234567890',
      userId: '61d51a7d9bf746d92153564c',
      updatedAt: '2022-01-05T04:11:41.337Z',
    },
    payment: {
      status: 'DUE',
      _id: '61efafe8fa83960fe081cc16',
      payeeUserId: '61d51a7d9bf746d92153564c',
      createdAt: '2022-01-25T08:08:08.671Z',
      updatedAt: '2022-01-25T08:09:19.355Z',
      method: 'CARD',
      payeeCardId: 'pm_1KLhpfDfCzS5kElfeN3VBYgH',
    },
    tipDetails: [],
    items: [
      {
        quantity: 1,
        priceCurrency: 'USD',
        _id: '617a5fb2778aff544683aec3',
        title: 'Rayne Vigneau 1989 Sauternes ',
        price: 119.99,
        customData: {},
        iconUrl: '',
        name: 'Rayne Vigneau 1989 Sauternes ',
        createdAt: '2022-01-25T08:08:09.276Z',
        updatedAt: '2022-01-25T08:08:09.276Z',
        addonCost: [],
      },
    ],
    createdAt: '2022-01-25T08:08:08.672Z',
    updatedAt: '2022-01-25T08:09:19.392Z',
    __v: 0,
    address: {
      wayPoints: [],
      _id: '61efb02ffa83960fe081cc34',
      dropAddress: {
        latitude: 17.385044,
        longitude: 78.486671,
        addressLine1: 'Plot no 87, GS Estate, Adilabad, Telangana, India',
      },
      pickupAddress: {
        addressLine1: 'Tirupally,Tirupally,Adilabad',
        country: 'India',
        longitude: 78.545793,
        city: 'Adilabad',
        latitude: 19.69119,
        state: 'TG',
        pin: '504001',
      },
    },
    customerContactNumber: '9866622203',
    endTime: '2022-01-17T20:08:15.761Z',
    orderExecutionTime: 0,
    routes: {
      defaultRoute: null,
      otherPossibleRoutes: [],
    },
    startPoint: {
      type: 'Point',
      coordinates: [78.545793, 19.69119],
      _id: '61efb02ffa83960fe081cc33',
    },
    startTime: '2022-01-17T20:08:15.761Z',
    title: 'Taxi',
    actualTravelDurationInMin: null,
    amount: 119.99,
    displayAmount: '$119.99',
    expectedTravelDurationInMin: null,
    splitAmount: {
      tenant: 119.99,
    },
    travelDistance: null,
    ratingCompleted: false,
    totalAmount: 119.99,
    totalAgentAmount: 0,
    totalDisplayAmount: '$119.99',
    totalAgentDisplayAmount: '$0.00',
    additionalDetails: [],
  },
};
