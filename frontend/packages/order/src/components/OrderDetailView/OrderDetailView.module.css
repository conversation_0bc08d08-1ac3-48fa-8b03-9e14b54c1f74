.modalRight {
  right: 0;
  height: 100vh;
  min-width: 100%;
  width: unset !important;
  left: unset !important;
}

.modalContent {
  min-height: 100vh;
  border: unset !important;
  border-radius: unset !important;
}

.modalDialog {
  margin: unset !important;
  max-width: unset !important;
}

.container {
  max-width: 600px;
}

.cardBody {
  overflow: auto;
  height: 90vh;
  flex: 1 1 auto;
  background-color: white;
  padding: 1.25rem;
}

.d-flex {
  display: flex !important;
}

.labelText {
  color: var(--text-secondary, #3b8396);
}

.justify-content-between {
  justify-content: space-between !important;
  display: flex !important;
}

.imageSize {
  max-height: 480px;
  max-width: 530px;
}

.labelValue {
  color: #adb5bd !important;
}
.orderDetailsTable thead {
  background: #f4f5f7;
}
.orderDetailsTable thead tr th {
  font-size: 16px;
  border-right: 1px solid #e3e3e3;
}
.orderDetailsTable {
  border: 1px solid #e3e3e3;
}
.orderDetailsTable td {
  border-top: 0px;
  border-right: 1px solid #e3e3e3;
  font-size: 14px;
}
.orderDetailsTable tbody tr td:nth-child(3) {
  text-align: center;
}
.orderDetailsTable tr {
  border: 1px solid #e3e3e3;
}
.orderDetailsTotal td {
  font-weight: bold;
  font-size: 16px;
}
.showdataitems {
  width: 13rem;
}

@media only screen and (min-width: 992px) {
  .modalRight {
    min-width: 40% !important;
  }
}
