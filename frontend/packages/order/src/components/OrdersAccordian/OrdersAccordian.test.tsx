import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { OrdersAccordian } from './OrdersAccordian';

const data = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'matchUsers=true&type[$ne]=employee',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};

describe('<OrdersAccordian />', () => {
  test('it should mount', () => {
    render(<OrdersAccordian {...data} />);

    const ordersAccordian = screen.getByTestId('OrdersAccordian');

    expect(ordersAccordian).toBeInTheDocument();
  });
});
