.modalRight {
  right: 0;
  height: 100vh;
  min-width: 100%;
  width: 50% !important;
  left: unset !important;
}

.modalContent {
  min-height: 100vh;
  border: unset !important;
  border-radius: unset !important;
}

.modalDialog {
  margin: unset !important;
  max-width: unset !important;
}

.imageSize {
  width: 100%;
  height: 10rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.agent {
  padding: 0.5rem;
}

.checkIcon {
  float: right;
  margin-right: 0.5rem;
}

.alignItems {
  position: fixed;
  padding: 0.875rem 0.875rem;
  bottom: 0rem;
  width: 100%;
  background-color: #ffffff;
  font-size: 1rem;
}

.avatar {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  margin-right: 1rem;
}

.agentName {
  font-size: 1rem;
  font-weight: 600;
}

.otherDetails {
  font-size: 0.875rem;
}

.resetButton {
  padding: 0.5rem;
  background-color: var(--primary, #ffffff);
  color: var(--text-secondary, #3b8396);
  border-color: var(--bg-secondary, #2a5e6c);
}

.resetButton:hover {
  background-color: var(--bg-secondary, #2a5e6c);
  color: var(--text-primary, #ffffff);
  border-color: var(--bg-primary, #3b8396);
}

.checkboxPosition {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.userInfoContainer {
  padding: 0.5rem;
  cursor: pointer;
}

.infoGroup {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.button {
  margin-right: 1rem;
  font-size: 0.7rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.buttonCancel {
  margin-right: 1rem;
  font-size: 0.7rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  border: 1px solid var(--danger, #ff0000);
  color: var(--danger, #ff0000);
}

.readyToServe:before,
.online:before,
.offline:before .unknown:before {
  content: '\2022';
  padding-right: 15px;
  font-size: 2rem;
}

.online {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f2f2f2;
  color: var(--success, #28a745);
}

.readyToServe {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f2f2f2;
  color: var(--warning, #ffc107);
}

.offline {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f2f2f2;
  color: var(--danger, #ff0000);
}

.unknown {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f2f2f2;
  color: var(--info, #17a2b8);
}

@media only screen and (min-width: 992px) {
  .modalRight {
    min-width: 45% !important;
  }
  .imageSize {
    width: 30rem;
  }
  .alignItems {
    width: 50%;
  }
}

.status {
  font-weight: 500;
  padding: 1rem;
  background-color: #c5c5c5;
  font-size: 1.2rem;
}

@media only screen and (max-width: 992px) {
  .agentName {
    font-size: 0.875rem;
    font-weight: 600;
  }

  .otherDetails {
    font-size: 0.675rem;
  }

  .button {
    margin-right: 0.875rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .buttonCancel {
    margin-right: 0.875rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
