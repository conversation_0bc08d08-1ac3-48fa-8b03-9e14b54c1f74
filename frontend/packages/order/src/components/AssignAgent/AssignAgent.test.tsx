import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { AssignAgent } from './AssignAgent';

const data = {
  setModal: true,
  modalConfig: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#3B8396',
    primary: '#FFFFFF',
  },
  apiConfigurations: {
    baseUrl: 'http://localhost:3104',
    userId: '60bef418a690497dc9f46499',
    token: '',
  },
  onCancelClick: () => new Function(),
};

describe('<AssignAgent />', () => {
  test('it should mount', () => {
    render(<AssignAgent {...data} />);

    const assignAgent = screen.getByTestId('AssignAgent');

    expect(assignAgent).toBeInTheDocument();
  });
});
