/* eslint-disable */
import React from 'react';
import { <PERSON>a, <PERSON> } from '@storybook/react';
import { AssignAgent, AssignAgentProps } from './AssignAgent';

export default {
  title: 'Components/AssignAgent',
  component: AssignAgent,
} as Meta;

const Template: Story<AssignAgentProps> = (args) => <AssignAgent {...args} />;

export const AssignAgentComponent = Template.bind({});
AssignAgentComponent.args = {
  modalConfig: {
    title: 'Assign Driver',
    fields: `name,familyName,givenName,email,contactNumber,profilePicture,presenceStatus,address`,
    orderId: '60d173067fb6710d3446de2e',
    selectedAgentsIds: ['61362700512be4c9898c9f33'],
    orderLocation: '72,18',
    defaultQueryParams: 'type=driver',
    defaultAccept: true,
    buttonLabel: 'Assign This Driver',
    tasks: {},
    noDataLabel: 'No Driver',
    staticData: {
      type: 'driver_ride',
    },
  },
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#3B8396',
    primary: '#FFFFFF',
  },
  apiConfigurations: {
    baseUrl: 'http://localhost:3104',
    userId: '60bef418a690497dc9f46499',
    token: '',
  },
};
