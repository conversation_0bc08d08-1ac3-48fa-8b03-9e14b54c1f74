import React, { useEffect, useState } from 'react';
import styles from './AssignAgent.module.css';
import Swal from 'sweetalert2';
import { <PERSON><PERSON>, Modal, Row, Col } from 'react-bootstrap';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PageHeader, ProgressBar } from '@frontend/core';
import axios, { AxiosRequestConfig } from 'axios';
import { size, groupBy, orderBy } from 'lodash';
import { ApiConfigurations, ThemeColors } from '..';
export interface AssignAgentProps {
  /**
   * The configurations required for API call
   */
  apiConfigurations: ApiConfigurations;
  /**
   * The configurations required for module
   */
  modalConfig: Record<string, any>;
  /**
   * The colors to be utilized for the component
   */
  themeColors: ThemeColors;
  /**
   * The type of loader to be used
   */
  loaderType?: any;
  /**
   * The cancel click function
   */
  onCancelClick: () => void;
}

export const AssignAgent: React.FC<AssignAgentProps> = ({
  themeColors,
  apiConfigurations,
  modalConfig,
  loaderType,
  ...props
}) => {
  loaderType = loaderType || 'Audio';

  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const [agents, setAgents] = useState<any>({});
  const [agentFetched, setAgentFetched] = useState(false);
  const [apiResponse, setApiResponse] = useState<any>({});
  const [selectedAgents, setSelectedAgents] = useState<Array<any>>([]);
  const agentStatus = modalConfig.statusConfigurations || {
    ONLINE: {
      key: 'ONLINE_SERVING',
      label: 'Online',
      selectionDisabled: false,
      class: 'online',
    },
    ONLINE_READY_FOR_SERVICE: {
      key: 'ONLINE_READY_FOR_SERVICE',
      label: 'Online ready for service',
      selectionDisabled: false,
      class: 'readyToServe',
    },
    OFFLINE: {
      key: 'OFFLINE',
      label: 'Offline',
      selectionDisabled: true,
      class: 'offline',
    },
    UNKNOWN: {
      key: 'UNKNOWN',
      selectionDisabled: false,
      label: 'Recently Joined',
      class: 'unknown',
    },
  };
  const taskStatus: any = {
    APPLY: 'Applied',
    ACCEPT: 'Accepted',
    ASSIGN: 'Assigned',
    DECLINE: 'Declined',
    CANCEL: 'Cancelled',
    AUTO_REJECT: 'Rejected',
    COMPLETED: 'Completed',
    FAIL: 'Failed',
    IN_PROGRESS: 'In-progress',
    OUT_FOR_DELIVERY: 'Out for delivery',
  };

  useEffect(() => {
    callGetUsersService();
  }, []);

  function callGetUsersService() {
    setAgentFetched(false);
    let defaultQuery = `?fields=${modalConfig.fields}`;
    if (size(modalConfig.defaultQueryParams) > 0) {
      defaultQuery += `&${modalConfig.defaultQueryParams}`;
    }
    if (modalConfig.searchSpecificCityOnly && size(modalConfig.pickupLocation?.city) > 0) {
      defaultQuery += `&address.city=${modalConfig.pickupLocation.city}`;
    }
    const url = `${apiConfigurations.baseUrl}/user/filter${defaultQuery}`;
    let requestConfig: AxiosRequestConfig = {
      method: 'GET',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
        location: modalConfig.orderLocation,
      },
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .get(url, requestConfig)
      .then((res: any) => {
        processGetUsersData(res);
      })
      .catch((err: any) => {
        processErrorOnGetUsersData(err);
      });
  }

  async function assignAgentTask(item: any) {
    try {
      const url = `${apiConfigurations.baseUrl}/order/${modalConfig.orderId}/task`;
      const data = {
        userId: item.id,
        status: 'ASSIGN',
        ...modalConfig.staticData,
      };
      let requestConfig: AxiosRequestConfig = {
        method: 'POST',
        url,
        headers: {
          'content-type': 'application/json',
          'x-sworks-timezone': userTimeZone,
          authorization: `Bearer ${apiConfigurations.token}`,
          userId: `${apiConfigurations.userId}`,
        },
        data,
      };
      requestConfig = JSON.parse(JSON.stringify(requestConfig));
      const res = await axios.post(url, data, requestConfig);

      processAssignAgentTask(res, item);
    } catch (err: any) {
      processErrorOnAssignAgentTask(err);
    }
  }

  function acceptAgentTask(order: any, item: any) {
    setAgentFetched(false);
    const url = `${apiConfigurations.baseUrl}/order/${order._id}/task`;
    const data = {
      userId: item.id,
      status: 'ACCEPT',
    };
    let requestConfig: AxiosRequestConfig = {
      method: 'PATCH',
      url,
      headers: {
        'content-type': 'application/json',
        'x-sworks-timezone': userTimeZone,
        authorization: `Bearer ${apiConfigurations.token}`,
        userId: `${apiConfigurations.userId}`,
      },
      data,
    };
    requestConfig = JSON.parse(JSON.stringify(requestConfig));
    axios
      .patch(url, data, requestConfig)
      .then((res: any) => {
        processAcceptAgentTask(res, item);
      })
      .catch((err: any) => {
        processErrorOnAcceptAgentTask(err);
      });
  }

  function processGetUsersData(res: any) {
    if (res.data?.result?.users) {
      setApiResponse(res.data.result);
      let users = res.data.result.users || [];
      users = orderBy(users, ['distanceFromLocation'], ['asc']);

      const groupedByStatus = groupBy(users, (user) => user.presenceStatus);

      setAgents(groupedByStatus);
      setAgentFetched(true);
    }
  }

  async function assignAgentsTask() {
    const promises: Promise<any>[] = [];
    const agents = selectedAgents.filter(
      (agent: any) => !modalConfig.selectedAgentsIds.find((item: any) => agent.id === item),
    );

    if (!agents.length) return;

    setAgentFetched(false);
    agents.forEach((item: any) => {
      promises.push(assignAgentTask(item));
    });
    await Promise.allSettled(promises).finally(() => setAgentFetched(true));
    setSelectedAgents([]);
    props.onCancelClick();
  }

  function processErrorOnGetUsersData(err: any) {}

  function processAssignAgentTask(res: any, item: any) {
    if (res.data?.result) {
      const order = res.data.result || {};
      if (modalConfig.defaultAccept) {
        acceptAgentTask(order, item);
      }
    }
  }

  function processErrorOnAssignAgentTask(err: any) {}

  function processAcceptAgentTask(res: any, item: any) {
    if (res.data?.result) {
      const order = res.data.result || {};
      setAgentFetched(true);
    }
  }

  function processErrorOnAcceptAgentTask(err: any) {}

  function onCancelClick(e: any) {
    props.onCancelClick();
  }

  function AgentsByStatus({ status, selectionDisabled }: any) {
    return (
      <>
        <div className={styles[status.class]}>{status.label}</div>
        {agents[status.key] && agents[status.key].length ? (
          agents[status.key].map((item: any, index: number) => (
            <RenderAssignAgent item={item} key={index} selectionDisabled={selectionDisabled} />
          ))
        ) : (
          <div className={`p-3`}>{modalConfig.noDataLabel || 'No Drivers'}</div>
        )}
      </>
    );
  }

  const isAgentAlreadyAssigned = (agentId: string) => {
    return !!modalConfig.selectedAgentsIds.find((id: any) => id === agentId);
  };

  const getTaskStatus = (userId: string) => {
    if (size(modalConfig.tasks) > 0) {
      const taskApplicants = Object.keys(modalConfig.tasks);
      for (const id of taskApplicants) {
        if (id === userId) {
          const status = modalConfig.tasks[id].status;
          return taskStatus[status] || 'N/A';
        }
      }
    }
    return null;
  };

  const RenderAssignAgent = ({ item, selectionDisabled, ...props }: Record<string, any>) => {
    const image = item.profilePicture || '';

    const selectAgent = (event: any, item: any) => {
      if (event.target.checked) {
        if (!selectedAgents.find((agent: any) => agent.id === item.id)) {
          setSelectedAgents([...selectedAgents, item]);
        }
      } else {
        setSelectedAgents(selectedAgents.filter((agent: any) => agent.id !== item.id));
      }
    };

    return (
      <div className={`ml-4`}>
        <label className={styles.userInfoContainer + ' row'} htmlFor={`agent_${item.id}`}>
          <Col xs={2}>
            <img className={styles.avatar} src={image} alt={image} />
          </Col>

          <Col className={styles.infoGroup}>
            <Row className={styles.agentName}>
              {item.familyName} {item.givenName}
            </Row>
            <Row className={styles.otherDetails}>
              {item.distanceFromLocation ? (
                <>
                  {item.distanceFromLocation?.toFixed(2) || 'N/A'} mile away, Last updated on {item.updatedAt || 'N/A'}
                </>
              ) : (
                ''
              )}
            </Row>
            <Row className={styles.otherDetails}>
              {size(getTaskStatus(item.id)) > 0 ? <>Status: {getTaskStatus(item.id)}</> : ''}
            </Row>
          </Col>

          <Col xs={1} className={styles.checkboxPosition}>
            <input
              className="form-check-input"
              type="checkbox"
              id={`agent_${item.id}`}
              onChange={(e) => selectAgent(e, item)}
              disabled={!!selectionDisabled || isAgentAlreadyAssigned(item.id)}
              checked={!!selectedAgents.find((agent: any) => agent.id === item.id) || isAgentAlreadyAssigned(item.id)}
            />
          </Col>
        </label>
      </div>
    );
  };

  function assignTaskConfirmation() {
    Swal.fire({
      title: 'Are you sure?',
      text: `Please confirm if you want to assign! User would get a notification for the assignment.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: themeColors.backgroundPrimary,
      cancelButtonColor: themeColors.backgroundSecondary,
      confirmButtonText: 'Yes',
    }).then((result: any) => {
      if (result.isConfirmed) {
        assignAgentsTask();
      }
    });
  }

  return (
    <Modal
      show={true}
      dialogClassName={styles.modalDialog}
      aria-labelledby="example-custom-modal-styling-title"
      className={styles.modalRight}
      contentClassName={styles.modalContent}
      scrollable={true}
      animation={false}
    >
      <Modal.Body className={`mb-5`}>
        <PageHeader title={modalConfig.title} fontColor={themeColors.textSecondary} />
        <div className="clearfix">
          {agentFetched ? (
            <div>
              {Object.keys(agents).length > 0 ? (
                <>
                  {agentStatus.ONLINE_READY_FOR_SERVICE ? (
                    <AgentsByStatus
                      status={agentStatus.ONLINE_READY_FOR_SERVICE}
                      selectionDisabled={
                        agentStatus.ONLINE_READY_FOR_SERVICE.hasOwnProperty('selectionDisabled')
                          ? agentStatus.ONLINE_READY_FOR_SERVICE.selectionDisabled
                          : false
                      }
                    />
                  ) : (
                    <></>
                  )}
                  {agentStatus.ONLINE_READY_FOR_SERVICE ? (
                    <AgentsByStatus
                      status={agentStatus.ONLINE}
                      selectionDisabled={
                        agentStatus.ONLINE.hasOwnProperty('selectionDisabled')
                          ? agentStatus.ONLINE.selectionDisabled
                          : true
                      }
                    />
                  ) : (
                    <></>
                  )}
                  {agentStatus.OFFLINE ? (
                    <AgentsByStatus
                      status={agentStatus.OFFLINE}
                      selectionDisabled={
                        agentStatus.OFFLINE.hasOwnProperty('selectionDisabled')
                          ? agentStatus.OFFLINE.selectionDisabled
                          : true
                      }
                    />
                  ) : (
                    <></>
                  )}
                  {agentStatus.UNKNOWN ? (
                    <AgentsByStatus
                      status={agentStatus.UNKNOWN}
                      selectionDisabled={
                        agentStatus.UNKNOWN.hasOwnProperty('selectionDisabled')
                          ? agentStatus.UNKNOWN.selectionDisabled
                          : true
                      }
                    />
                  ) : (
                    <></>
                  )}
                </>
              ) : (
                <ErrorHandler
                  errorCode={'404'}
                  showTryAgainButton={false}
                  errorMsg={`We are unable to find any agent for this task nearby`}
                />
              )}
            </div>
          ) : (
            <ProgressBar type={loaderType} background={`#0000002F`} color={themeColors.backgroundSecondary} />
          )}
          <Col className={styles.alignItems}>
            <Button
              className={styles.button}
              variant="dark"
              onClick={assignTaskConfirmation}
              disabled={!selectedAgents.length}
            >
              {modalConfig.buttonLabel}
            </Button>
            {/* <Button className={styles.button} variant="primary">
              AUTO ASSIGN
            </Button> */}
            <Button className={styles.buttonCancel} variant="light" onClick={onCancelClick}>
              DISMISS
            </Button>
          </Col>
        </div>
      </Modal.Body>
    </Modal>
  );
};
