import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { OrderTable } from './OrderTable';

const data = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: 'sort=_id',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};

describe('<OrderTable />', () => {
  test('it should mount', () => {
    render(<OrderTable {...data} />);

    const orderTable = screen.getByTestId('OrderTable');

    expect(orderTable).toBeInTheDocument();
  });
});
