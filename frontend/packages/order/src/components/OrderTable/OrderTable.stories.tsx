/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { OrderTable, OrderTableProps } from './OrderTable';

export default {
  title: 'Components/OrderTable',
  component: OrderTable,
} as Meta;

const Template: Story<OrderTableProps> = (args) => <OrderTable {...args} />;

export const OrderTableComponent = Template.bind({});
OrderTableComponent.args = {
  data: [],
  columns: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'sort=_id',
    actions: [
      {
        type: 'edit',
        label: 'EDIT',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};
