import React from 'react';
import { <PERSON>a, <PERSON> } from '@storybook/react';
import { OrderTabs, OrderTabsProps } from './OrderTabs';

export default {
  title: 'Components/ActionForm',
  component: OrderTabs,
} as Meta;

const Template: Story<OrderTabsProps> = (args) => <OrderTabs {...args} />;

export const ActionFormComponent = Template.bind({});
ActionFormComponent.args = {
  themeColors: {},
  onChangeTab: () => new Function(),
  serviceType: [],
};
