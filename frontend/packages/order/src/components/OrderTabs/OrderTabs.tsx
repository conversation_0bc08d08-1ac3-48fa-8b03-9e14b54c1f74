import React, { useEffect, useState } from 'react';
import styles from './OrderTabs.module.css';
export interface OrderTabsProps {
  themeColors: any;
  onChangeTab: (data: any) => void;
  serviceType: any[];
}

export const OrderTabs: React.FC<OrderTabsProps> = ({ themeColors, serviceType, ...props }) => {
  serviceType && serviceType.sort((a, b) => a.name.localeCompare(b.name));
  const defaultServiceType = serviceType[0];
  const [selectedServiceType, setSelectedServiceType] = useState();
  useEffect(() => {
    setSelectedServiceType(defaultServiceType?._id);
  }, [serviceType]);
  const changeTabs = (tab: any) => {
    setSelectedServiceType(tab?._id);
    props.onChangeTab(tab);
  };
  return (
    <div className="container-fluid">
      <div className={styles.orderTabs}>
        {serviceType.map((item: any, index: number) => {
          return (
            <div
              key={index}
              className={`orderTabs_item pe-4 pb-3 pt-4 ${
                item._id === selectedServiceType ? styles.underLine : styles.inActiveTabs
              }`}
              onClick={() => changeTabs(item)}
            >
              {item.name}
            </div>
          );
        })}
      </div>
    </div>
  );
};
