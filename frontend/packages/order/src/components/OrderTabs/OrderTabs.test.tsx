import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { OrderTabs } from './OrderTabs';

const data = {
  themeColors: {},
  onChangeTab: () => new Function(),
  serviceType: [],
};

describe('<OrderTabs />', () => {
  test('it should mount', () => {
    render(<OrderTabs {...data} />);

    const orderTabs = screen.getByTestId('OrderTabs');

    expect(orderTabs).toBeInTheDocument();
  });
});
