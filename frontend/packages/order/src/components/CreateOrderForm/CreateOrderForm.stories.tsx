import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import { CreateOrderForm, CreateOrderFormProps } from './CreateOrderForm';
import { MemoryRouter } from 'react-router-dom';

export default {
  title: 'Components/CreateOrderForm',
  component: CreateOrderForm,
  decorators: [
    (Story) => (
      // @ts-ignore
      <MemoryRouter>
        <Story />
      </MemoryRouter>
    ),
  ],
} as Meta;

const Template: Story<CreateOrderFormProps> = (args) => <CreateOrderForm {...args} />;

export const CreateOrderFormComponent = Template.bind({});
CreateOrderFormComponent.args = {
  setModal: true,
  modalConfig: {
    title: 'Create New Trip',
    submitLabel: 'CREATE TRIP',
    addUserFormEventKey: '/users',
    addUserFormSubEventKey: '/users',
    themeColors: {
      textAlternate: '#000000',
      textSecondary: '#2A5E6C',
      textPrimary: '#FFFFFF',
      backgroundSecondary: '#2A5E6C',
      backgroundPrimary: '#3B8396',
      primary: '#FFFFFF',
    },
    config: [
      {
        id: 'scheduledAtDate',
        label: 'Date',
        placeholder: 'Enter Scheduled Date',
        type: 'date',
        showTimeSelect: false,
        validationType: 'date',
        value: '',
        validations: [
          {
            type: 'required',
            params: ['Scheduled date is required'],
          },
        ],
      },
      {
        id: 'scheduledAtTime',
        label: 'Scheduled For',
        placeholder: 'Enter Scheduled Time',
        type: 'time',
        showTimeSelect: true,
        validationType: 'time',
        value: '',
        validations: [
          {
            type: 'required',
            params: ['Scheduled time is required'],
          },
        ],
      },
      {
        id: 'pickupAddress',
        label: 'Trip Details',
        placeholder: 'Enter Pickup Address',
        type: 'location',
        validationType: 'mixed',
        apiKey: 'AIzaSyA6XrzjQb2O0ImkkAVD8w1sA-t05e8__PE',
        validations: [
          {
            type: 'required',
            params: ['Pickup Address is required'],
          },
        ],
        searchable: true,
      },
      {
        id: 'dropAddress',
        placeholder: 'Enter Drop Address',
        type: 'location',
        validationType: 'mixed',
        validations: [
          {
            type: 'required',
            params: ['Drop Address is required'],
          },
        ],
        apiKey: 'AIzaSyA6XrzjQb2O0ImkkAVD8w1sA-t05e8__PE',
        searchable: true,
      },
    ],
    userSelectConfig: {
      name: 'name',
      label: 'Choose Customer',
    },
    apiConfigurations: {
      baseUrl: 'http://localhost:3104',
      userId: '62015862c8fd37299cf21e64',
      token: '',
    },
  },
  user: {
    _id: '61bc215154fa3bf4f7443afa',
    email: '<EMAIL>',
    contactNumber: '1234567890',
    countryCode: '+1',
    familyName: 'User 5',
    givenName: 'Test',
    isActive: true,
    name: 'Test User 5',
    password: '$2a$12$fvDFHGzryitFf/sZphVv1OmAZlb99jxZKLKAQWukTjl2pYmehfWfe',
    profileUrl: null,
    referralId: 'TES1052',
    roles: ['user'],
    settings: {
      notifications: {
        default: true,
      },
    },
    type: 'rider',
  },
};
