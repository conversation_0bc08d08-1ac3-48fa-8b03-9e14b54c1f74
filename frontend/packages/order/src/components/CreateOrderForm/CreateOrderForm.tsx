import React, { useState } from 'react';
import styles from './CreateOrderForm.module.css';
import { Modal } from 'react-bootstrap';
import { DynamicForm, PageHeader } from '@frontend/core';
import { ApiConfigurations, UserSelect } from '@frontend/user';
import { useHistory } from 'react-router-dom';

export interface CreateOrderFormProps {
  setModal: boolean;
  modalConfig: any;
  themeColors: any;
  apiConfigurations: ApiConfigurations;
  user: any;
  onCancelClick: () => void;
  onSubmitClick: (data: any) => void;
}

export const CreateOrderForm: React.FC<CreateOrderFormProps> = ({ themeColors, apiConfigurations, user, ...props }) => {
  const history = useHistory();

  function onCancelClick(e: any) {
    props.onCancelClick();
  }

  function onSubmitClick(data: any) {
    props.onSubmitClick({ ...data, customer: customer?.value });
  }
  const [customer, setCustomer] = useState<any>(user ? { value: user } : null);

  return (
    <Modal
      show={true}
      dialogClassName={styles.modalDialog}
      aria-labelledby="example-custom-modal-styling-title"
      className={styles.modalRight}
      contentClassName={styles.modalContent}
      scrollable={true}
      animation={false}
    >
      <Modal.Body>
        {props.modalConfig?.title ? (
          <PageHeader title={props.modalConfig.title} fontColor={themeColors?.backgroundSecondary} />
        ) : (
          ''
        )}

        <div className={`px-2 mt-4`}>
          <UserSelect
            name="customer"
            {...props.modalConfig.userSelectConfig}
            apiConfigurations={apiConfigurations}
            labelCss={{ color: themeColors?.backgroundSecondary || '#2A5E6C' }}
            handleChange={(customer: any) => setCustomer(customer)}
            value={user}
            newOptionHandler={() =>
              history.push(props.modalConfig?.addUserFormEventKey, {
                create: true,
                from: history.location.pathname,
                eventKey: props.modalConfig?.addUserFormEventKey,
                subEventKey: props.modalConfig?.addUserFormSubEventKey,
                returnEventKey: props.modalConfig?.addOrderFormEventKey,
                returnSubEventKey: props.modalConfig?.addOrderFormSubEventKey,
              })
            }
          />
        </div>
        <DynamicForm
          fields={props.modalConfig.config}
          submitLabel={props.modalConfig.submitLabel}
          colorTheme={{
            backgroundColor: themeColors?.primary || '#FFFFFF',
            formTitleColor: themeColors?.textSecondary || '#3B8396',
            fieldTitleColor: themeColors?.backgroundSecondary || '#2A5E6C',
            buttonPrimaryColor: themeColors?.backgroundPrimary || '#3B8396',
          }}
          onSubmit={(data: any) => onSubmitClick(data)}
          onReset={(e: any) => onCancelClick(e)}
        />
      </Modal.Body>
    </Modal>
  );
};
