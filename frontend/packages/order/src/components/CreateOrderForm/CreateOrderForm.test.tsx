import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { CreateOrderForm } from './CreateOrderForm';

const data = {
  setModal: true,
  modalConfig: [],
  themeColors: {},
  user: {},
  onCancelClick: () => new Function(),
  onSubmitClick: () => new Function(),
  apiConfigurations: {
    baseUrl: '',
    token: '',
    userId: '',
  },
};

describe('<CreateOrderForm />', () => {
  test('it should mount', () => {
    render(<CreateOrderForm {...data} />);

    const craeteOrderForm = screen.getByTestId('CreateOrderForm');

    expect(craeteOrderForm).toBeInTheDocument();
  });
});
