/* eslint-disable */
import React from 'react';
import { Meta, Story } from '@storybook/react';
import { OrderViewTable, OrderViewTableProps } from './OrderViewTable';

export default {
  title: 'Components/OrderViewTable',
  component: OrderViewTable,
} as Meta;

const Template: Story<OrderViewTableProps> = (args) => <OrderViewTable {...args} />;

export const OrderViewTableComponent = Template.bind({});
OrderViewTableComponent.args = {
  orders: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  moduleConfigurations: {
    defaultQueryParams: 'type=PRODUCT',
    actions: [
      {
        type: 'edit',
        label: 'Edit',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};
