import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { OrderViewTable } from './OrderViewTable';

const data = {
  orders: [],
  themeColors: {
    textAlternate: '#000000',
    textSecondary: '#3B8396',
    textPrimary: '#FFFFFF',
    backgroundSecondary: '#2A5E6C',
    backgroundPrimary: '#FFFFFF',
    primary: '#3B8396',
  },
  dataColumns: [],
  moduleConfigurations: {
    defaultQueryParams: 'type=PRODUCT',
    actions: [
      {
        type: 'edit',
        label: 'Edit',
      },
      {
        type: 'delete',
        label: 'DELETE',
      },
    ],
  },
};

describe('<OrderViewTable />', () => {
  test('it should mount', () => {
    render(<OrderViewTable {...data} />);

    const orderViewTable = screen.getByTestId('OrderViewTable');

    expect(orderViewTable).toBeInTheDocument();
  });
});
