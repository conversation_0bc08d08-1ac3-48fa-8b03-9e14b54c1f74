import React from 'react';
import styled from 'styled-components';
import { OrderTable } from '../OrderTable/OrderTable.lazy';
import { ThemeColors } from '../SharedExports';
const Styles = styled.div`
  // padding: 1rem;
  padding-top: 0rem;
  width: 100%;
  //Most important below 4 lines to avoid full screen scrolling
  ${'' /* These styles are suggested for the table fill all available space in its containing element */}
  display: block;
  ${'' /* These styles are required for a horizontaly scrollable table overflow */}
  overflow: auto;
  table {
    border-spacing: 0;
    border: 1px solid #f2f2f2;
    font-size: 14px;
    tr {
      border-bottom: 1px solid #f2f2f2;
      background-color: var(--primary, #ffffff);
      :last-child {
        td {
          border-bottom: 0;
        }
      }
      :hover {
        background-color: #f5f5f5;
      }
    }
    th {
      padding: 0.5rem;
      border-bottom: 1px solid #f2f2f2;
      text-transform: uppercase;
      background-color: #fafafa;
      :last-child {
        border-right: 0;
      }
    }
    ,
    td {
      margin: 0rem;
      padding: 6px;
      min-width: 11rem;
      text-transform: none;
      text-align: left;
      white-space: break-spaces;
      :last-child {
        border-right: 0;
      }
    }
  }
`;

export interface OrderViewTableProps {
  /**
   * The orders data
   */
  orders: any;
  /**
   * The data mapping columns
   */
  dataColumns: { [key: string]: any }[];
  /**
   * The colors to be utilized for the component
   */
  themeColors?: ThemeColors;
  /**
   * The configurations required for module
   */
  moduleConfigurations?: Record<string, any>;
  /**
   * The edit api callback call
   */
  callback?: any;

  showOrderDetailView?: any;
}

export const OrderViewTable: React.FC<OrderViewTableProps> = ({
  orders,
  dataColumns,
  themeColors,
  moduleConfigurations,
  callback,
  showOrderDetailView,
  ...props
}) => {
  const data = React.useMemo(() => orders, [orders]);
  const columns = React.useMemo(() => dataColumns, []);

  return (
    <Styles>
      {columns && (
        <OrderTable
          callback={callback}
          themeColors={themeColors}
          moduleConfigurations={moduleConfigurations}
          columns={columns}
          data={data}
          showOrderDetailView={showOrderDetailView}
        />
      )}
    </Styles>
  );
};
