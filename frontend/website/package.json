{"name": "website", "version": "0.0.0", "private": true, "scripts": {"analyze": "source-map-explorer 'build/static/js/*.js' --html report.html", "build": "npm install && react-scripts build", "build:development": "env-cmd -f .env.development react-scripts --max_old_space_size=8192 build", "build:prod": "env-cmd -f .env.production react-scripts build", "eject": "react-scripts eject", "format": "prettier --write \"src/**/*.js\"", "start": "react-scripts start", "start:development": "env-cmd -f .env.development react-scripts start", "start:prod": "env-cmd -f .env.production react-scripts start", "test": "react-scripts test"}, "dependencies": {"@babel/runtime": "^7.21.0", "@frontend/catalogue": "*", "@frontend/core": "*", "@frontend/notification": "*", "@frontend/order": "*", "@frontend/support": "*", "@frontend/user": "*", "@types/react": "^17.0.2", "axios": "^1.3.5", "body-scroll-lock": "^4.0.0-beta.0", "bootstrap": "^5.2.3", "css-vars": "^2.4.0", "device-detector-js": "^3.0.3", "env-cmd": "^10.1.0", "firebase": "^9.19.1", "lodash": "^4.17.21", "react": "^17.0.2", "react-bootstrap": "1.5.2", "react-dom": "^17.0.2", "react-hot-toast": "^2.4.1", "react-router-dom": "5.3.4", "react-scripts": "^5.0.1", "sweetalert2": "^11.7.3", "uuid": "^11.0.3", "web-vitals": "^2.1.4"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "autoprefixer": "^10.4.15", "generate-react-cli": "^8.3.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "postcss-cli": "^10.1.0", "postcss-loader": "^7.1.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.7", "sass": "^1.59.3", "source-map-explorer": "^2.5.3"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}