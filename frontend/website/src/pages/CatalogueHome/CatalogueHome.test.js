import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import CatalogueHome from './CatalogueHome';

describe('<CatalogueHome />', () => {
  test('it should mount', () => {
    render(<CatalogueHome />);

    const catalogueHome = screen.getByTestId('CatalogueHome');

    expect(catalogueHome).toBeInTheDocument();
  });
});
