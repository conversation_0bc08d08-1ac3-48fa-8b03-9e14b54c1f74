import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import EditUserProfile from './EditUserProfile';

describe('<EditUserProfile />', () => {
  test('it should mount', () => {
    render(<EditUserProfile />);

    const editUserProfile = screen.getByTestId('EditUserProfile');

    expect(editUserProfile).toBeInTheDocument();
  });
});
