// NPM modules
import React from 'react';
import { BrowserRouter, Route, Redirect, Switch } from 'react-router-dom';
import './App.module.scss';
import './bootstrap-custom.css';
// import './custom-css.scss';
import '@frontend/core/lib/index.css';
import '../../node_modules/react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import 'bootstrap/dist/css/bootstrap.css';
import '../../node_modules/react-loading-skeleton/dist/skeleton.css';
// import './react-draft-wysiwyg.css';
import 'react-datepicker/dist/react-datepicker.css';
import 'rc-time-picker/assets/index.css';
import 'react-image-gallery/styles/css/image-gallery.css';
// Constants file
import Constants from './shared/constants/AppConstants';
import PathConstants from './shared/constants/PathConstants';
// Components file
import Loader from './components/Loader/Loader.lazy';
// Pages file
import Login from './pages/Login/Login.lazy';
import CatalogueHome from './pages/CatalogueHome/CatalogueHome.lazy';
import DashboardHome from './pages/DashboardHome/DashboardHome.lazy';
import FeedbackHome from './pages/FeedbackHome/FeedbackHome.lazy';
import NotificationHome from './pages/NotificationHome/NotificationHome.lazy';
import OrderHome from './pages/OrderHome/OrderHome.lazy';
import EditUserProfile from './pages/Settings/EditUserProfile/EditUserProfile.lazy';
import SignOut from './pages/Settings/SignOut/SignOut.lazy';
import UserHome from './pages/UserHome/UserHome.lazy';
import { initGA } from '@frontend/core';
// Support modules
import ErrorBoundary from './ErrorBoundary';

function ProtectedRoute({ component: Component, ...rest }) {
  const isAuthenticated = localStorage.getItem(Constants.FIELD.IS_AUTHENTICATED);
  return (
    <Route
      {...rest}
      render={(props) => {
        if (isAuthenticated) {
          return <Component {...props} />;
        } else {
          return (
            <Redirect
              to={{
                pathname: `${PathConstants.LOGIN}`,
                state: { from: props.location },
              }}
            />
          );
        }
      }}
    />
  );
}

function App() {
  initGA(process.env.REACT_APP_GOOGLE_ANALYTICS_TRACKING_ID);
  return (
    <BrowserRouter>
      <React.Suspense fallback={<Loader />}>
        <ErrorBoundary>
          <Switch>
            <Route path={PathConstants.LOGIN} render={(props) => <Login {...props} />} />
            <Route exact path="/" render={(props) => <Login {...props} />} />
            <Route path={PathConstants.ERROR} />
            <ProtectedRoute path={PathConstants.HOME} key={PathConstants.HOME} component={DashboardHome} />

            <Route
              render={(props) => (
                <DashboardHome {...props}>
                  <Switch>
                    <Route path="/" exact component={DashboardHome} />
                    <Route path={PathConstants.CATALOGUES} key={window.location.pathname} component={CatalogueHome} />
                    <Route
                      path={PathConstants.SETTINGS_EDIT_PROFILE}
                      key={window.location.pathname}
                      component={EditUserProfile}
                    />
                    <Route path={PathConstants.FEEDBACKS} key={window.location.pathname} component={FeedbackHome} />
                    <Route
                      path={PathConstants.NOTIFICATIONS}
                      key={window.location.pathname}
                      component={NotificationHome}
                    />
                    <Route path={PathConstants.ORDERS} key={window.location.pathname} component={OrderHome} />
                    <Route path={PathConstants.SETTINGS_SIGNOUT} key={window.location.pathname} component={SignOut} />
                    <Route path={PathConstants.USERS} key={window.location.pathname} component={UserHome} />
                  </Switch>
                </DashboardHome>
              )}
            />
            <Route component={Login} />
          </Switch>
        </ErrorBoundary>
      </React.Suspense>
    </BrowserRouter>
  );
}

export default App;
