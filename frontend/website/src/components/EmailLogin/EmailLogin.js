// NPM modules
import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import axios from 'axios';
import { Button, Col, Form, Row } from 'react-bootstrap';
// Constants file
import Constants from '../../shared/constants/AppConstants';
import UrlConstants from '../../shared/constants/UrlConstants';
import PathConstants from '../../shared/constants/PathConstants';
// Support modules
import styles from './EmailLogin.module.scss';

const EmailLogin = (props) => {
  const history = useHistory();
  const API_HOST_URL = process.env.REACT_APP_API_HOST_URL;
  const userType = new URLSearchParams(window.location.search).get('userType')?.toLowerCase();
  const [emailId, setEmailId] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('Something went wrong at our end. Please try after some time.');

  function onFormSubmit(e) {
    e.preventDefault();
    callUserAuthenticationAPI();
  }

  function callUserAuthenticationAPI() {
    props.shouldShowLoader(true);
    const postData = {
      idToken: emailId,
      idTokenSecret: password,
      strategy: 'basic_email',
    };
    axios
      .post(`${API_HOST_URL}${UrlConstants.POST_USER_AUTHENTICATION}`, postData, {
        headers: { usertype: userType ? userType : 'admin' },
      })
      .then((res) => {
        processSuccessData(res);
      })
      .catch((err) => {
        processErrorData(err);
      });
    props.shouldShowLoader(false);
  }

  function callDashboardConfigAPI(dashboardId, token) {
    axios
      .get(`${API_HOST_URL}${UrlConstants.GET_DASHBOARD_CONFIG}/${dashboardId}`, {
        headers: { authorization: `Bearer ${token}` },
      })
      .then((res) => {
        processDashboardConfigData(res);
      })
      .catch((err) => {
        processErrorData(err);
      });
  }

  function callConfigAPI() {
    axios
      .get(`${API_HOST_URL}${UrlConstants.GET_CONFIG}`, {
        headers: {},
      })
      .then((res) => {
        processConfigData(res);
      })
      .catch((err) => {
        processErrorData(err);
      });
  }

  function processConfigData(res) {
    if (res.data?.result) {
      localStorage.setItem(Constants.FIELD.CONFIGURATIONS, JSON.stringify(res.data.result));
      props.setConfiguration(res.data.result);
      if (localStorage.getItem(Constants.FIELD.DASHBOARD_CONFIGURATIONS)) {
        history.push(PathConstants.HOME);
      }
    } else {
      setErrorMessage(`Configuration is not enabled for your user`);
      setError(true);
    }
  }

  function addDays(days) {
    const result = new Date();
    result.setDate(result.getDate() + days);
    return result;
  }

  function processDashboardConfigData(res) {
    if (res.data?.result) {
      localStorage.setItem(Constants.FIELD.DASHBOARD_CONFIGURATIONS, JSON.stringify(res.data.result));
      localStorage.setItem(Constants.FIELD.REFETCH_ON, addDays(1));
      if (localStorage.getItem(Constants.FIELD.CONFIGURATIONS)) {
        history.push(PathConstants.HOME);
      }
    } else {
      setErrorMessage(`Dashboard is not enabled for your user`);
      setError(true);
    }
  }

  function processSuccessData(res) {
    if (res && res.data && res.data?.status === 1 && res.data.result?.user) {
      const userRole = res.data.result.user.roles;
      const dashboardId = res.data.result.user.dashboardId;
      const isActive = res.data.result.user.isActive;
      const accessToken = res.data.result.accessToken;
      if (isActive && Array.isArray(userRole) && userRole.indexOf('admin') > -1 && dashboardId) {
        setError(false);
        localStorage.setItem(Constants.FIELD.USER, JSON.stringify(res.data.result.user));
        localStorage.setItem(Constants.FIELD.AUTH_TOKEN, res.data.result.accessToken);
        localStorage.setItem(Constants.FIELD.IS_AUTHENTICATED, 'true');
        callDashboardConfigAPI(dashboardId, accessToken);
        callConfigAPI();
      } else {
        setErrorMessage('You are not authorized to access this application');
        setError(true);
      }
    } else {
      processErrorData(res);
    }
  }

  function processErrorData(err) {
    if (err && err.data) {
      const error = err.data.error;
      if (error && error.errorMessage) {
        setErrorMessage(error.errorMessage);
      }
    }
    setError(true);
  }

  const LoginError = () => {
    return <div className={styles.loginError}>{errorMessage}</div>;
  };

  const SignInButton = () => {
    return (
      <div className="pt-3">
        <Button size="lg" block id="signInClick" type="submit" className={styles.signInButton}>
          SIGN IN
        </Button>
      </div>
    );
  };

  return (
    <div>
      <Form onSubmit={onFormSubmit}>
        <Row>
          <Col md={3} lg={2} xl={3}></Col>
          <Col md={6} lg={8} xl={6} className={styles.sideMargin}>
            <Form.Group controlId="formEmail">
              <Form.Label className={styles.headerLabel}>Email ID</Form.Label>
              <Form.Control
                type="email"
                placeholder="Email ID"
                onChange={(event) => setEmailId(event.target.value)}
                value={emailId}
                className="mb-0"
                autoComplete="email"
                required
              />
            </Form.Group>
            <Form.Group controlId="formPassword" className="pt-2">
              <Form.Label className={styles.headerLabel}>Password</Form.Label>
              <Form.Control
                type="password"
                placeholder="Password"
                autoComplete="password"
                className="mb-0"
                onChange={(event) => setPassword(event.target.value)}
                required
              />
            </Form.Group>
            {error ? <LoginError /> : ''}
            <SignInButton />
          </Col>
          <Col md={3} lg={2} xl={3}></Col>
        </Row>
      </Form>
    </div>
  );
};

EmailLogin.propTypes = {};

EmailLogin.defaultProps = {};

export default EmailLogin;
