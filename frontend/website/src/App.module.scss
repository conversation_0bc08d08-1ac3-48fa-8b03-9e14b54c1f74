@import './styles/themes.module.scss';
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');
$theme-colors: (
    "light":      #f4ced9,
    "dark":       #0f0a07,
    "primary":    #000000,
    "secondary":  #58544f,
    "info":       #da4d3e,
    "success":    #009b59,
    "warning":    #dfc900,
    "danger":     #ed1b00,
);
@font-face {
  font-family: 'poppins';
  src: url('./shared/assets/font/Poppins-Medium.ttf');
}

@font-face {
  font-family: 'poppins-italic';
  src: url('./shared/assets/font/Poppins-Italic.ttf');
}

@font-face {
  font-family: 'poppins-SemiBold';
  src: url('./shared/assets/font/Poppins-SemiBold.ttf');
}

@font-face {
  font-family: 'poppins-regular';
  src: url('./shared/assets/font/Poppins-Regular.ttf');
}

@font-face{
  font-family: 'SFProDisplay-Regular';
  src: url('./shared/assets/font/SFProDisplay-Regular.ttf');
}
@font-face{
  font-family: 'SFProDisplay-Bold';
  src: url('./shared/assets/font/SFProDisplay-Bold.ttf');
}

body {
  font-family: 'Poppins' !important;
  background-color: var(--primary);
  // color: var(--text-primary);
}

.App {
  text-align: center;
  display: flex;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 7px;
  height: 5px;
  position: absolute;
  
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent; 
}
 
/* Handle */
::-webkit-scrollbar-thumb {
  background: #a7a7a7; 
  border-radius: 0px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555; 
}

#sideMenuBar {

  ::-webkit-scrollbar {
    width: 5px !important;
    position: absolute;
    display: none;
    
  }
  
  /* Track */
   ::-webkit-scrollbar-track {
    background: #f1f1f1; 
  }
   
  /* Handle */
   ::-webkit-scrollbar-thumb {
    background: #888; 
    border-radius: 0px;
  }
  
  /* Handle on hover */
   ::-webkit-scrollbar-thumb:hover {
    background: #555; 
  }
}

