import axios from 'axios';
import { history } from '../../App.js';
import PathConstants from '../constants/PathConstants';
import Constants from '../constants/AppConstants';
import { <PERSON>rror<PERSON>andler } from '@frontend/core';

const instance = axios.create({
  baseURL: process.env.REACT_APP_API_HOST_URL,
  responseType: Constants.SYSTEM.VALUE_DEFAULT_RESPONSE_TYPE,
});

instance.interceptors.request.use(function (config) {
  if (localStorage.getItem(Constants.FIELD.AUTH_TOKEN)) {
    config.headers.Authorization = `Bearer ${localStorage.getItem(Constants.FIELD.AUTH_TOKEN)}`;
    if (localStorage.getItem(Constants.FIELD.USER)) {
      config.headers.userId = JSON.parse(localStorage.getItem(Constants.FIELD.USER))._id;
    }
    return config;
  }
});

instance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const errorStatusCode = error.status || (error.response ? error.response.status : 500);
    if (errorStatusCode === 401 && error.response.data.name === 'NotAuthenticated') {
      localStorage.clear();
      history.push(`${PathConstants.LOGIN}`);
    } else {
      <ErrorHandler errorCode={errorStatusCode} occupyFullScreen={true} showTryAgainButton={true} />;
      // history.push({
      //   pathname: PathConstants.ERROR,
      //   state: { errorCode: errorStatusCode },
      // });
    }
  },
);

export default instance;
