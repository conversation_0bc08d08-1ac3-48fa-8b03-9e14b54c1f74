// import { themeOptions } from '../../styles/ThemeOptions';

export function themeStyling(theme) {
  // const selectedTheme = themeOptions.find((theme) => theme.name.toLowerCase() === themeName.toLowerCase()) || {};
  const html = document.getElementsByTagName('html')[0];
  Object.keys(theme).forEach((property) => {
    if (property === 'name') {
      return;
    }
    html.style.setProperty(property, theme[property]);
  });
}
