# Cleveland Dashboard

A modern, modular React dashboard application built for the sworks ecosystem. This revamped smagic dashboard provides a centralized platform for managing various business modules including catalogues, orders, feedback, notifications, and user management.

## 🚀 Features

- **Modular Architecture**: Built with pluggable `@sworksio/dashboard-*` modules
- **Dynamic Configuration**: Theme, branding, and menu structure fetched from backend
- **Role-based Authentication**: Secure login with admin role requirements
- **Responsive Design**: Bootstrap-powered responsive UI
- **Real-time Notifications**: Firebase integration for live updates
- **Multi-environment Support**: Development and production configurations
- **Error Boundary**: Graceful error handling and recovery

### Dashboard Modules

- **Dashboard Home**: Main overview and analytics
- **Catalogue Management**: Product and service catalog administration
- **Order Management**: Order processing and tracking
- **Feedback System**: Customer feedback collection and management
- **Notifications**: Real-time notification center
- **User Management**: User profiles and administration
- **Settings**: Application configuration and user preferences

## 🛠️ Tech Stack

- **Frontend**: React 18, React Router, Bootstrap 5
- **State Management**: Local storage with dynamic configuration
- **HTTP Client**: Axios
- **Styling**: SCSS, CSS Variables, Bootstrap
- **Authentication**: Token-based authentication
- **Backend Integration**: Firebase, sworks APIs
- **Build Tool**: Create React App
- **Package Manager**: Yarn

## 📋 Prerequisites

- Node.js (v14 or higher)
- Yarn package manager
- GitHub personal access token (for sworks packages)

## 🔧 Setup Instructions

### 1. Generate GitHub Personal Access Token

1. Go to [GitHub Settings > Tokens](https://github.com/settings/tokens)
2. Generate a new personal access token with the following scopes:
   - `repo` (Full control of private repositories)
   - `write:packages` (Write packages to GitHub Package Registry)

### 2. Configure Environment

Add your personal access token to your shell configuration:

**For Bash (.bashrc or .bash_profile):**
```bash
export NODE_AUTH_TOKEN=your_personal_access_token_here
```

**For Zsh (.zshrc):**
```bash
export NODE_AUTH_TOKEN=your_personal_access_token_here
```

Restart your terminal or run `source ~/.bashrc` (or `source ~/.zshrc`) to apply changes.

### 3. Install Dependencies

```bash
# Install all dependencies
yarn install

# Or using npm
npm install
```

### 4. Environment Configuration

Create environment files for different deployment stages:

- `.env.development` - Development environment variables
- `.env.production` - Production environment variables

## 🚀 Available Scripts

### Development

```bash
# Start development server
yarn start

# Start with development environment
yarn start:development

# Start with production environment  
yarn start:prod
```

The app will open at [http://localhost:3000](http://localhost:3000) with hot reloading enabled.

### Building

```bash
# Build for production
yarn build

# Build with development environment
yarn build:development

# Build with production environment
yarn build:prod
```

### Testing

```bash
# Run tests in watch mode
yarn test
```

### Code Quality

```bash
# Format code with Prettier
yarn format

# Analyze bundle size
yarn analyze
```

## 🔐 Authentication

The application uses email-based authentication with the following requirements:

- Valid email and password
- User must have 'admin' role
- User must be associated with a dashboard ID
- Authentication token is stored in localStorage

### Login Flow

1. User enters email and password
2. Application fetches initial configurations
3. Credentials are validated against `/user/tokensignin` endpoint
4. Dashboard configurations are loaded based on user's dashboard ID
5. User is redirected to the main dashboard

## ⚙️ Configuration

The application supports dynamic configuration through:

- **Theme Configuration**: Colors, fonts, and styling
- **Dashboard Configuration**: Menu structure and module availability
- **API Configuration**: Backend endpoint URLs
- **Branding Configuration**: Logo, title, and landing page images

Configurations are fetched from the backend and cached in localStorage.

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── EmailLogin/     # Email authentication component
│   ├── Loader/         # Loading indicator
│   └── SideMenuBar/    # Navigation sidebar
├── pages/              # Page components
│   ├── AppConfig/      # Application configuration
│   ├── DashboardHome/  # Main dashboard layout
│   ├── Login/          # Login page
│   ├── Settings/       # User settings
│   └── [Modules]/      # Various dashboard modules
├── shared/             # Shared utilities and constants
│   ├── assets/         # Static assets
│   ├── constants/      # Application constants
│   ├── providers/      # Service providers and utilities
│   └── styles/         # Global styles and themes
└── App.js              # Main application component
```

## 🔧 Development

### Adding New Modules

1. Create a new page component in `src/pages/`
2. Add routing configuration in `App.js`
3. Update navigation in `SideMenuBar.js`
4. Install corresponding `@sworksio/dashboard-*` package if needed

### Styling

- Global styles: `src/styles/`
- Component-specific styles: `ComponentName.module.scss`
- Theme variables: `src/styles/themes.module.scss`

### API Integration

- HTTP client configuration: `src/shared/providers/AxiosConfig.js`
- API endpoints: `src/shared/constants/UrlConstants.js`
- Utility functions: `src/shared/providers/Utils.js`

## 🚀 Deployment

### Production Build

```bash
yarn build:prod
```

The build artifacts will be stored in the `build/` directory, ready for deployment to any static hosting service.

### Environment Variables

Ensure the following environment variables are configured for production:

- API endpoints
- Firebase configuration
- Google Analytics tracking ID
- Authentication service URLs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is private and proprietary to sworks.

## 🆘 Troubleshooting

### Common Issues

**Package Installation Fails**
- Ensure `NODE_AUTH_TOKEN` is properly set
- Verify GitHub token has correct permissions
- Try clearing npm/yarn cache

**Build Fails**
- Check Node.js version compatibility
- Ensure all environment variables are set
- Clear `node_modules` and reinstall

**Authentication Issues**
- Verify API endpoints are accessible
- Check user role and dashboard ID assignment
- Ensure authentication token is valid

## 📞 Support

For technical support or questions, please contact the sworks development team.
